import{r as a,af as O,a4 as D,aA as Ne,aB as je,aC as De,aD as Ae,aE as B,j as b,t as R,aF as Me,aG as $e,B as Fe,aH as Ke,x as Le}from"./index-G17GlXLb.js";import{S as Oe}from"./search-DYwk6_-u.js";import{C as Pe}from"./chevron-down-BIMAYvOi.js";var fe=1,qe=.9,Be=.8,Ve=.17,ee=.1,te=.999,Ge=.9999,Ue=.99,ze=/[\\\/_+.#"@\[\(\{&]/,He=/[\\\/_+.#"@\[\(\{&]/g,Xe=/[\s-]/,be=/[\s-]/g;function le(e,l,t,m,u,i,c){if(i===l.length)return u===e.length?fe:Ue;var d=`${u},${i}`;if(c[d]!==void 0)return c[d];for(var p=m.charAt(i),n=t.indexOf(p,u),h=0,g,I,k,N;n>=0;)g=le(e,l,t,m,n+1,i+1,c),g>h&&(n===u?g*=fe:ze.test(e.charAt(n-1))?(g*=Be,k=e.slice(u,n-1).match(He),k&&u>0&&(g*=Math.pow(te,k.length))):Xe.test(e.charAt(n-1))?(g*=qe,N=e.slice(u,n-1).match(be),N&&u>0&&(g*=Math.pow(te,N.length))):(g*=Ve,u>0&&(g*=Math.pow(te,n-u))),e.charAt(n)!==l.charAt(i)&&(g*=Ge)),(g<ee&&t.charAt(n-1)===m.charAt(i+1)||m.charAt(i+1)===m.charAt(i)&&t.charAt(n-1)!==m.charAt(i))&&(I=le(e,l,t,m,n+1,i+2,c),I*ee>g&&(g=I*ee)),g>h&&(h=g),n=t.indexOf(p,n+1);return c[d]=h,h}function pe(e){return e.toLowerCase().replace(be," ")}function Te(e,l,t){return e=t&&t.length>0?`${e+" "+t.join(" ")}`:e,le(e,l,pe(e),pe(l),0,0,{})}var q='[cmdk-group=""]',re='[cmdk-group-items=""]',We='[cmdk-group-heading=""]',he='[cmdk-item=""]',ve=`${he}:not([aria-disabled="true"])`,ae="cmdk-item-select",K="data-value",Je=(e,l,t)=>Te(e,l,t),ge=a.createContext(void 0),V=()=>a.useContext(ge),xe=a.createContext(void 0),ne=()=>a.useContext(xe),ye=a.createContext(void 0),we=a.forwardRef((e,l)=>{let t=L(()=>{var r,s;return{search:"",value:(s=(r=e.value)!=null?r:e.defaultValue)!=null?s:"",selectedItemId:void 0,filtered:{count:0,items:new Map,groups:new Set}}}),m=L(()=>new Set),u=L(()=>new Map),i=L(()=>new Map),c=L(()=>new Set),d=ke(e),{label:p,children:n,value:h,onValueChange:g,filter:I,shouldFilter:k,loop:N,disablePointerSelection:H=!1,vimBindings:A=!0,...G}=e,X=O(),oe=O(),T=O(),M=a.useRef(null),y=ot();$(()=>{if(h!==void 0){let r=h.trim();t.current.value=r,E.emit()}},[h]),$(()=>{y(6,ie)},[]);let E=a.useMemo(()=>({subscribe:r=>(c.current.add(r),()=>c.current.delete(r)),snapshot:()=>t.current,setState:(r,s,f)=>{var o,v,x,S;if(!Object.is(t.current[r],s)){if(t.current[r]=s,r==="search")Q(),J(),y(1,Y);else if(r==="value"){if(document.activeElement.hasAttribute("cmdk-input")||document.activeElement.hasAttribute("cmdk-root")){let w=document.getElementById(T);w?w.focus():(o=document.getElementById(X))==null||o.focus()}if(y(7,()=>{var w;t.current.selectedItemId=(w=F())==null?void 0:w.id,E.emit()}),f||y(5,ie),((v=d.current)==null?void 0:v.value)!==void 0){let w=s??"";(S=(x=d.current).onValueChange)==null||S.call(x,w);return}}E.emit()}},emit:()=>{c.current.forEach(r=>r())}}),[]),W=a.useMemo(()=>({value:(r,s,f)=>{var o;s!==((o=i.current.get(r))==null?void 0:o.value)&&(i.current.set(r,{value:s,keywords:f}),t.current.filtered.items.set(r,ue(s,f)),y(2,()=>{J(),E.emit()}))},item:(r,s)=>(m.current.add(r),s&&(u.current.has(s)?u.current.get(s).add(r):u.current.set(s,new Set([r]))),y(3,()=>{Q(),J(),t.current.value||Y(),E.emit()}),()=>{i.current.delete(r),m.current.delete(r),t.current.filtered.items.delete(r);let f=F();y(4,()=>{Q(),(f==null?void 0:f.getAttribute("id"))===r&&Y(),E.emit()})}),group:r=>(u.current.has(r)||u.current.set(r,new Set),()=>{i.current.delete(r),u.current.delete(r)}),filter:()=>d.current.shouldFilter,label:p||e["aria-label"],getDisablePointerSelection:()=>d.current.disablePointerSelection,listId:X,inputId:T,labelId:oe,listInnerRef:M}),[]);function ue(r,s){var f,o;let v=(o=(f=d.current)==null?void 0:f.filter)!=null?o:Je;return r?v(r,t.current.search,s):0}function J(){if(!t.current.search||d.current.shouldFilter===!1)return;let r=t.current.filtered.items,s=[];t.current.filtered.groups.forEach(o=>{let v=u.current.get(o),x=0;v.forEach(S=>{let w=r.get(S);x=Math.max(w,x)}),s.push([o,x])});let f=M.current;P().sort((o,v)=>{var x,S;let w=o.getAttribute("id"),U=v.getAttribute("id");return((x=r.get(U))!=null?x:0)-((S=r.get(w))!=null?S:0)}).forEach(o=>{let v=o.closest(re);v?v.appendChild(o.parentElement===v?o:o.closest(`${re} > *`)):f.appendChild(o.parentElement===f?o:o.closest(`${re} > *`))}),s.sort((o,v)=>v[1]-o[1]).forEach(o=>{var v;let x=(v=M.current)==null?void 0:v.querySelector(`${q}[${K}="${encodeURIComponent(o[0])}"]`);x==null||x.parentElement.appendChild(x)})}function Y(){let r=P().find(f=>f.getAttribute("aria-disabled")!=="true"),s=r==null?void 0:r.getAttribute(K);E.setState("value",s||void 0)}function Q(){var r,s,f,o;if(!t.current.search||d.current.shouldFilter===!1){t.current.filtered.count=m.current.size;return}t.current.filtered.groups=new Set;let v=0;for(let x of m.current){let S=(s=(r=i.current.get(x))==null?void 0:r.value)!=null?s:"",w=(o=(f=i.current.get(x))==null?void 0:f.keywords)!=null?o:[],U=ue(S,w);t.current.filtered.items.set(x,U),U>0&&v++}for(let[x,S]of u.current)for(let w of S)if(t.current.filtered.items.get(w)>0){t.current.filtered.groups.add(x);break}t.current.filtered.count=v}function ie(){var r,s,f;let o=F();o&&(((r=o.parentElement)==null?void 0:r.firstChild)===o&&((f=(s=o.closest(q))==null?void 0:s.querySelector(We))==null||f.scrollIntoView({block:"nearest"})),o.scrollIntoView({block:"nearest"}))}function F(){var r;return(r=M.current)==null?void 0:r.querySelector(`${he}[aria-selected="true"]`)}function P(){var r;return Array.from(((r=M.current)==null?void 0:r.querySelectorAll(ve))||[])}function Z(r){let s=P()[r];s&&E.setState("value",s.getAttribute(K))}function _(r){var s;let f=F(),o=P(),v=o.findIndex(S=>S===f),x=o[v+r];(s=d.current)!=null&&s.loop&&(x=v+r<0?o[o.length-1]:v+r===o.length?o[0]:o[v+r]),x&&E.setState("value",x.getAttribute(K))}function se(r){let s=F(),f=s==null?void 0:s.closest(q),o;for(;f&&!o;)f=r>0?at(f,q):nt(f,q),o=f==null?void 0:f.querySelector(ve);o?E.setState("value",o.getAttribute(K)):_(r)}let de=()=>Z(P().length-1),ce=r=>{r.preventDefault(),r.metaKey?de():r.altKey?se(1):_(1)},me=r=>{r.preventDefault(),r.metaKey?Z(0):r.altKey?se(-1):_(-1)};return a.createElement(D.div,{ref:l,tabIndex:-1,...G,"cmdk-root":"",onKeyDown:r=>{var s;(s=G.onKeyDown)==null||s.call(G,r);let f=r.nativeEvent.isComposing||r.keyCode===229;if(!(r.defaultPrevented||f))switch(r.key){case"n":case"j":{A&&r.ctrlKey&&ce(r);break}case"ArrowDown":{ce(r);break}case"p":case"k":{A&&r.ctrlKey&&me(r);break}case"ArrowUp":{me(r);break}case"Home":{r.preventDefault(),Z(0);break}case"End":{r.preventDefault(),de();break}case"Enter":{r.preventDefault();let o=F();if(o){let v=new Event(ae);o.dispatchEvent(v)}}}}},a.createElement("label",{"cmdk-label":"",htmlFor:W.inputId,id:W.labelId,style:it},p),z(e,r=>a.createElement(xe.Provider,{value:E},a.createElement(ge.Provider,{value:W},r))))}),Ye=a.forwardRef((e,l)=>{var t,m;let u=O(),i=a.useRef(null),c=a.useContext(ye),d=V(),p=ke(e),n=(m=(t=p.current)==null?void 0:t.forceMount)!=null?m:c==null?void 0:c.forceMount;$(()=>{if(!n)return d.item(u,c==null?void 0:c.id)},[n]);let h=Ee(u,i,[e.value,e.children,i],e.keywords),g=ne(),I=j(y=>y.value&&y.value===h.current),k=j(y=>n||d.filter()===!1?!0:y.search?y.filtered.items.get(u)>0:!0);a.useEffect(()=>{let y=i.current;if(!(!y||e.disabled))return y.addEventListener(ae,N),()=>y.removeEventListener(ae,N)},[k,e.onSelect,e.disabled]);function N(){var y,E;H(),(E=(y=p.current).onSelect)==null||E.call(y,h.current)}function H(){g.setState("value",h.current,!0)}if(!k)return null;let{disabled:A,value:G,onSelect:X,forceMount:oe,keywords:T,...M}=e;return a.createElement(D.div,{ref:B(i,l),...M,id:u,"cmdk-item":"",role:"option","aria-disabled":!!A,"aria-selected":!!I,"data-disabled":!!A,"data-selected":!!I,onPointerMove:A||d.getDisablePointerSelection()?void 0:H,onClick:A?void 0:N},e.children)}),Qe=a.forwardRef((e,l)=>{let{heading:t,children:m,forceMount:u,...i}=e,c=O(),d=a.useRef(null),p=a.useRef(null),n=O(),h=V(),g=j(k=>u||h.filter()===!1?!0:k.search?k.filtered.groups.has(c):!0);$(()=>h.group(c),[]),Ee(c,d,[e.value,e.heading,p]);let I=a.useMemo(()=>({id:c,forceMount:u}),[u]);return a.createElement(D.div,{ref:B(d,l),...i,"cmdk-group":"",role:"presentation",hidden:g?void 0:!0},t&&a.createElement("div",{ref:p,"cmdk-group-heading":"","aria-hidden":!0,id:n},t),z(e,k=>a.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":t?n:void 0},a.createElement(ye.Provider,{value:I},k))))}),Ze=a.forwardRef((e,l)=>{let{alwaysRender:t,...m}=e,u=a.useRef(null),i=j(c=>!c.search);return!t&&!i?null:a.createElement(D.div,{ref:B(u,l),...m,"cmdk-separator":"",role:"separator"})}),_e=a.forwardRef((e,l)=>{let{onValueChange:t,...m}=e,u=e.value!=null,i=ne(),c=j(n=>n.search),d=j(n=>n.selectedItemId),p=V();return a.useEffect(()=>{e.value!=null&&i.setState("search",e.value)},[e.value]),a.createElement(D.input,{ref:l,...m,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":p.listId,"aria-labelledby":p.labelId,"aria-activedescendant":d,id:p.inputId,type:"text",value:u?e.value:c,onChange:n=>{u||i.setState("search",n.target.value),t==null||t(n.target.value)}})}),et=a.forwardRef((e,l)=>{let{children:t,label:m="Suggestions",...u}=e,i=a.useRef(null),c=a.useRef(null),d=j(n=>n.selectedItemId),p=V();return a.useEffect(()=>{if(c.current&&i.current){let n=c.current,h=i.current,g,I=new ResizeObserver(()=>{g=requestAnimationFrame(()=>{let k=n.offsetHeight;h.style.setProperty("--cmdk-list-height",k.toFixed(1)+"px")})});return I.observe(n),()=>{cancelAnimationFrame(g),I.unobserve(n)}}},[]),a.createElement(D.div,{ref:B(i,l),...u,"cmdk-list":"",role:"listbox",tabIndex:-1,"aria-activedescendant":d,"aria-label":m,id:p.listId},z(e,n=>a.createElement("div",{ref:B(c,p.listInnerRef),"cmdk-list-sizer":""},n)))}),tt=a.forwardRef((e,l)=>{let{open:t,onOpenChange:m,overlayClassName:u,contentClassName:i,container:c,...d}=e;return a.createElement(Ne,{open:t,onOpenChange:m},a.createElement(je,{container:c},a.createElement(De,{"cmdk-overlay":"",className:u}),a.createElement(Ae,{"aria-label":e.label,"cmdk-dialog":"",className:i},a.createElement(we,{ref:l,...d}))))}),rt=a.forwardRef((e,l)=>j(t=>t.filtered.count===0)?a.createElement(D.div,{ref:l,...e,"cmdk-empty":"",role:"presentation"}):null),lt=a.forwardRef((e,l)=>{let{progress:t,children:m,label:u="Loading...",...i}=e;return a.createElement(D.div,{ref:l,...i,"cmdk-loading":"",role:"progressbar","aria-valuenow":t,"aria-valuemin":0,"aria-valuemax":100,"aria-label":u},z(e,c=>a.createElement("div",{"aria-hidden":!0},c)))}),C=Object.assign(we,{List:et,Item:Ye,Input:_e,Group:Qe,Separator:Ze,Dialog:tt,Empty:rt,Loading:lt});function at(e,l){let t=e.nextElementSibling;for(;t;){if(t.matches(l))return t;t=t.nextElementSibling}}function nt(e,l){let t=e.previousElementSibling;for(;t;){if(t.matches(l))return t;t=t.previousElementSibling}}function ke(e){let l=a.useRef(e);return $(()=>{l.current=e}),l}var $=typeof window>"u"?a.useEffect:a.useLayoutEffect;function L(e){let l=a.useRef();return l.current===void 0&&(l.current=e()),l}function j(e){let l=ne(),t=()=>e(l.snapshot());return a.useSyncExternalStore(l.subscribe,t,t)}function Ee(e,l,t,m=[]){let u=a.useRef(),i=V();return $(()=>{var c;let d=(()=>{var n;for(let h of t){if(typeof h=="string")return h.trim();if(typeof h=="object"&&"current"in h)return h.current?(n=h.current.textContent)==null?void 0:n.trim():u.current}})(),p=m.map(n=>n.trim());i.value(e,d,p),(c=l.current)==null||c.setAttribute(K,d),u.current=d}),u}var ot=()=>{let[e,l]=a.useState(),t=L(()=>new Map);return $(()=>{t.current.forEach(m=>m()),t.current=new Map},[e]),(m,u)=>{t.current.set(m,u),l({})}};function ut(e){let l=e.type;return typeof l=="function"?l(e.props):"render"in l?l.render(e.props):e}function z({asChild:e,children:l},t){return e&&a.isValidElement(l)?a.cloneElement(ut(l),{ref:l.ref},t(l.props.children)):t(l)}var it={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"};const Ce=a.forwardRef(({className:e,...l},t)=>b.jsx(C,{ref:t,className:R("flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",e),...l}));Ce.displayName=C.displayName;const st=a.forwardRef(({className:e,...l},t)=>b.jsxs("div",{className:"flex items-center border-b px-3","cmdk-input-wrapper":"",children:[b.jsx(Oe,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),b.jsx(C.Input,{ref:t,className:R("flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",e),...l})]}));st.displayName=C.Input.displayName;const Se=a.forwardRef(({className:e,...l},t)=>b.jsx(C.List,{ref:t,className:R("max-h-[300px] overflow-y-auto overflow-x-hidden",e),...l}));Se.displayName=C.List.displayName;const dt=a.forwardRef((e,l)=>b.jsx(C.Empty,{ref:l,className:"py-6 text-center text-sm",...e}));dt.displayName=C.Empty.displayName;const Ie=a.forwardRef(({className:e,...l},t)=>b.jsx(C.Group,{ref:t,className:R("overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground",e),...l}));Ie.displayName=C.Group.displayName;const ct=a.forwardRef(({className:e,...l},t)=>b.jsx(C.Separator,{ref:t,className:R("-mx-1 h-px bg-border",e),...l}));ct.displayName=C.Separator.displayName;const Re=a.forwardRef(({className:e,...l},t)=>b.jsx(C.Item,{ref:t,className:R("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled=true]:pointer-events-none data-[selected='true']:bg-accent data-[selected=true]:text-accent-foreground data-[disabled=true]:opacity-50",e),...l}));Re.displayName=C.Item.displayName;const mt={USDC:{bg:"bg-blue-100",bgDark:"dark:bg-blue-900/30",text:"text-blue-700",textDark:"dark:text-blue-100",border:"border-blue-500",borderDark:"dark:border-blue-400"},IDRX:{bg:"bg-purple-100",bgDark:"dark:bg-purple-900/30",text:"text-purple-700",textDark:"dark:text-purple-100",border:"border-purple-500",borderDark:"dark:border-purple-400"}},bt=({tokens:e,selectedToken:l,onTokenChange:t,className:m,isLoading:u})=>{const[i,c]=a.useState(!1),d=a.useCallback(n=>mt[n]||{bg:"bg-gray-100",bgDark:"dark:bg-gray-800",text:"text-gray-700",textDark:"dark:text-gray-300",border:"border-gray-400",borderDark:"dark:border-gray-600"},[]),p=a.useMemo(()=>d(l.symbol),[l.symbol,d]);return b.jsx("div",{className:"relative",children:b.jsxs(Me,{open:i,onOpenChange:c,children:[b.jsx($e,{asChild:!0,children:b.jsxs(Fe,{variant:"outline","aria-expanded":i,className:R("w-full justify-between h-9 text-sm px-3",p.border,p.borderDark,p.text,p.textDark,m),disabled:u,children:[b.jsxs("div",{className:"flex items-center",children:[l.icon&&b.jsx("div",{className:R("mr-2 h-5 w-5 overflow-hidden rounded-full",p.bg,p.bgDark),children:b.jsx("img",{src:l.icon,alt:l.name,className:"h-full w-full object-cover"})}),b.jsx("span",{className:"font-medium",children:l.symbol})]}),b.jsx(Pe,{className:"ml-1 h-4 w-4 shrink-0 opacity-50"})]})}),b.jsx(Ke,{className:"w-[170px] p-0",children:b.jsx(Ce,{children:b.jsx(Se,{children:b.jsx(Ie,{children:e.map(n=>b.jsxs(Re,{onSelect:()=>{t(n),c(!1)},className:R("flex items-center py-2",d(n.symbol).bg,d(n.symbol).bgDark),children:[n.icon&&b.jsx("div",{className:R("mr-2 h-4 w-4 overflow-hidden rounded-full","bg-white",n.symbol==="USDC"&&"dark:bg-blue-800",n.symbol==="IDRX"&&"dark:bg-purple-800"),children:b.jsx("img",{src:n.icon,alt:n.name,className:"h-full w-full object-cover"})}),b.jsx("span",{className:R("flex-1 font-medium text-sm",d(n.symbol).text,d(n.symbol).textDark),children:n.symbol}),n.symbol===l.symbol&&b.jsx(Le,{className:R("ml-1 h-4 w-4",d(n.symbol).text,d(n.symbol).textDark)})]},n.symbol))})})})})]})})};export{bt as T};
