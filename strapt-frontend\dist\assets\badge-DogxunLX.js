import{j as n,t as a,X as o}from"./index-G17GlXLb.js";const s=o("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-500 text-white hover:bg-green-600",warning:"border-transparent bg-amber-500 text-white hover:bg-amber-600"}},defaultVariants:{variant:"default"}});function d({className:r,variant:e,...t}){return n.jsx("div",{className:a(s({variant:e}),r),...t})}export{d as B};
