import{k as r,l as n,j as e,B as o}from"./index-G17GlXLb.js";import{A as i}from"./arrow-left-D0Dlhjqr.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const c=r("Construction",[["rect",{x:"2",y:"6",width:"20",height:"8",rx:"1",key:"1estib"}],["path",{d:"M17 14v7",key:"7m2elx"}],["path",{d:"M7 14v7",key:"1cm7wv"}],["path",{d:"M17 3v3",key:"1v4jwn"}],["path",{d:"M7 3v3",key:"7o6guu"}],["path",{d:"M10 14 2.3 6.3",key:"1023jk"}],["path",{d:"m14 6 7.7 7.7",key:"1s8pl2"}],["path",{d:"m8 6 8 8",key:"hl96qh"}]]),h=({title:t="Feature Coming Soon",description:s="We're working hard to bring you this exciting new feature. Stay tuned!"})=>{const a=n();return e.jsxs("div",{className:"flex flex-col items-center justify-center min-h-[70vh] px-4 text-center",children:[e.jsx("div",{className:"rounded-full bg-primary/10 p-6 mb-6",children:e.jsx(c,{className:"h-12 w-12 text-primary"})}),e.jsx("h1",{className:"text-2xl font-bold mb-3",children:t}),e.jsx("p",{className:"text-muted-foreground mb-8 max-w-md",children:s}),e.jsxs(o,{onClick:()=>a(-1),children:[e.jsx(i,{className:"mr-2 h-4 w-4"}),"Go Back"]})]})};export{h as ComingSoon,h as default};
