import{k as S,r as d,u as z,j as e,n as w,o as y,p as C,q as k,s as b,am as A,an as $,ao as D,B as f,aI as E,ad as I,w as T,aw as F,i as P}from"./index-G17GlXLb.js";import{S as l}from"./skeleton-CLgd6C74.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _=S("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const U=S("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]),v=[{id:"lens",name:"Lens Protocol",handle:"@trustuser.lens",iconUrl:"",verified:!0,provider:"lens"},{id:"farcaster",name:"Farcaster",handle:"@trustuser",iconUrl:"",verified:!1,provider:"farcaster"}],N=[{id:"lens",name:"Lens Protocol",description:"Social media platform built on blockchain"},{id:"farcaster",name:"Farcaster",description:"Decentralized social network"},{id:"ceramic",name:"Ceramic Network",description:"Decentralized data network for Web3"},{id:"ens",name:"Ethereum Name Service",description:"Decentralized naming for wallets, websites, & more"}],L="strapt_identity";function O(){const[o,r]=d.useState([]),[g,x]=d.useState(!0),{address:t}=z();d.useEffect(()=>{(()=>{if(!t){r(v),x(!1);return}try{const n=`${L}_${t}`,i=localStorage.getItem(n);if(i){const s=JSON.parse(i);r(s)}else r(v)}catch(n){console.error("Error loading connected accounts:",n),r(v)}x(!1)})()},[t]);const h=d.useCallback(a=>{if(t)try{const n=`${L}_${t}`;localStorage.setItem(n,JSON.stringify(a))}catch(n){console.error("Error saving connected accounts:",n)}},[t]),j=d.useCallback(a=>{if(!t||o.some(c=>c.id===a))return;const i=N.find(c=>c.id===a);if(!i)return;const s={id:a,name:i.name,handle:`@${t.slice(2,8).toLowerCase()}${a==="lens"?".lens":""}`,iconUrl:i.iconUrl||"",verified:!1,provider:a,connectionDate:new Date().toISOString()};r(c=>{const m=[...c,s];return h(m),m})},[t,o,h]),p=d.useCallback(a=>{t&&r(n=>{const i=n.filter(s=>s.id!==a);return h(i),i})},[t,h]),u=d.useCallback(()=>{const a=o.map(n=>n.id);return N.filter(n=>!a.includes(n.id))},[o]);return{connectedAccounts:o,isLoading:g,connectAccount:j,disconnectAccount:p,getAvailableProviders:u,identityProviders:N}}const B=()=>{const{connectedAccounts:o,isLoading:r,connectAccount:g,disconnectAccount:x,getAvailableProviders:t,identityProviders:h}=O(),[j,p]=d.useState(null),{toast:u}=P(),a=async s=>{var c;p(s);try{await new Promise(m=>setTimeout(m,1500)),g(s),u({title:"Connection successful",description:`Connected to ${(c=h.find(m=>m.id===s))==null?void 0:c.name}`,duration:3e3})}catch{u({title:"Connection failed",description:"There was an error connecting to the provider",variant:"destructive",duration:3e3})}finally{p(null)}},n=s=>{x(s),u({title:"Account disconnected",description:"The account has been disconnected",duration:3e3})},i=s=>s.split(" ").map(c=>c[0]).join("").toUpperCase();return e.jsxs("div",{className:"space-y-4",children:[e.jsxs(w,{children:[e.jsxs(y,{children:[e.jsx(C,{className:"text-xl",children:"Connected Identities"}),e.jsx(k,{children:"Your Web3 identities connected to this account"})]}),e.jsx(b,{children:r?e.jsx("div",{className:"space-y-3",children:Array.from({length:2}).map((s,c)=>e.jsxs("div",{className:"flex items-center justify-between bg-secondary/30 rounded-lg p-3 animate-pulse",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(l,{className:"h-10 w-10 rounded-full"}),e.jsxs("div",{children:[e.jsx(l,{className:"h-4 w-24 mb-2"}),e.jsx(l,{className:"h-3 w-16"})]})]}),e.jsx(l,{className:"h-8 w-8 rounded-md"})]},`skeleton-identity-${c}-${Date.now()}`))}):o.length===0?e.jsx("p",{className:"text-center text-muted-foreground py-4",children:"No connected identities"}):e.jsx("div",{className:"space-y-3",children:o.map(s=>e.jsxs("div",{className:"flex items-center justify-between bg-secondary/30 rounded-lg p-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs(A,{children:[e.jsx($,{src:s.iconUrl}),e.jsx(D,{children:i(s.name)})]}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("p",{className:"font-medium",children:s.name}),s.verified&&e.jsx(_,{className:"h-4 w-4 text-green-500"})]}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s.handle})]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(f,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",title:"View profile",children:e.jsx(E,{className:"h-4 w-4"})}),e.jsx(f,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 text-red-500 hover:text-red-600 hover:bg-red-100",title:"Disconnect",onClick:()=>n(s.id),children:e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round","aria-hidden":"true",role:"img","aria-label":"Disconnect",children:[e.jsx("title",{children:"Disconnect"}),e.jsx("path",{d:"M18 6 6 18"}),e.jsx("path",{d:"m6 6 12 12"})]})})]})]},s.id))})})]}),e.jsxs(w,{children:[e.jsxs(y,{children:[e.jsx(C,{className:"text-xl",children:"Available Connections"}),e.jsx(k,{children:"Connect with more decentralized identity providers"})]}),e.jsx(b,{children:r?e.jsx("div",{className:"space-y-3",children:Array.from({length:2}).map((s,c)=>e.jsxs("div",{className:"flex items-center justify-between bg-secondary/30 rounded-lg p-3 animate-pulse",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(l,{className:"h-10 w-10 rounded-full"}),e.jsxs("div",{children:[e.jsx(l,{className:"h-4 w-24 mb-2"}),e.jsx(l,{className:"h-3 w-40"})]})]}),e.jsx(l,{className:"h-9 w-24 rounded-md"})]},`skeleton-provider-${c}-${Date.now()}`))}):e.jsxs("div",{className:"space-y-3",children:[t().map(s=>e.jsxs("div",{className:"flex items-center justify-between bg-secondary/30 rounded-lg p-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(A,{children:e.jsx(D,{children:i(s.name)})}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:s.name}),e.jsx("p",{className:"text-sm text-muted-foreground",children:s.description})]})]}),e.jsx(f,{variant:"outline",size:"sm",onClick:()=>a(s.id),disabled:j===s.id,children:j===s.id?e.jsxs(e.Fragment,{children:[e.jsx(I,{className:"h-4 w-4 mr-1 animate-spin"})," Connecting..."]}):e.jsxs(e.Fragment,{children:[e.jsx(U,{className:"h-4 w-4 mr-1"})," Connect"]})})]},s.id)),t().length===0&&e.jsx("p",{className:"text-center text-muted-foreground py-4",children:"No more providers available to connect"})]})}),e.jsx(T,{children:e.jsxs(f,{variant:"link",className:"mx-auto",children:["Learn more about decentralized identity ",e.jsx(F,{className:"h-4 w-4 ml-1"})]})})]})]})};export{B as default};
