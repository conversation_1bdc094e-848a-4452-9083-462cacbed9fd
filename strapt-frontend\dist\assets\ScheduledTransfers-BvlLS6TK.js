import{k as ot,j as s,r as p,t as J,aw as st,bG as Be,u as Pt,l as Ot,n as Tt,o as Wt,p as Ft,q as Yt,B,s as Et,v as Lt,w as It,D as Rt,d as Bt,e as At,f as Ht,g as qt,aF as Xt,aG as Vt,aH as Gt,h as Qt,ad as $t,i as Ut}from"./index-G17GlXLb.js";import{S as Ae}from"./separator-BLTLWNIf.js";import{I as He}from"./input-CWM2bTJL.js";import{L as ee}from"./label-aQpKQY_t.js";import{C as zt}from"./chevron-left-CRB4yH4j.js";import{C as qe,S as Xe,a as Ve,b as Ge,c as Qe,d as U}from"./select-uVdWjx2o.js";import{B as $e}from"./badge-DogxunLX.js";import{S as G}from"./skeleton-CLgd6C74.js";import{P as Jt}from"./plus-DMc7oH9p.js";import{C as Kt}from"./OptimizedProfile-BELwX6Fh.js";import"./index-B97upM1f.js";import"./chevron-down-BIMAYvOi.js";import"./switch-DAmDmXFQ.js";import"./tabs-CCtap3dh.js";import"./QRCode-DVz9xBTO.js";import"./qr-code-BnIF1LDH.js";import"./info-BYTODGZ9.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Zt=ot("Pen",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const en=ot("Trash",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}]]);function k(e){const t=Object.prototype.toString.call(e);return e instanceof Date||typeof e=="object"&&t==="[object Date]"?new e.constructor(+e):typeof e=="number"||t==="[object Number]"||typeof e=="string"||t==="[object String]"?new Date(e):new Date(NaN)}function W(e,t){return e instanceof Date?new e.constructor(t):new Date(t)}function S(e,t){const n=k(e);return isNaN(t)?W(e,NaN):(t&&n.setDate(n.getDate()+t),n)}function Y(e,t){const n=k(e);if(isNaN(t))return W(e,NaN);if(!t)return n;const a=n.getDate(),r=W(e,n.getTime());r.setMonth(n.getMonth()+t+1,0);const o=r.getDate();return a>=o?r:(n.setFullYear(r.getFullYear(),r.getMonth(),a),n)}const Ce=6048e5,tn=864e5;let nn={};function re(){return nn}function I(e,t){var l,c,u,d;const n=re(),a=(t==null?void 0:t.weekStartsOn)??((c=(l=t==null?void 0:t.locale)==null?void 0:l.options)==null?void 0:c.weekStartsOn)??n.weekStartsOn??((d=(u=n.locale)==null?void 0:u.options)==null?void 0:d.weekStartsOn)??0,r=k(e),o=r.getDay(),i=(o<a?7:0)+o-a;return r.setDate(r.getDate()-i),r.setHours(0,0,0,0),r}function $(e){return I(e,{weekStartsOn:1})}function it(e){const t=k(e),n=t.getFullYear(),a=W(e,0);a.setFullYear(n+1,0,4),a.setHours(0,0,0,0);const r=$(a),o=W(e,0);o.setFullYear(n,0,4),o.setHours(0,0,0,0);const i=$(o);return t.getTime()>=r.getTime()?n+1:t.getTime()>=i.getTime()?n:n-1}function K(e){const t=k(e);return t.setHours(0,0,0,0),t}function fe(e){const t=k(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),+e-+n}function L(e,t){const n=K(e),a=K(t),r=+n-fe(n),o=+a-fe(a);return Math.round((r-o)/tn)}function an(e){const t=it(e),n=W(e,0);return n.setFullYear(t,0,4),n.setHours(0,0,0,0),$(n)}function _e(e,t){const n=t*7;return S(e,n)}function rn(e,t){return Y(e,t*12)}function on(e){let t;return e.forEach(function(n){const a=k(n);(t===void 0||t<a||isNaN(Number(a)))&&(t=a)}),t||new Date(NaN)}function sn(e){let t;return e.forEach(n=>{const a=k(n);(!t||t>a||isNaN(+a))&&(t=a)}),t||new Date(NaN)}function P(e,t){const n=K(e),a=K(t);return+n==+a}function Se(e){return e instanceof Date||typeof e=="object"&&Object.prototype.toString.call(e)==="[object Date]"}function ln(e){if(!Se(e)&&typeof e!="number")return!1;const t=k(e);return!isNaN(Number(t))}function ae(e,t){const n=k(e),a=k(t),r=n.getFullYear()-a.getFullYear(),o=n.getMonth()-a.getMonth();return r*12+o}function cn(e,t,n){const a=I(e,n),r=I(t,n),o=+a-fe(a),i=+r-fe(r);return Math.round((o-i)/Ce)}function Pe(e){const t=k(e),n=t.getMonth();return t.setFullYear(t.getFullYear(),n+1,0),t.setHours(23,59,59,999),t}function O(e){const t=k(e);return t.setDate(1),t.setHours(0,0,0,0),t}function lt(e){const t=k(e),n=W(e,0);return n.setFullYear(t.getFullYear(),0,1),n.setHours(0,0,0,0),n}function Oe(e,t){var l,c,u,d;const n=re(),a=(t==null?void 0:t.weekStartsOn)??((c=(l=t==null?void 0:t.locale)==null?void 0:l.options)==null?void 0:c.weekStartsOn)??n.weekStartsOn??((d=(u=n.locale)==null?void 0:u.options)==null?void 0:d.weekStartsOn)??0,r=k(e),o=r.getDay(),i=(o<a?-7:0)+6-(o-a);return r.setDate(r.getDate()+i),r.setHours(23,59,59,999),r}function ct(e){return Oe(e,{weekStartsOn:1})}const un={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},dn=(e,t,n)=>{let a;const r=un[e];return typeof r=="string"?a=r:t===1?a=r.one:a=r.other.replace("{{count}}",t.toString()),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?"in "+a:a+" ago":a};function be(e){return(t={})=>{const n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}const fn={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},hn={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},mn={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},vn={date:be({formats:fn,defaultWidth:"full"}),time:be({formats:hn,defaultWidth:"full"}),dateTime:be({formats:mn,defaultWidth:"full"})},yn={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},gn=(e,t,n,a)=>yn[e];function te(e){return(t,n)=>{const a=n!=null&&n.context?String(n.context):"standalone";let r;if(a==="formatting"&&e.formattingValues){const i=e.defaultFormattingWidth||e.defaultWidth,l=n!=null&&n.width?String(n.width):i;r=e.formattingValues[l]||e.formattingValues[i]}else{const i=e.defaultWidth,l=n!=null&&n.width?String(n.width):e.defaultWidth;r=e.values[l]||e.values[i]}const o=e.argumentCallback?e.argumentCallback(t):t;return r[o]}}const bn={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},xn={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},wn={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},pn={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},Dn={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},kn={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},Nn=(e,t)=>{const n=Number(e),a=n%100;if(a>20||a<10)switch(a%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},Mn={ordinalNumber:Nn,era:te({values:bn,defaultWidth:"wide"}),quarter:te({values:xn,defaultWidth:"wide",argumentCallback:e=>e-1}),month:te({values:wn,defaultWidth:"wide"}),day:te({values:pn,defaultWidth:"wide"}),dayPeriod:te({values:Dn,defaultWidth:"wide",formattingValues:kn,defaultFormattingWidth:"wide"})};function ne(e){return(t,n={})=>{const a=n.width,r=a&&e.matchPatterns[a]||e.matchPatterns[e.defaultMatchWidth],o=t.match(r);if(!o)return null;const i=o[0],l=a&&e.parsePatterns[a]||e.parsePatterns[e.defaultParseWidth],c=Array.isArray(l)?jn(l,f=>f.test(i)):_n(l,f=>f.test(i));let u;u=e.valueCallback?e.valueCallback(c):c,u=n.valueCallback?n.valueCallback(u):u;const d=t.slice(i.length);return{value:u,rest:d}}}function _n(e,t){for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n}function jn(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n}function Cn(e){return(t,n={})=>{const a=t.match(e.matchPattern);if(!a)return null;const r=a[0],o=t.match(e.parsePattern);if(!o)return null;let i=e.valueCallback?e.valueCallback(o[0]):o[0];i=n.valueCallback?n.valueCallback(i):i;const l=t.slice(r.length);return{value:i,rest:l}}}const Sn=/^(\d+)(th|st|nd|rd)?/i,Pn=/\d+/i,On={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},Tn={any:[/^b/i,/^(a|c)/i]},Wn={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},Fn={any:[/1/i,/2/i,/3/i,/4/i]},Yn={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},En={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},Ln={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},In={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},Rn={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},Bn={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},An={ordinalNumber:Cn({matchPattern:Sn,parsePattern:Pn,valueCallback:e=>parseInt(e,10)}),era:ne({matchPatterns:On,defaultMatchWidth:"wide",parsePatterns:Tn,defaultParseWidth:"any"}),quarter:ne({matchPatterns:Wn,defaultMatchWidth:"wide",parsePatterns:Fn,defaultParseWidth:"any",valueCallback:e=>e+1}),month:ne({matchPatterns:Yn,defaultMatchWidth:"wide",parsePatterns:En,defaultParseWidth:"any"}),day:ne({matchPatterns:Ln,defaultMatchWidth:"wide",parsePatterns:In,defaultParseWidth:"any"}),dayPeriod:ne({matchPatterns:Rn,defaultMatchWidth:"any",parsePatterns:Bn,defaultParseWidth:"any"})},ut={code:"en-US",formatDistance:dn,formatLong:vn,formatRelative:gn,localize:Mn,match:An,options:{weekStartsOn:0,firstWeekContainsDate:1}};function Hn(e){const t=k(e);return L(t,lt(t))+1}function dt(e){const t=k(e),n=+$(t)-+an(t);return Math.round(n/Ce)+1}function ft(e,t){var d,f,h,m;const n=k(e),a=n.getFullYear(),r=re(),o=(t==null?void 0:t.firstWeekContainsDate)??((f=(d=t==null?void 0:t.locale)==null?void 0:d.options)==null?void 0:f.firstWeekContainsDate)??r.firstWeekContainsDate??((m=(h=r.locale)==null?void 0:h.options)==null?void 0:m.firstWeekContainsDate)??1,i=W(e,0);i.setFullYear(a+1,0,o),i.setHours(0,0,0,0);const l=I(i,t),c=W(e,0);c.setFullYear(a,0,o),c.setHours(0,0,0,0);const u=I(c,t);return n.getTime()>=l.getTime()?a+1:n.getTime()>=u.getTime()?a:a-1}function qn(e,t){var l,c,u,d;const n=re(),a=(t==null?void 0:t.firstWeekContainsDate)??((c=(l=t==null?void 0:t.locale)==null?void 0:l.options)==null?void 0:c.firstWeekContainsDate)??n.firstWeekContainsDate??((d=(u=n.locale)==null?void 0:u.options)==null?void 0:d.firstWeekContainsDate)??1,r=ft(e,t),o=W(e,0);return o.setFullYear(r,0,a),o.setHours(0,0,0,0),I(o,t)}function ht(e,t){const n=k(e),a=+I(n,t)-+qn(n,t);return Math.round(a/Ce)+1}function _(e,t){const n=e<0?"-":"",a=Math.abs(e).toString().padStart(t,"0");return n+a}const q={y(e,t){const n=e.getFullYear(),a=n>0?n:1-n;return _(t==="yy"?a%100:a,t.length)},M(e,t){const n=e.getMonth();return t==="M"?String(n+1):_(n+1,2)},d(e,t){return _(e.getDate(),t.length)},a(e,t){const n=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];case"aaaa":default:return n==="am"?"a.m.":"p.m."}},h(e,t){return _(e.getHours()%12||12,t.length)},H(e,t){return _(e.getHours(),t.length)},m(e,t){return _(e.getMinutes(),t.length)},s(e,t){return _(e.getSeconds(),t.length)},S(e,t){const n=t.length,a=e.getMilliseconds(),r=Math.trunc(a*Math.pow(10,n-3));return _(r,t.length)}},z={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},Ue={G:function(e,t,n){const a=e.getFullYear()>0?1:0;switch(t){case"G":case"GG":case"GGG":return n.era(a,{width:"abbreviated"});case"GGGGG":return n.era(a,{width:"narrow"});case"GGGG":default:return n.era(a,{width:"wide"})}},y:function(e,t,n){if(t==="yo"){const a=e.getFullYear(),r=a>0?a:1-a;return n.ordinalNumber(r,{unit:"year"})}return q.y(e,t)},Y:function(e,t,n,a){const r=ft(e,a),o=r>0?r:1-r;if(t==="YY"){const i=o%100;return _(i,2)}return t==="Yo"?n.ordinalNumber(o,{unit:"year"}):_(o,t.length)},R:function(e,t){const n=it(e);return _(n,t.length)},u:function(e,t){const n=e.getFullYear();return _(n,t.length)},Q:function(e,t,n){const a=Math.ceil((e.getMonth()+1)/3);switch(t){case"Q":return String(a);case"QQ":return _(a,2);case"Qo":return n.ordinalNumber(a,{unit:"quarter"});case"QQQ":return n.quarter(a,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(a,{width:"narrow",context:"formatting"});case"QQQQ":default:return n.quarter(a,{width:"wide",context:"formatting"})}},q:function(e,t,n){const a=Math.ceil((e.getMonth()+1)/3);switch(t){case"q":return String(a);case"qq":return _(a,2);case"qo":return n.ordinalNumber(a,{unit:"quarter"});case"qqq":return n.quarter(a,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(a,{width:"narrow",context:"standalone"});case"qqqq":default:return n.quarter(a,{width:"wide",context:"standalone"})}},M:function(e,t,n){const a=e.getMonth();switch(t){case"M":case"MM":return q.M(e,t);case"Mo":return n.ordinalNumber(a+1,{unit:"month"});case"MMM":return n.month(a,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(a,{width:"narrow",context:"formatting"});case"MMMM":default:return n.month(a,{width:"wide",context:"formatting"})}},L:function(e,t,n){const a=e.getMonth();switch(t){case"L":return String(a+1);case"LL":return _(a+1,2);case"Lo":return n.ordinalNumber(a+1,{unit:"month"});case"LLL":return n.month(a,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(a,{width:"narrow",context:"standalone"});case"LLLL":default:return n.month(a,{width:"wide",context:"standalone"})}},w:function(e,t,n,a){const r=ht(e,a);return t==="wo"?n.ordinalNumber(r,{unit:"week"}):_(r,t.length)},I:function(e,t,n){const a=dt(e);return t==="Io"?n.ordinalNumber(a,{unit:"week"}):_(a,t.length)},d:function(e,t,n){return t==="do"?n.ordinalNumber(e.getDate(),{unit:"date"}):q.d(e,t)},D:function(e,t,n){const a=Hn(e);return t==="Do"?n.ordinalNumber(a,{unit:"dayOfYear"}):_(a,t.length)},E:function(e,t,n){const a=e.getDay();switch(t){case"E":case"EE":case"EEE":return n.day(a,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(a,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(a,{width:"short",context:"formatting"});case"EEEE":default:return n.day(a,{width:"wide",context:"formatting"})}},e:function(e,t,n,a){const r=e.getDay(),o=(r-a.weekStartsOn+8)%7||7;switch(t){case"e":return String(o);case"ee":return _(o,2);case"eo":return n.ordinalNumber(o,{unit:"day"});case"eee":return n.day(r,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(r,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(r,{width:"short",context:"formatting"});case"eeee":default:return n.day(r,{width:"wide",context:"formatting"})}},c:function(e,t,n,a){const r=e.getDay(),o=(r-a.weekStartsOn+8)%7||7;switch(t){case"c":return String(o);case"cc":return _(o,t.length);case"co":return n.ordinalNumber(o,{unit:"day"});case"ccc":return n.day(r,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(r,{width:"narrow",context:"standalone"});case"cccccc":return n.day(r,{width:"short",context:"standalone"});case"cccc":default:return n.day(r,{width:"wide",context:"standalone"})}},i:function(e,t,n){const a=e.getDay(),r=a===0?7:a;switch(t){case"i":return String(r);case"ii":return _(r,t.length);case"io":return n.ordinalNumber(r,{unit:"day"});case"iii":return n.day(a,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(a,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(a,{width:"short",context:"formatting"});case"iiii":default:return n.day(a,{width:"wide",context:"formatting"})}},a:function(e,t,n){const r=e.getHours()/12>=1?"pm":"am";switch(t){case"a":case"aa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(r,{width:"narrow",context:"formatting"});case"aaaa":default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},b:function(e,t,n){const a=e.getHours();let r;switch(a===12?r=z.noon:a===0?r=z.midnight:r=a/12>=1?"pm":"am",t){case"b":case"bb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(r,{width:"narrow",context:"formatting"});case"bbbb":default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},B:function(e,t,n){const a=e.getHours();let r;switch(a>=17?r=z.evening:a>=12?r=z.afternoon:a>=4?r=z.morning:r=z.night,t){case"B":case"BB":case"BBB":return n.dayPeriod(r,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(r,{width:"narrow",context:"formatting"});case"BBBB":default:return n.dayPeriod(r,{width:"wide",context:"formatting"})}},h:function(e,t,n){if(t==="ho"){let a=e.getHours()%12;return a===0&&(a=12),n.ordinalNumber(a,{unit:"hour"})}return q.h(e,t)},H:function(e,t,n){return t==="Ho"?n.ordinalNumber(e.getHours(),{unit:"hour"}):q.H(e,t)},K:function(e,t,n){const a=e.getHours()%12;return t==="Ko"?n.ordinalNumber(a,{unit:"hour"}):_(a,t.length)},k:function(e,t,n){let a=e.getHours();return a===0&&(a=24),t==="ko"?n.ordinalNumber(a,{unit:"hour"}):_(a,t.length)},m:function(e,t,n){return t==="mo"?n.ordinalNumber(e.getMinutes(),{unit:"minute"}):q.m(e,t)},s:function(e,t,n){return t==="so"?n.ordinalNumber(e.getSeconds(),{unit:"second"}):q.s(e,t)},S:function(e,t){return q.S(e,t)},X:function(e,t,n){const a=e.getTimezoneOffset();if(a===0)return"Z";switch(t){case"X":return Je(a);case"XXXX":case"XX":return Q(a);case"XXXXX":case"XXX":default:return Q(a,":")}},x:function(e,t,n){const a=e.getTimezoneOffset();switch(t){case"x":return Je(a);case"xxxx":case"xx":return Q(a);case"xxxxx":case"xxx":default:return Q(a,":")}},O:function(e,t,n){const a=e.getTimezoneOffset();switch(t){case"O":case"OO":case"OOO":return"GMT"+ze(a,":");case"OOOO":default:return"GMT"+Q(a,":")}},z:function(e,t,n){const a=e.getTimezoneOffset();switch(t){case"z":case"zz":case"zzz":return"GMT"+ze(a,":");case"zzzz":default:return"GMT"+Q(a,":")}},t:function(e,t,n){const a=Math.trunc(e.getTime()/1e3);return _(a,t.length)},T:function(e,t,n){const a=e.getTime();return _(a,t.length)}};function ze(e,t=""){const n=e>0?"-":"+",a=Math.abs(e),r=Math.trunc(a/60),o=a%60;return o===0?n+String(r):n+String(r)+t+_(o,2)}function Je(e,t){return e%60===0?(e>0?"-":"+")+_(Math.abs(e)/60,2):Q(e,t)}function Q(e,t=""){const n=e>0?"-":"+",a=Math.abs(e),r=_(Math.trunc(a/60),2),o=_(a%60,2);return n+r+t+o}const Ke=(e,t)=>{switch(e){case"P":return t.date({width:"short"});case"PP":return t.date({width:"medium"});case"PPP":return t.date({width:"long"});case"PPPP":default:return t.date({width:"full"})}},mt=(e,t)=>{switch(e){case"p":return t.time({width:"short"});case"pp":return t.time({width:"medium"});case"ppp":return t.time({width:"long"});case"pppp":default:return t.time({width:"full"})}},Xn=(e,t)=>{const n=e.match(/(P+)(p+)?/)||[],a=n[1],r=n[2];if(!r)return Ke(e,t);let o;switch(a){case"P":o=t.dateTime({width:"short"});break;case"PP":o=t.dateTime({width:"medium"});break;case"PPP":o=t.dateTime({width:"long"});break;case"PPPP":default:o=t.dateTime({width:"full"});break}return o.replace("{{date}}",Ke(a,t)).replace("{{time}}",mt(r,t))},Vn={p:mt,P:Xn},Gn=/^D+$/,Qn=/^Y+$/,$n=["D","DD","YY","YYYY"];function Un(e){return Gn.test(e)}function zn(e){return Qn.test(e)}function Jn(e,t,n){const a=Kn(e,t,n);if(console.warn(a),$n.includes(e))throw new RangeError(a)}function Kn(e,t,n){const a=e[0]==="Y"?"years":"days of the month";return`Use \`${e.toLowerCase()}\` instead of \`${e}\` (in \`${t}\`) for formatting ${a} to the input \`${n}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const Zn=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,ea=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,ta=/^'([^]*?)'?$/,na=/''/g,aa=/[a-zA-Z]/;function H(e,t,n){var d,f,h,m,v,y,b,M;const a=re(),r=(n==null?void 0:n.locale)??a.locale??ut,o=(n==null?void 0:n.firstWeekContainsDate)??((f=(d=n==null?void 0:n.locale)==null?void 0:d.options)==null?void 0:f.firstWeekContainsDate)??a.firstWeekContainsDate??((m=(h=a.locale)==null?void 0:h.options)==null?void 0:m.firstWeekContainsDate)??1,i=(n==null?void 0:n.weekStartsOn)??((y=(v=n==null?void 0:n.locale)==null?void 0:v.options)==null?void 0:y.weekStartsOn)??a.weekStartsOn??((M=(b=a.locale)==null?void 0:b.options)==null?void 0:M.weekStartsOn)??0,l=k(e);if(!ln(l))throw new RangeError("Invalid time value");let c=t.match(ea).map(D=>{const C=D[0];if(C==="p"||C==="P"){const R=Vn[C];return R(D,r.formatLong)}return D}).join("").match(Zn).map(D=>{if(D==="''")return{isToken:!1,value:"'"};const C=D[0];if(C==="'")return{isToken:!1,value:ra(D)};if(Ue[C])return{isToken:!0,value:D};if(C.match(aa))throw new RangeError("Format string contains an unescaped latin alphabet character `"+C+"`");return{isToken:!1,value:D}});r.localize.preprocessor&&(c=r.localize.preprocessor(l,c));const u={firstWeekContainsDate:o,weekStartsOn:i,locale:r};return c.map(D=>{if(!D.isToken)return D.value;const C=D.value;(!(n!=null&&n.useAdditionalWeekYearTokens)&&zn(C)||!(n!=null&&n.useAdditionalDayOfYearTokens)&&Un(C))&&Jn(C,t,String(e));const R=Ue[C[0]];return R(l,C,r.localize,u)}).join("")}function ra(e){const t=e.match(ta);return t?t[1].replace(na,"'"):e}function oa(e){const t=k(e),n=t.getFullYear(),a=t.getMonth(),r=W(e,0);return r.setFullYear(n,a+1,0),r.setHours(0,0,0,0),r.getDate()}function sa(e){return Math.trunc(+k(e)/1e3)}function ia(e){const t=k(e),n=t.getMonth();return t.setFullYear(t.getFullYear(),n+1,0),t.setHours(0,0,0,0),t}function la(e,t){return cn(ia(e),O(e),t)+1}function je(e,t){const n=k(e),a=k(t);return n.getTime()>a.getTime()}function vt(e,t){const n=k(e),a=k(t);return+n<+a}function Te(e,t){const n=k(e),a=k(t);return n.getFullYear()===a.getFullYear()&&n.getMonth()===a.getMonth()}function ca(e,t){const n=k(e),a=k(t);return n.getFullYear()===a.getFullYear()}function xe(e,t){return S(e,-t)}function we(e,t){const n=k(e),a=n.getFullYear(),r=n.getDate(),o=W(e,0);o.setFullYear(a,t,15),o.setHours(0,0,0,0);const i=oa(o);return n.setMonth(t,Math.min(r,i)),n}function Ze(e,t){const n=k(e);return isNaN(+n)?W(e,NaN):(n.setFullYear(t),n)}var x=function(){return x=Object.assign||function(t){for(var n,a=1,r=arguments.length;a<r;a++){n=arguments[a];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},x.apply(this,arguments)};function ua(e,t){var n={};for(var a in e)Object.prototype.hasOwnProperty.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,a=Object.getOwnPropertySymbols(e);r<a.length;r++)t.indexOf(a[r])<0&&Object.prototype.propertyIsEnumerable.call(e,a[r])&&(n[a[r]]=e[a[r]]);return n}function yt(e,t,n){for(var a=0,r=t.length,o;a<r;a++)(o||!(a in t))&&(o||(o=Array.prototype.slice.call(t,0,a)),o[a]=t[a]);return e.concat(o||Array.prototype.slice.call(t))}function oe(e){return e.mode==="multiple"}function se(e){return e.mode==="range"}function me(e){return e.mode==="single"}var da={root:"rdp",multiple_months:"rdp-multiple_months",with_weeknumber:"rdp-with_weeknumber",vhidden:"rdp-vhidden",button_reset:"rdp-button_reset",button:"rdp-button",caption:"rdp-caption",caption_start:"rdp-caption_start",caption_end:"rdp-caption_end",caption_between:"rdp-caption_between",caption_label:"rdp-caption_label",caption_dropdowns:"rdp-caption_dropdowns",dropdown:"rdp-dropdown",dropdown_month:"rdp-dropdown_month",dropdown_year:"rdp-dropdown_year",dropdown_icon:"rdp-dropdown_icon",months:"rdp-months",month:"rdp-month",table:"rdp-table",tbody:"rdp-tbody",tfoot:"rdp-tfoot",head:"rdp-head",head_row:"rdp-head_row",head_cell:"rdp-head_cell",nav:"rdp-nav",nav_button:"rdp-nav_button",nav_button_previous:"rdp-nav_button_previous",nav_button_next:"rdp-nav_button_next",nav_icon:"rdp-nav_icon",row:"rdp-row",weeknumber:"rdp-weeknumber",cell:"rdp-cell",day:"rdp-day",day_today:"rdp-day_today",day_outside:"rdp-day_outside",day_selected:"rdp-day_selected",day_disabled:"rdp-day_disabled",day_hidden:"rdp-day_hidden",day_range_start:"rdp-day_range_start",day_range_end:"rdp-day_range_end",day_range_middle:"rdp-day_range_middle"};function fa(e,t){return H(e,"LLLL y",t)}function ha(e,t){return H(e,"d",t)}function ma(e,t){return H(e,"LLLL",t)}function va(e){return"".concat(e)}function ya(e,t){return H(e,"cccccc",t)}function ga(e,t){return H(e,"yyyy",t)}var ba=Object.freeze({__proto__:null,formatCaption:fa,formatDay:ha,formatMonthCaption:ma,formatWeekNumber:va,formatWeekdayName:ya,formatYearCaption:ga}),xa=function(e,t,n){return H(e,"do MMMM (EEEE)",n)},wa=function(){return"Month: "},pa=function(){return"Go to next month"},Da=function(){return"Go to previous month"},ka=function(e,t){return H(e,"cccc",t)},Na=function(e){return"Week n. ".concat(e)},Ma=function(){return"Year: "},_a=Object.freeze({__proto__:null,labelDay:xa,labelMonthDropdown:wa,labelNext:pa,labelPrevious:Da,labelWeekNumber:Na,labelWeekday:ka,labelYearDropdown:Ma});function ja(){var e="buttons",t=da,n=ut,a={},r={},o=1,i={},l=new Date;return{captionLayout:e,classNames:t,formatters:ba,labels:_a,locale:n,modifiersClassNames:a,modifiers:r,numberOfMonths:o,styles:i,today:l,mode:"default"}}function Ca(e){var t=e.fromYear,n=e.toYear,a=e.fromMonth,r=e.toMonth,o=e.fromDate,i=e.toDate;return a?o=O(a):t&&(o=new Date(t,0,1)),r?i=Pe(r):n&&(i=new Date(n,11,31)),{fromDate:o?K(o):void 0,toDate:i?K(i):void 0}}var gt=p.createContext(void 0);function Sa(e){var t,n=e.initialProps,a=ja(),r=Ca(n),o=r.fromDate,i=r.toDate,l=(t=n.captionLayout)!==null&&t!==void 0?t:a.captionLayout;l!=="buttons"&&(!o||!i)&&(l="buttons");var c;(me(n)||oe(n)||se(n))&&(c=n.onSelect);var u=x(x(x({},a),n),{captionLayout:l,classNames:x(x({},a.classNames),n.classNames),components:x({},n.components),formatters:x(x({},a.formatters),n.formatters),fromDate:o,labels:x(x({},a.labels),n.labels),mode:n.mode||a.mode,modifiers:x(x({},a.modifiers),n.modifiers),modifiersClassNames:x(x({},a.modifiersClassNames),n.modifiersClassNames),onSelect:c,styles:x(x({},a.styles),n.styles),toDate:i});return s.jsx(gt.Provider,{value:u,children:e.children})}function j(){var e=p.useContext(gt);if(!e)throw new Error("useDayPicker must be used within a DayPickerProvider.");return e}function bt(e){var t=j(),n=t.locale,a=t.classNames,r=t.styles,o=t.formatters.formatCaption;return s.jsx("div",{className:a.caption_label,style:r.caption_label,"aria-live":"polite",role:"presentation",id:e.id,children:o(e.displayMonth,{locale:n})})}function Pa(e){return s.jsx("svg",x({width:"8px",height:"8px",viewBox:"0 0 120 120","data-testid":"iconDropdown"},e,{children:s.jsx("path",{d:"M4.22182541,48.2218254 C8.44222828,44.0014225 15.2388494,43.9273804 19.5496459,47.9996989 L19.7781746,48.2218254 L60,88.443 L100.221825,48.2218254 C104.442228,44.0014225 111.238849,43.9273804 115.549646,47.9996989 L115.778175,48.2218254 C119.998577,52.4422283 120.07262,59.2388494 116.000301,63.5496459 L115.778175,63.7781746 L67.7781746,111.778175 C63.5577717,115.998577 56.7611506,116.07262 52.4503541,112.000301 L52.2218254,111.778175 L4.22182541,63.7781746 C-0.**********,59.4824074 -0.**********,52.5175926 4.22182541,48.2218254 Z",fill:"currentColor",fillRule:"nonzero"})}))}function xt(e){var t,n,a=e.onChange,r=e.value,o=e.children,i=e.caption,l=e.className,c=e.style,u=j(),d=(n=(t=u.components)===null||t===void 0?void 0:t.IconDropdown)!==null&&n!==void 0?n:Pa;return s.jsxs("div",{className:l,style:c,children:[s.jsx("span",{className:u.classNames.vhidden,children:e["aria-label"]}),s.jsx("select",{name:e.name,"aria-label":e["aria-label"],className:u.classNames.dropdown,style:u.styles.dropdown,value:r,onChange:a,children:o}),s.jsxs("div",{className:u.classNames.caption_label,style:u.styles.caption_label,"aria-hidden":"true",children:[i,s.jsx(d,{className:u.classNames.dropdown_icon,style:u.styles.dropdown_icon})]})]})}function Oa(e){var t,n=j(),a=n.fromDate,r=n.toDate,o=n.styles,i=n.locale,l=n.formatters.formatMonthCaption,c=n.classNames,u=n.components,d=n.labels.labelMonthDropdown;if(!a)return s.jsx(s.Fragment,{});if(!r)return s.jsx(s.Fragment,{});var f=[];if(ca(a,r))for(var h=O(a),m=a.getMonth();m<=r.getMonth();m++)f.push(we(h,m));else for(var h=O(new Date),m=0;m<=11;m++)f.push(we(h,m));var v=function(b){var M=Number(b.target.value),D=we(O(e.displayMonth),M);e.onChange(D)},y=(t=u==null?void 0:u.Dropdown)!==null&&t!==void 0?t:xt;return s.jsx(y,{name:"months","aria-label":d(),className:c.dropdown_month,style:o.dropdown_month,onChange:v,value:e.displayMonth.getMonth(),caption:l(e.displayMonth,{locale:i}),children:f.map(function(b){return s.jsx("option",{value:b.getMonth(),children:l(b,{locale:i})},b.getMonth())})})}function Ta(e){var t,n=e.displayMonth,a=j(),r=a.fromDate,o=a.toDate,i=a.locale,l=a.styles,c=a.classNames,u=a.components,d=a.formatters.formatYearCaption,f=a.labels.labelYearDropdown,h=[];if(!r)return s.jsx(s.Fragment,{});if(!o)return s.jsx(s.Fragment,{});for(var m=r.getFullYear(),v=o.getFullYear(),y=m;y<=v;y++)h.push(Ze(lt(new Date),y));var b=function(D){var C=Ze(O(n),Number(D.target.value));e.onChange(C)},M=(t=u==null?void 0:u.Dropdown)!==null&&t!==void 0?t:xt;return s.jsx(M,{name:"years","aria-label":f(),className:c.dropdown_year,style:l.dropdown_year,onChange:b,value:n.getFullYear(),caption:d(n,{locale:i}),children:h.map(function(D){return s.jsx("option",{value:D.getFullYear(),children:d(D,{locale:i})},D.getFullYear())})})}function Wa(e,t){var n=p.useState(e),a=n[0],r=n[1],o=t===void 0?a:t;return[o,r]}function Fa(e){var t=e.month,n=e.defaultMonth,a=e.today,r=t||n||a||new Date,o=e.toDate,i=e.fromDate,l=e.numberOfMonths,c=l===void 0?1:l;if(o&&ae(o,r)<0){var u=-1*(c-1);r=Y(o,u)}return i&&ae(r,i)<0&&(r=i),O(r)}function Ya(){var e=j(),t=Fa(e),n=Wa(t,e.month),a=n[0],r=n[1],o=function(i){var l;if(!e.disableNavigation){var c=O(i);r(c),(l=e.onMonthChange)===null||l===void 0||l.call(e,c)}};return[a,o]}function Ea(e,t){for(var n=t.reverseMonths,a=t.numberOfMonths,r=O(e),o=O(Y(r,a)),i=ae(o,r),l=[],c=0;c<i;c++){var u=Y(r,c);l.push(u)}return n&&(l=l.reverse()),l}function La(e,t){if(!t.disableNavigation){var n=t.toDate,a=t.pagedNavigation,r=t.numberOfMonths,o=r===void 0?1:r,i=a?o:1,l=O(e);if(!n)return Y(l,i);var c=ae(n,e);if(!(c<o))return Y(l,i)}}function Ia(e,t){if(!t.disableNavigation){var n=t.fromDate,a=t.pagedNavigation,r=t.numberOfMonths,o=r===void 0?1:r,i=a?o:1,l=O(e);if(!n)return Y(l,-i);var c=ae(l,n);if(!(c<=0))return Y(l,-i)}}var wt=p.createContext(void 0);function Ra(e){var t=j(),n=Ya(),a=n[0],r=n[1],o=Ea(a,t),i=La(a,t),l=Ia(a,t),c=function(f){return o.some(function(h){return Te(f,h)})},u=function(f,h){c(f)||(h&&vt(f,h)?r(Y(f,1+t.numberOfMonths*-1)):r(f))},d={currentMonth:a,displayMonths:o,goToMonth:r,goToDate:u,previousMonth:l,nextMonth:i,isDateDisplayed:c};return s.jsx(wt.Provider,{value:d,children:e.children})}function ie(){var e=p.useContext(wt);if(!e)throw new Error("useNavigation must be used within a NavigationProvider");return e}function et(e){var t,n=j(),a=n.classNames,r=n.styles,o=n.components,i=ie().goToMonth,l=function(d){i(Y(d,e.displayIndex?-e.displayIndex:0))},c=(t=o==null?void 0:o.CaptionLabel)!==null&&t!==void 0?t:bt,u=s.jsx(c,{id:e.id,displayMonth:e.displayMonth});return s.jsxs("div",{className:a.caption_dropdowns,style:r.caption_dropdowns,children:[s.jsx("div",{className:a.vhidden,children:u}),s.jsx(Oa,{onChange:l,displayMonth:e.displayMonth}),s.jsx(Ta,{onChange:l,displayMonth:e.displayMonth})]})}function Ba(e){return s.jsx("svg",x({width:"16px",height:"16px",viewBox:"0 0 120 120"},e,{children:s.jsx("path",{d:"M69.490332,3.34314575 C72.6145263,0.218951416 77.6798462,0.218951416 80.8040405,3.34314575 C83.8617626,6.40086786 83.9268205,11.3179931 80.9992143,14.4548388 L80.8040405,14.6568542 L35.461,60 L80.8040405,105.343146 C83.8617626,108.400868 83.9268205,113.317993 80.9992143,116.454839 L80.8040405,116.656854 C77.7463184,119.714576 72.8291931,119.779634 69.6923475,116.852028 L69.490332,116.656854 L18.490332,65.6568542 C15.4326099,62.5991321 15.367552,57.6820069 18.2951583,54.5451612 L18.490332,54.3431458 L69.490332,3.34314575 Z",fill:"currentColor",fillRule:"nonzero"})}))}function Aa(e){return s.jsx("svg",x({width:"16px",height:"16px",viewBox:"0 0 120 120"},e,{children:s.jsx("path",{d:"M49.8040405,3.34314575 C46.6798462,0.218951416 41.6145263,0.218951416 38.490332,3.34314575 C35.4326099,6.40086786 35.367552,11.3179931 38.2951583,14.4548388 L38.490332,14.6568542 L83.8333725,60 L38.490332,105.343146 C35.4326099,108.400868 35.367552,113.317993 38.2951583,116.454839 L38.490332,116.656854 C41.5480541,119.714576 46.4651794,119.779634 49.602025,116.852028 L49.8040405,116.656854 L100.804041,65.6568542 C103.861763,62.5991321 103.926821,57.6820069 100.999214,54.5451612 L100.804041,54.3431458 L49.8040405,3.34314575 Z",fill:"currentColor"})}))}var he=p.forwardRef(function(e,t){var n=j(),a=n.classNames,r=n.styles,o=[a.button_reset,a.button];e.className&&o.push(e.className);var i=o.join(" "),l=x(x({},r.button_reset),r.button);return e.style&&Object.assign(l,e.style),s.jsx("button",x({},e,{ref:t,type:"button",className:i,style:l}))});function Ha(e){var t,n,a=j(),r=a.dir,o=a.locale,i=a.classNames,l=a.styles,c=a.labels,u=c.labelPrevious,d=c.labelNext,f=a.components;if(!e.nextMonth&&!e.previousMonth)return s.jsx(s.Fragment,{});var h=u(e.previousMonth,{locale:o}),m=[i.nav_button,i.nav_button_previous].join(" "),v=d(e.nextMonth,{locale:o}),y=[i.nav_button,i.nav_button_next].join(" "),b=(t=f==null?void 0:f.IconRight)!==null&&t!==void 0?t:Aa,M=(n=f==null?void 0:f.IconLeft)!==null&&n!==void 0?n:Ba;return s.jsxs("div",{className:i.nav,style:l.nav,children:[!e.hidePrevious&&s.jsx(he,{name:"previous-month","aria-label":h,className:m,style:l.nav_button_previous,disabled:!e.previousMonth,onClick:e.onPreviousClick,children:r==="rtl"?s.jsx(b,{className:i.nav_icon,style:l.nav_icon}):s.jsx(M,{className:i.nav_icon,style:l.nav_icon})}),!e.hideNext&&s.jsx(he,{name:"next-month","aria-label":v,className:y,style:l.nav_button_next,disabled:!e.nextMonth,onClick:e.onNextClick,children:r==="rtl"?s.jsx(M,{className:i.nav_icon,style:l.nav_icon}):s.jsx(b,{className:i.nav_icon,style:l.nav_icon})})]})}function tt(e){var t=j().numberOfMonths,n=ie(),a=n.previousMonth,r=n.nextMonth,o=n.goToMonth,i=n.displayMonths,l=i.findIndex(function(v){return Te(e.displayMonth,v)}),c=l===0,u=l===i.length-1,d=t>1&&(c||!u),f=t>1&&(u||!c),h=function(){a&&o(a)},m=function(){r&&o(r)};return s.jsx(Ha,{displayMonth:e.displayMonth,hideNext:d,hidePrevious:f,nextMonth:r,previousMonth:a,onPreviousClick:h,onNextClick:m})}function qa(e){var t,n=j(),a=n.classNames,r=n.disableNavigation,o=n.styles,i=n.captionLayout,l=n.components,c=(t=l==null?void 0:l.CaptionLabel)!==null&&t!==void 0?t:bt,u;return r?u=s.jsx(c,{id:e.id,displayMonth:e.displayMonth}):i==="dropdown"?u=s.jsx(et,{displayMonth:e.displayMonth,id:e.id}):i==="dropdown-buttons"?u=s.jsxs(s.Fragment,{children:[s.jsx(et,{displayMonth:e.displayMonth,displayIndex:e.displayIndex,id:e.id}),s.jsx(tt,{displayMonth:e.displayMonth,displayIndex:e.displayIndex,id:e.id})]}):u=s.jsxs(s.Fragment,{children:[s.jsx(c,{id:e.id,displayMonth:e.displayMonth,displayIndex:e.displayIndex}),s.jsx(tt,{displayMonth:e.displayMonth,id:e.id})]}),s.jsx("div",{className:a.caption,style:o.caption,children:u})}function Xa(e){var t=j(),n=t.footer,a=t.styles,r=t.classNames.tfoot;return n?s.jsx("tfoot",{className:r,style:a.tfoot,children:s.jsx("tr",{children:s.jsx("td",{colSpan:8,children:n})})}):s.jsx(s.Fragment,{})}function Va(e,t,n){for(var a=n?$(new Date):I(new Date,{locale:e,weekStartsOn:t}),r=[],o=0;o<7;o++){var i=S(a,o);r.push(i)}return r}function Ga(){var e=j(),t=e.classNames,n=e.styles,a=e.showWeekNumber,r=e.locale,o=e.weekStartsOn,i=e.ISOWeek,l=e.formatters.formatWeekdayName,c=e.labels.labelWeekday,u=Va(r,o,i);return s.jsxs("tr",{style:n.head_row,className:t.head_row,children:[a&&s.jsx("td",{style:n.head_cell,className:t.head_cell}),u.map(function(d,f){return s.jsx("th",{scope:"col",className:t.head_cell,style:n.head_cell,"aria-label":c(d,{locale:r}),children:l(d,{locale:r})},f)})]})}function Qa(){var e,t=j(),n=t.classNames,a=t.styles,r=t.components,o=(e=r==null?void 0:r.HeadRow)!==null&&e!==void 0?e:Ga;return s.jsx("thead",{style:a.head,className:n.head,children:s.jsx(o,{})})}function $a(e){var t=j(),n=t.locale,a=t.formatters.formatDay;return s.jsx(s.Fragment,{children:a(e.date,{locale:n})})}var We=p.createContext(void 0);function Ua(e){if(!oe(e.initialProps)){var t={selected:void 0,modifiers:{disabled:[]}};return s.jsx(We.Provider,{value:t,children:e.children})}return s.jsx(za,{initialProps:e.initialProps,children:e.children})}function za(e){var t=e.initialProps,n=e.children,a=t.selected,r=t.min,o=t.max,i=function(u,d,f){var h,m;(h=t.onDayClick)===null||h===void 0||h.call(t,u,d,f);var v=!!(d.selected&&r&&(a==null?void 0:a.length)===r);if(!v){var y=!!(!d.selected&&o&&(a==null?void 0:a.length)===o);if(!y){var b=a?yt([],a):[];if(d.selected){var M=b.findIndex(function(D){return P(u,D)});b.splice(M,1)}else b.push(u);(m=t.onSelect)===null||m===void 0||m.call(t,b,u,d,f)}}},l={disabled:[]};a&&l.disabled.push(function(u){var d=o&&a.length>o-1,f=a.some(function(h){return P(h,u)});return!!(d&&!f)});var c={selected:a,onDayClick:i,modifiers:l};return s.jsx(We.Provider,{value:c,children:n})}function Fe(){var e=p.useContext(We);if(!e)throw new Error("useSelectMultiple must be used within a SelectMultipleProvider");return e}function Ja(e,t){var n=t||{},a=n.from,r=n.to;return a&&r?P(r,e)&&P(a,e)?void 0:P(r,e)?{from:r,to:void 0}:P(a,e)?void 0:je(a,e)?{from:e,to:r}:{from:a,to:e}:r?je(e,r)?{from:r,to:e}:{from:e,to:r}:a?vt(e,a)?{from:e,to:a}:{from:a,to:e}:{from:e,to:void 0}}var Ye=p.createContext(void 0);function Ka(e){if(!se(e.initialProps)){var t={selected:void 0,modifiers:{range_start:[],range_end:[],range_middle:[],disabled:[]}};return s.jsx(Ye.Provider,{value:t,children:e.children})}return s.jsx(Za,{initialProps:e.initialProps,children:e.children})}function Za(e){var t=e.initialProps,n=e.children,a=t.selected,r=a||{},o=r.from,i=r.to,l=t.min,c=t.max,u=function(m,v,y){var b,M;(b=t.onDayClick)===null||b===void 0||b.call(t,m,v,y);var D=Ja(m,a);(M=t.onSelect)===null||M===void 0||M.call(t,D,m,v,y)},d={range_start:[],range_end:[],range_middle:[],disabled:[]};if(o?(d.range_start=[o],i?(d.range_end=[i],P(o,i)||(d.range_middle=[{after:o,before:i}])):d.range_end=[o]):i&&(d.range_start=[i],d.range_end=[i]),l&&(o&&!i&&d.disabled.push({after:xe(o,l-1),before:S(o,l-1)}),o&&i&&d.disabled.push({after:o,before:S(o,l-1)}),!o&&i&&d.disabled.push({after:xe(i,l-1),before:S(i,l-1)})),c){if(o&&!i&&(d.disabled.push({before:S(o,-c+1)}),d.disabled.push({after:S(o,c-1)})),o&&i){var f=L(i,o)+1,h=c-f;d.disabled.push({before:xe(o,h)}),d.disabled.push({after:S(i,h)})}!o&&i&&(d.disabled.push({before:S(i,-c+1)}),d.disabled.push({after:S(i,c-1)}))}return s.jsx(Ye.Provider,{value:{selected:a,onDayClick:u,modifiers:d},children:n})}function Ee(){var e=p.useContext(Ye);if(!e)throw new Error("useSelectRange must be used within a SelectRangeProvider");return e}function de(e){return Array.isArray(e)?yt([],e):e!==void 0?[e]:[]}function er(e){var t={};return Object.entries(e).forEach(function(n){var a=n[0],r=n[1];t[a]=de(r)}),t}var E;(function(e){e.Outside="outside",e.Disabled="disabled",e.Selected="selected",e.Hidden="hidden",e.Today="today",e.RangeStart="range_start",e.RangeEnd="range_end",e.RangeMiddle="range_middle"})(E||(E={}));var tr=E.Selected,A=E.Disabled,nr=E.Hidden,ar=E.Today,pe=E.RangeEnd,De=E.RangeMiddle,ke=E.RangeStart,rr=E.Outside;function or(e,t,n){var a,r=(a={},a[tr]=de(e.selected),a[A]=de(e.disabled),a[nr]=de(e.hidden),a[ar]=[e.today],a[pe]=[],a[De]=[],a[ke]=[],a[rr]=[],a);return e.fromDate&&r[A].push({before:e.fromDate}),e.toDate&&r[A].push({after:e.toDate}),oe(e)?r[A]=r[A].concat(t.modifiers[A]):se(e)&&(r[A]=r[A].concat(n.modifiers[A]),r[ke]=n.modifiers[ke],r[De]=n.modifiers[De],r[pe]=n.modifiers[pe]),r}var pt=p.createContext(void 0);function sr(e){var t=j(),n=Fe(),a=Ee(),r=or(t,n,a),o=er(t.modifiers),i=x(x({},r),o);return s.jsx(pt.Provider,{value:i,children:e.children})}function Dt(){var e=p.useContext(pt);if(!e)throw new Error("useModifiers must be used within a ModifiersProvider");return e}function ir(e){return!!(e&&typeof e=="object"&&"before"in e&&"after"in e)}function lr(e){return!!(e&&typeof e=="object"&&"from"in e)}function cr(e){return!!(e&&typeof e=="object"&&"after"in e)}function ur(e){return!!(e&&typeof e=="object"&&"before"in e)}function dr(e){return!!(e&&typeof e=="object"&&"dayOfWeek"in e)}function fr(e,t){var n,a=t.from,r=t.to;if(a&&r){var o=L(r,a)<0;o&&(n=[r,a],a=n[0],r=n[1]);var i=L(e,a)>=0&&L(r,e)>=0;return i}return r?P(r,e):a?P(a,e):!1}function hr(e){return Se(e)}function mr(e){return Array.isArray(e)&&e.every(Se)}function vr(e,t){return t.some(function(n){if(typeof n=="boolean")return n;if(hr(n))return P(e,n);if(mr(n))return n.includes(e);if(lr(n))return fr(e,n);if(dr(n))return n.dayOfWeek.includes(e.getDay());if(ir(n)){var a=L(n.before,e),r=L(n.after,e),o=a>0,i=r<0,l=je(n.before,n.after);return l?i&&o:o||i}return cr(n)?L(e,n.after)>0:ur(n)?L(n.before,e)>0:typeof n=="function"?n(e):!1})}function Le(e,t,n){var a=Object.keys(t).reduce(function(o,i){var l=t[i];return vr(e,l)&&o.push(i),o},[]),r={};return a.forEach(function(o){return r[o]=!0}),n&&!Te(e,n)&&(r.outside=!0),r}function yr(e,t){for(var n=O(e[0]),a=Pe(e[e.length-1]),r,o,i=n;i<=a;){var l=Le(i,t),c=!l.disabled&&!l.hidden;if(!c){i=S(i,1);continue}if(l.selected)return i;l.today&&!o&&(o=i),r||(r=i),i=S(i,1)}return o||r}var gr=365;function kt(e,t){var n=t.moveBy,a=t.direction,r=t.context,o=t.modifiers,i=t.retry,l=i===void 0?{count:0,lastFocused:e}:i,c=r.weekStartsOn,u=r.fromDate,d=r.toDate,f=r.locale,h={day:S,week:_e,month:Y,year:rn,startOfWeek:function(b){return r.ISOWeek?$(b):I(b,{locale:f,weekStartsOn:c})},endOfWeek:function(b){return r.ISOWeek?ct(b):Oe(b,{locale:f,weekStartsOn:c})}},m=h[n](e,a==="after"?1:-1);a==="before"&&u?m=on([u,m]):a==="after"&&d&&(m=sn([d,m]));var v=!0;if(o){var y=Le(m,o);v=!y.disabled&&!y.hidden}return v?m:l.count>gr?l.lastFocused:kt(m,{moveBy:n,direction:a,context:r,modifiers:o,retry:x(x({},l),{count:l.count+1})})}var Nt=p.createContext(void 0);function br(e){var t=ie(),n=Dt(),a=p.useState(),r=a[0],o=a[1],i=p.useState(),l=i[0],c=i[1],u=yr(t.displayMonths,n),d=r??(l&&t.isDateDisplayed(l))?l:u,f=function(){c(r),o(void 0)},h=function(b){o(b)},m=j(),v=function(b,M){if(r){var D=kt(r,{moveBy:b,direction:M,context:m,modifiers:n});P(r,D)||(t.goToDate(D,r),h(D))}},y={focusedDay:r,focusTarget:d,blur:f,focus:h,focusDayAfter:function(){return v("day","after")},focusDayBefore:function(){return v("day","before")},focusWeekAfter:function(){return v("week","after")},focusWeekBefore:function(){return v("week","before")},focusMonthBefore:function(){return v("month","before")},focusMonthAfter:function(){return v("month","after")},focusYearBefore:function(){return v("year","before")},focusYearAfter:function(){return v("year","after")},focusStartOfWeek:function(){return v("startOfWeek","before")},focusEndOfWeek:function(){return v("endOfWeek","after")}};return s.jsx(Nt.Provider,{value:y,children:e.children})}function Ie(){var e=p.useContext(Nt);if(!e)throw new Error("useFocusContext must be used within a FocusProvider");return e}function xr(e,t){var n=Dt(),a=Le(e,n,t);return a}var Re=p.createContext(void 0);function wr(e){if(!me(e.initialProps)){var t={selected:void 0};return s.jsx(Re.Provider,{value:t,children:e.children})}return s.jsx(pr,{initialProps:e.initialProps,children:e.children})}function pr(e){var t=e.initialProps,n=e.children,a=function(o,i,l){var c,u,d;if((c=t.onDayClick)===null||c===void 0||c.call(t,o,i,l),i.selected&&!t.required){(u=t.onSelect)===null||u===void 0||u.call(t,void 0,o,i,l);return}(d=t.onSelect)===null||d===void 0||d.call(t,o,o,i,l)},r={selected:t.selected,onDayClick:a};return s.jsx(Re.Provider,{value:r,children:n})}function Mt(){var e=p.useContext(Re);if(!e)throw new Error("useSelectSingle must be used within a SelectSingleProvider");return e}function Dr(e,t){var n=j(),a=Mt(),r=Fe(),o=Ee(),i=Ie(),l=i.focusDayAfter,c=i.focusDayBefore,u=i.focusWeekAfter,d=i.focusWeekBefore,f=i.blur,h=i.focus,m=i.focusMonthBefore,v=i.focusMonthAfter,y=i.focusYearBefore,b=i.focusYearAfter,M=i.focusStartOfWeek,D=i.focusEndOfWeek,C=function(w){var g,ve,ye,ge;me(n)?(g=a.onDayClick)===null||g===void 0||g.call(a,e,t,w):oe(n)?(ve=r.onDayClick)===null||ve===void 0||ve.call(r,e,t,w):se(n)?(ye=o.onDayClick)===null||ye===void 0||ye.call(o,e,t,w):(ge=n.onDayClick)===null||ge===void 0||ge.call(n,e,t,w)},R=function(w){var g;h(e),(g=n.onDayFocus)===null||g===void 0||g.call(n,e,t,w)},Z=function(w){var g;f(),(g=n.onDayBlur)===null||g===void 0||g.call(n,e,t,w)},X=function(w){var g;(g=n.onDayMouseEnter)===null||g===void 0||g.call(n,e,t,w)},N=function(w){var g;(g=n.onDayMouseLeave)===null||g===void 0||g.call(n,e,t,w)},T=function(w){var g;(g=n.onDayPointerEnter)===null||g===void 0||g.call(n,e,t,w)},F=function(w){var g;(g=n.onDayPointerLeave)===null||g===void 0||g.call(n,e,t,w)},le=function(w){var g;(g=n.onDayTouchCancel)===null||g===void 0||g.call(n,e,t,w)},V=function(w){var g;(g=n.onDayTouchEnd)===null||g===void 0||g.call(n,e,t,w)},ce=function(w){var g;(g=n.onDayTouchMove)===null||g===void 0||g.call(n,e,t,w)},ue=function(w){var g;(g=n.onDayTouchStart)===null||g===void 0||g.call(n,e,t,w)},jt=function(w){var g;(g=n.onDayKeyUp)===null||g===void 0||g.call(n,e,t,w)},Ct=function(w){var g;switch(w.key){case"ArrowLeft":w.preventDefault(),w.stopPropagation(),n.dir==="rtl"?l():c();break;case"ArrowRight":w.preventDefault(),w.stopPropagation(),n.dir==="rtl"?c():l();break;case"ArrowDown":w.preventDefault(),w.stopPropagation(),u();break;case"ArrowUp":w.preventDefault(),w.stopPropagation(),d();break;case"PageUp":w.preventDefault(),w.stopPropagation(),w.shiftKey?y():m();break;case"PageDown":w.preventDefault(),w.stopPropagation(),w.shiftKey?b():v();break;case"Home":w.preventDefault(),w.stopPropagation(),M();break;case"End":w.preventDefault(),w.stopPropagation(),D();break}(g=n.onDayKeyDown)===null||g===void 0||g.call(n,e,t,w)},St={onClick:C,onFocus:R,onBlur:Z,onKeyDown:Ct,onKeyUp:jt,onMouseEnter:X,onMouseLeave:N,onPointerEnter:T,onPointerLeave:F,onTouchCancel:le,onTouchEnd:V,onTouchMove:ce,onTouchStart:ue};return St}function kr(){var e=j(),t=Mt(),n=Fe(),a=Ee(),r=me(e)?t.selected:oe(e)?n.selected:se(e)?a.selected:void 0;return r}function Nr(e){return Object.values(E).includes(e)}function Mr(e,t){var n=[e.classNames.day];return Object.keys(t).forEach(function(a){var r=e.modifiersClassNames[a];if(r)n.push(r);else if(Nr(a)){var o=e.classNames["day_".concat(a)];o&&n.push(o)}}),n}function _r(e,t){var n=x({},e.styles.day);return Object.keys(t).forEach(function(a){var r;n=x(x({},n),(r=e.modifiersStyles)===null||r===void 0?void 0:r[a])}),n}function jr(e,t,n){var a,r,o,i=j(),l=Ie(),c=xr(e,t),u=Dr(e,c),d=kr(),f=!!(i.onDayClick||i.mode!=="default");p.useEffect(function(){var X;c.outside||l.focusedDay&&f&&P(l.focusedDay,e)&&((X=n.current)===null||X===void 0||X.focus())},[l.focusedDay,e,n,f,c.outside]);var h=Mr(i,c).join(" "),m=_r(i,c),v=!!(c.outside&&!i.showOutsideDays||c.hidden),y=(o=(r=i.components)===null||r===void 0?void 0:r.DayContent)!==null&&o!==void 0?o:$a,b=s.jsx(y,{date:e,displayMonth:t,activeModifiers:c}),M={style:m,className:h,children:b,role:"gridcell"},D=l.focusTarget&&P(l.focusTarget,e)&&!c.outside,C=l.focusedDay&&P(l.focusedDay,e),R=x(x(x({},M),(a={disabled:c.disabled,role:"gridcell"},a["aria-selected"]=c.selected,a.tabIndex=C||D?0:-1,a)),u),Z={isButton:f,isHidden:v,activeModifiers:c,selectedDays:d,buttonProps:R,divProps:M};return Z}function Cr(e){var t=p.useRef(null),n=jr(e.date,e.displayMonth,t);return n.isHidden?s.jsx("div",{role:"gridcell"}):n.isButton?s.jsx(he,x({name:"day",ref:t},n.buttonProps)):s.jsx("div",x({},n.divProps))}function Sr(e){var t=e.number,n=e.dates,a=j(),r=a.onWeekNumberClick,o=a.styles,i=a.classNames,l=a.locale,c=a.labels.labelWeekNumber,u=a.formatters.formatWeekNumber,d=u(Number(t),{locale:l});if(!r)return s.jsx("span",{className:i.weeknumber,style:o.weeknumber,children:d});var f=c(Number(t),{locale:l}),h=function(m){r(t,n,m)};return s.jsx(he,{name:"week-number","aria-label":f,className:i.weeknumber,style:o.weeknumber,onClick:h,children:d})}function Pr(e){var t,n,a=j(),r=a.styles,o=a.classNames,i=a.showWeekNumber,l=a.components,c=(t=l==null?void 0:l.Day)!==null&&t!==void 0?t:Cr,u=(n=l==null?void 0:l.WeekNumber)!==null&&n!==void 0?n:Sr,d;return i&&(d=s.jsx("td",{className:o.cell,style:r.cell,children:s.jsx(u,{number:e.weekNumber,dates:e.dates})})),s.jsxs("tr",{className:o.row,style:r.row,children:[d,e.dates.map(function(f){return s.jsx("td",{className:o.cell,style:r.cell,role:"presentation",children:s.jsx(c,{displayMonth:e.displayMonth,date:f})},sa(f))})]})}function nt(e,t,n){for(var a=n!=null&&n.ISOWeek?ct(t):Oe(t,n),r=n!=null&&n.ISOWeek?$(e):I(e,n),o=L(a,r),i=[],l=0;l<=o;l++)i.push(S(r,l));var c=i.reduce(function(u,d){var f=n!=null&&n.ISOWeek?dt(d):ht(d,n),h=u.find(function(m){return m.weekNumber===f});return h?(h.dates.push(d),u):(u.push({weekNumber:f,dates:[d]}),u)},[]);return c}function Or(e,t){var n=nt(O(e),Pe(e),t);if(t!=null&&t.useFixedWeeks){var a=la(e,t);if(a<6){var r=n[n.length-1],o=r.dates[r.dates.length-1],i=_e(o,6-a),l=nt(_e(o,1),i,t);n.push.apply(n,l)}}return n}function Tr(e){var t,n,a,r=j(),o=r.locale,i=r.classNames,l=r.styles,c=r.hideHead,u=r.fixedWeeks,d=r.components,f=r.weekStartsOn,h=r.firstWeekContainsDate,m=r.ISOWeek,v=Or(e.displayMonth,{useFixedWeeks:!!u,ISOWeek:m,locale:o,weekStartsOn:f,firstWeekContainsDate:h}),y=(t=d==null?void 0:d.Head)!==null&&t!==void 0?t:Qa,b=(n=d==null?void 0:d.Row)!==null&&n!==void 0?n:Pr,M=(a=d==null?void 0:d.Footer)!==null&&a!==void 0?a:Xa;return s.jsxs("table",{id:e.id,className:i.table,style:l.table,role:"grid","aria-labelledby":e["aria-labelledby"],children:[!c&&s.jsx(y,{}),s.jsx("tbody",{className:i.tbody,style:l.tbody,children:v.map(function(D){return s.jsx(b,{displayMonth:e.displayMonth,dates:D.dates,weekNumber:D.weekNumber},D.weekNumber)})}),s.jsx(M,{displayMonth:e.displayMonth})]})}function Wr(){return!!(typeof window<"u"&&window.document&&window.document.createElement)}var Fr=Wr()?p.useLayoutEffect:p.useEffect,Ne=!1,Yr=0;function at(){return"react-day-picker-".concat(++Yr)}function Er(e){var t,n=e??(Ne?at():null),a=p.useState(n),r=a[0],o=a[1];return Fr(function(){r===null&&o(at())},[]),p.useEffect(function(){Ne===!1&&(Ne=!0)},[]),(t=e??r)!==null&&t!==void 0?t:void 0}function Lr(e){var t,n,a=j(),r=a.dir,o=a.classNames,i=a.styles,l=a.components,c=ie().displayMonths,u=Er(a.id?"".concat(a.id,"-").concat(e.displayIndex):void 0),d=a.id?"".concat(a.id,"-grid-").concat(e.displayIndex):void 0,f=[o.month],h=i.month,m=e.displayIndex===0,v=e.displayIndex===c.length-1,y=!m&&!v;r==="rtl"&&(t=[m,v],v=t[0],m=t[1]),m&&(f.push(o.caption_start),h=x(x({},h),i.caption_start)),v&&(f.push(o.caption_end),h=x(x({},h),i.caption_end)),y&&(f.push(o.caption_between),h=x(x({},h),i.caption_between));var b=(n=l==null?void 0:l.Caption)!==null&&n!==void 0?n:qa;return s.jsxs("div",{className:f.join(" "),style:h,children:[s.jsx(b,{id:u,displayMonth:e.displayMonth,displayIndex:e.displayIndex}),s.jsx(Tr,{id:d,"aria-labelledby":u,displayMonth:e.displayMonth})]},e.displayIndex)}function Ir(e){var t=j(),n=t.classNames,a=t.styles;return s.jsx("div",{className:n.months,style:a.months,children:e.children})}function Rr(e){var t,n,a=e.initialProps,r=j(),o=Ie(),i=ie(),l=p.useState(!1),c=l[0],u=l[1];p.useEffect(function(){r.initialFocus&&o.focusTarget&&(c||(o.focus(o.focusTarget),u(!0)))},[r.initialFocus,c,o.focus,o.focusTarget,o]);var d=[r.classNames.root,r.className];r.numberOfMonths>1&&d.push(r.classNames.multiple_months),r.showWeekNumber&&d.push(r.classNames.with_weeknumber);var f=x(x({},r.styles.root),r.style),h=Object.keys(a).filter(function(v){return v.startsWith("data-")}).reduce(function(v,y){var b;return x(x({},v),(b={},b[y]=a[y],b))},{}),m=(n=(t=a.components)===null||t===void 0?void 0:t.Months)!==null&&n!==void 0?n:Ir;return s.jsx("div",x({className:d.join(" "),style:f,dir:r.dir,id:r.id,nonce:a.nonce,title:a.title,lang:a.lang},h,{children:s.jsx(m,{children:i.displayMonths.map(function(v,y){return s.jsx(Lr,{displayIndex:y,displayMonth:v},y)})})}))}function Br(e){var t=e.children,n=ua(e,["children"]);return s.jsx(Sa,{initialProps:n,children:s.jsx(Ra,{children:s.jsx(wr,{initialProps:n,children:s.jsx(Ua,{initialProps:n,children:s.jsx(Ka,{initialProps:n,children:s.jsx(sr,{children:s.jsx(br,{children:t})})})})})})})}function Ar(e){return s.jsx(Br,x({},e,{children:s.jsx(Rr,{initialProps:e})}))}function _t({className:e,classNames:t,showOutsideDays:n=!0,...a}){return s.jsx(Ar,{showOutsideDays:n,className:J("p-3",e),classNames:{months:"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",month:"space-y-4",caption:"flex justify-center pt-1 relative items-center",caption_label:"text-sm font-medium",nav:"space-x-1 flex items-center",nav_button:J(Be({variant:"outline"}),"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-y-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20",day:J(Be({variant:"ghost"}),"h-9 w-9 p-0 font-normal aria-selected:opacity-100"),day_range_end:"day-range-end",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...t},components:{IconLeft:({...r})=>s.jsx(zt,{className:"h-4 w-4"}),IconRight:({...r})=>s.jsx(st,{className:"h-4 w-4"})},...a})}_t.displayName="Calendar";const Me=[{id:"1",recipient:"0x1234...5678",recipientName:"Sarah Miller",amount:"100",token:"USDC",scheduledDate:new Date(Date.now()+864e5*2).toISOString(),recurring:!0,frequency:"monthly",status:"pending",createdAt:new Date().toISOString()},{id:"2",recipient:"0x9876...5432",recipientName:"Alex Rodriguez",amount:"50",token:"IDRX",scheduledDate:new Date(Date.now()+864e5*5).toISOString(),recurring:!1,status:"pending",createdAt:new Date().toISOString()},{id:"3",recipient:"0x6543...2109",recipientName:"Jamie Smith",amount:"200",token:"USDC",scheduledDate:new Date(Date.now()-864e5*1).toISOString(),recurring:!1,status:"completed",createdAt:new Date(Date.now()-864e5*7).toISOString()}],rt="strapt_scheduled_transfers";function Hr(){const[e,t]=p.useState([]),[n,a]=p.useState(!0),{address:r}=Pt();p.useEffect(()=>{(()=>{if(!r){t(Me),a(!1);return}try{const m=`${rt}_${r}`,v=localStorage.getItem(m);if(v){const y=JSON.parse(v);t(y)}else t(Me)}catch(m){console.error("Error loading scheduled transfers:",m),t(Me)}a(!1)})()},[r]);const o=p.useCallback(h=>{if(r)try{const m=`${rt}_${r}`;localStorage.setItem(m,JSON.stringify(h))}catch(m){console.error("Error saving scheduled transfers:",m)}},[r]),i=p.useCallback(h=>{if(!r)return;const m={...h,id:`transfer_${Date.now()}`,createdAt:new Date().toISOString(),status:"pending"};t(v=>{const y=[...v,m];return o(y),y})},[r,o]),l=p.useCallback((h,m)=>{r&&t(v=>{const y=v.map(b=>b.id===h?{...b,...m}:b);return o(y),y})},[r,o]),c=p.useCallback(h=>{r&&t(m=>{const v=m.map(y=>y.id===h?{...y,status:"cancelled"}:y);return o(v),v})},[r,o]),u=p.useCallback(h=>{r&&t(m=>{const v=m.filter(y=>y.id!==h);return o(v),v})},[r,o]),d=p.useCallback(()=>e.filter(h=>h.status==="pending"),[e]),f=p.useCallback(()=>e.filter(h=>h.status==="completed"),[e]);return{scheduledTransfers:e,isLoading:n,addScheduledTransfer:i,updateScheduledTransfer:l,cancelScheduledTransfer:c,removeScheduledTransfer:u,getPendingTransfers:d,getCompletedTransfers:f}}const so=()=>{const{scheduledTransfers:e,isLoading:t,addScheduledTransfer:n,cancelScheduledTransfer:a,removeScheduledTransfer:r}=Hr(),[o,i]=p.useState(!1),[l,c]=p.useState(new Date),[u,d]=p.useState(!1),[f,h]=p.useState("once"),[m,v]=p.useState("IDRX"),{toast:y}=Ut(),b=Ot(),M=p.useRef(null),D=p.useRef(null),C=N=>{var ce,ue;N.preventDefault(),d(!0);const T=((ce=M.current)==null?void 0:ce.value)||"",F=((ue=D.current)==null?void 0:ue.value)||"";if(!T||!F||!l){y({title:"Missing information",description:"Please fill in all required fields",variant:"destructive",duration:3e3}),d(!1);return}const le=f!=="once";let V;if(le)switch(f){case"daily":V="daily";break;case"weekly":V="weekly";break;case"monthly":V="monthly";break;default:V=void 0}n({recipient:T,recipientName:T.includes("0x")?void 0:T,amount:F,token:m,scheduledDate:l.toISOString(),recurring:le,frequency:V}),M.current&&(M.current.value=""),D.current&&(D.current.value=""),c(new Date),h("once"),v("IDRX"),y({title:"Transfer scheduled",description:"Your transfer has been scheduled successfully",duration:3e3}),d(!1),i(!1)},R=N=>{a(N),y({title:"Transfer cancelled",description:"The scheduled transfer has been cancelled",duration:3e3})},Z=N=>{r(N),y({title:"Transfer removed",description:"The scheduled transfer has been removed",duration:3e3})},X=N=>{const T=new Date,F=Math.floor((N.getTime()-T.getTime())/(1e3*60*60*24));return F===0?"Today":F===1?"Tomorrow":F<7?`In ${F} days`:F<30?`In ${Math.floor(F/7)} weeks`:`In ${Math.floor(F/30)} months`};return s.jsxs(s.Fragment,{children:[s.jsxs(Tt,{children:[s.jsx(Wt,{children:s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsxs("div",{children:[s.jsx(Ft,{className:"text-xl",children:"Scheduled Transfers"}),s.jsx(Yt,{children:"Upcoming and recurring transfers"})]}),s.jsxs(B,{size:"sm",onClick:()=>i(!0),children:[s.jsx(Jt,{className:"h-4 w-4 mr-1"})," New"]})]})}),s.jsx(Et,{children:t?s.jsx("div",{className:"space-y-4",children:Array.from({length:3}).map((N,T)=>s.jsxs("div",{children:[s.jsx("div",{className:"bg-secondary/30 rounded-lg p-4 animate-pulse",children:s.jsxs("div",{className:"flex justify-between items-start",children:[s.jsxs("div",{children:[s.jsx(G,{className:"h-4 w-32 mb-2"}),s.jsx(G,{className:"h-3 w-20 mb-2"}),s.jsxs("div",{className:"flex items-center gap-1 mt-1",children:[s.jsx(G,{className:"h-3.5 w-3.5 rounded-full"}),s.jsx(G,{className:"h-3 w-24"})]}),s.jsx(G,{className:"h-5 w-20 mt-2"})]}),s.jsxs("div",{className:"flex gap-1",children:[s.jsx(G,{className:"h-8 w-8 rounded-md"}),s.jsx(G,{className:"h-8 w-8 rounded-md"})]})]})}),T<2&&s.jsx(Ae,{className:"my-2"})]},`skeleton-transfer-${T}-${Date.now()}`))}):e.length===0?s.jsxs("div",{className:"text-center py-8",children:[s.jsx(Kt,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),s.jsx("p",{className:"text-muted-foreground",children:"No scheduled transfers"}),s.jsx(B,{variant:"link",onClick:()=>i(!0),children:"Create your first scheduled transfer"})]}):s.jsx("div",{className:"space-y-4",children:e.map((N,T)=>s.jsxs("div",{children:[s.jsx("div",{className:J("bg-secondary/30 rounded-lg p-4",N.status==="cancelled"&&"opacity-60"),children:s.jsxs("div",{className:"flex justify-between items-start",children:[s.jsxs("div",{children:[s.jsx("h3",{className:"font-medium",children:N.recipientName||N.recipient}),s.jsxs("p",{className:"text-sm text-muted-foreground",children:[N.amount," ",N.token]}),s.jsxs("div",{className:"flex items-center gap-1 mt-1",children:[s.jsx(qe,{className:"h-3.5 w-3.5 text-muted-foreground"}),s.jsx("span",{className:"text-xs",children:H(new Date(N.scheduledDate),"PPP")}),s.jsxs("span",{className:"text-xs text-muted-foreground",children:["(",X(new Date(N.scheduledDate)),")"]})]}),s.jsxs("div",{className:"flex gap-2 mt-2",children:[N.recurring&&N.frequency&&s.jsxs($e,{variant:"outline",children:[s.jsx(Lt,{className:"h-3 w-3 mr-1"})," ",N.frequency]}),s.jsx($e,{variant:"outline",className:J(N.status==="pending"&&"bg-yellow-500/10 text-yellow-600",N.status==="completed"&&"bg-green-500/10 text-green-600",N.status==="failed"&&"bg-red-500/10 text-red-600",N.status==="cancelled"&&"bg-gray-500/10 text-gray-600"),children:N.status})]})]}),s.jsxs("div",{className:"flex gap-1",children:[N.status==="pending"&&s.jsxs(s.Fragment,{children:[s.jsx(B,{variant:"ghost",size:"icon",className:"h-8 w-8",title:"Edit transfer",children:s.jsx(Zt,{className:"h-4 w-4"})}),s.jsx(B,{variant:"ghost",size:"icon",className:"h-8 w-8 text-yellow-600 hover:text-yellow-700 hover:bg-yellow-100",title:"Cancel transfer",onClick:()=>R(N.id),children:s.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round","aria-hidden":"true",role:"img","aria-label":"Cancel",children:[s.jsx("title",{children:"Cancel"}),s.jsx("circle",{cx:"12",cy:"12",r:"10"}),s.jsx("line",{x1:"8",y1:"12",x2:"16",y2:"12"})]})})]}),s.jsx(B,{variant:"ghost",size:"icon",className:"h-8 w-8 text-red-500 hover:text-red-600 hover:bg-red-100",title:"Delete transfer",onClick:()=>Z(N.id),children:s.jsx(en,{className:"h-4 w-4"})})]})]})}),T<e.length-1&&s.jsx(Ae,{className:"my-2"})]},N.id))})}),s.jsx(It,{className:"justify-center",children:s.jsxs(B,{variant:"link",onClick:()=>b("/app/transfer"),className:"text-sm",children:["Go to Transfer page ",s.jsx(st,{className:"h-4 w-4 ml-1"})]})})]}),s.jsx(Rt,{open:o,onOpenChange:i,children:s.jsxs(Bt,{className:"sm:max-w-md",children:[s.jsxs(At,{children:[s.jsx(Ht,{children:"Schedule a Transfer"}),s.jsx(qt,{children:"Create a one-time or recurring transfer that will execute automatically"})]}),s.jsxs("form",{onSubmit:C,children:[s.jsxs("div",{className:"grid gap-4 py-4",children:[s.jsxs("div",{className:"grid gap-2",children:[s.jsx(ee,{htmlFor:"recipient",children:"Recipient"}),s.jsx(He,{id:"recipient",placeholder:"Address or @username",ref:M,disabled:u})]}),s.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[s.jsxs("div",{className:"grid gap-2",children:[s.jsx(ee,{htmlFor:"amount",children:"Amount"}),s.jsx(He,{id:"amount",type:"number",min:"0",placeholder:"0.00",ref:D,disabled:u})]}),s.jsxs("div",{className:"grid gap-2",children:[s.jsx(ee,{htmlFor:"token",children:"Token"}),s.jsxs(Xe,{value:m,onValueChange:v,disabled:u,children:[s.jsx(Ve,{id:"token",children:s.jsx(Ge,{placeholder:"Select token"})}),s.jsxs(Qe,{children:[s.jsx(U,{value:"IDRX",children:"IDRX"}),s.jsx(U,{value:"USDC",children:"USDC"})]})]})]})]}),s.jsxs("div",{className:"grid gap-2",children:[s.jsx(ee,{children:"Schedule Date"}),s.jsxs(Xt,{children:[s.jsx(Vt,{asChild:!0,children:s.jsxs(B,{type:"button",variant:"outline",className:J("justify-start text-left font-normal",!l&&"text-muted-foreground"),disabled:u,children:[s.jsx(qe,{className:"mr-2 h-4 w-4"}),l?H(l,"PPP"):s.jsx("span",{children:"Pick a date"})]})}),s.jsx(Gt,{className:"w-auto p-0",children:s.jsx(_t,{mode:"single",selected:l,onSelect:c,initialFocus:!0,disabled:N=>N<new Date})})]})]}),s.jsxs("div",{className:"grid gap-2",children:[s.jsx(ee,{htmlFor:"recurring",children:"Frequency"}),s.jsxs(Xe,{value:f,onValueChange:h,disabled:u,children:[s.jsx(Ve,{id:"recurring",children:s.jsx(Ge,{placeholder:"Select frequency"})}),s.jsxs(Qe,{children:[s.jsx(U,{value:"once",children:"One-time only"}),s.jsx(U,{value:"daily",children:"Daily"}),s.jsx(U,{value:"weekly",children:"Weekly"}),s.jsx(U,{value:"monthly",children:"Monthly"})]})]})]})]}),s.jsxs(Qt,{children:[s.jsx(B,{type:"button",variant:"outline",onClick:()=>i(!1),disabled:u,children:"Cancel"}),s.jsx(B,{type:"submit",disabled:u,children:u?s.jsxs(s.Fragment,{children:[s.jsx($t,{className:"mr-2 h-4 w-4 animate-spin"}),"Scheduling..."]}):"Schedule Transfer"})]})]})]})})]})};export{so as default};
