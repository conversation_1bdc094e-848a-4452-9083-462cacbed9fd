import{r as t,j as E}from"./index-G17GlXLb.js";import{M as S,u as L,P as W,a as b,b as A,L as D}from"./proxy-B8vTGf2f.js";class H extends t.Component{getSnapshotBeforeUpdate(l){const e=this.props.childRef.current;if(e&&l.isPresent&&!this.props.isPresent){const s=e.offsetParent,d=s instanceof HTMLElement&&s.offsetWidth||0,n=this.props.sizeRef.current;n.height=e.offsetHeight||0,n.width=e.offsetWidth||0,n.top=e.offsetTop,n.left=e.offsetLeft,n.right=d-n.width-n.left}return null}componentDidUpdate(){}render(){return this.props.children}}function K({children:c,isPresent:l,anchorX:e}){const s=t.useId(),d=t.useRef(null),n=t.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:p}=t.useContext(S);return t.useInsertionEffect(()=>{const{width:x,height:r,top:m,left:i,right:u}=n.current;if(l||!d.current||!x||!r)return;const a=e==="left"?`left: ${i}`:`right: ${u}`;d.current.dataset.motionPopId=s;const f=document.createElement("style");return p&&(f.nonce=p),document.head.appendChild(f),f.sheet&&f.sheet.insertRule(`
          [data-motion-pop-id="${s}"] {
            position: absolute !important;
            width: ${x}px !important;
            height: ${r}px !important;
            ${a}px !important;
            top: ${m}px !important;
          }
        `),()=>{document.head.contains(f)&&document.head.removeChild(f)}},[l]),E.jsx(H,{isPresent:l,childRef:d,sizeRef:n,children:t.cloneElement(c,{ref:d})})}const U=({children:c,initial:l,isPresent:e,onExitComplete:s,custom:d,presenceAffectsLayout:n,mode:p,anchorX:x})=>{const r=L(B),m=t.useId();let i=!0,u=t.useMemo(()=>(i=!1,{id:m,initial:l,isPresent:e,custom:d,onExitComplete:a=>{r.set(a,!0);for(const f of r.values())if(!f)return;s&&s()},register:a=>(r.set(a,!1),()=>r.delete(a))}),[e,r,s]);return n&&i&&(u={...u}),t.useMemo(()=>{r.forEach((a,f)=>r.set(f,!1))},[e]),t.useEffect(()=>{!e&&!r.size&&s&&s()},[e]),p==="popLayout"&&(c=E.jsx(K,{isPresent:e,anchorX:x,children:c})),E.jsx(W.Provider,{value:u,children:c})};function B(){return new Map}const P=c=>c.key||"";function v(c){const l=[];return t.Children.forEach(c,e=>{t.isValidElement(e)&&l.push(e)}),l}const T=({children:c,custom:l,initial:e=!0,onExitComplete:s,presenceAffectsLayout:d=!0,mode:n="sync",propagate:p=!1,anchorX:x="left"})=>{const[r,m]=b(p),i=t.useMemo(()=>v(c),[c]),u=p&&!r?[]:i.map(P),a=t.useRef(!0),f=t.useRef(i),g=L(()=>new Map),[z,I]=t.useState(i),[C,j]=t.useState(i);A(()=>{a.current=!1,f.current=i;for(let h=0;h<C.length;h++){const o=P(C[h]);u.includes(o)?g.delete(o):g.get(o)!==!0&&g.set(o,!1)}},[C,u.length,u.join("-")]);const R=[];if(i!==z){let h=[...i];for(let o=0;o<C.length;o++){const y=C[o],w=P(y);u.includes(w)||(h.splice(o,0,y),R.push(y))}return n==="wait"&&R.length&&(h=R),j(v(h)),I(i),null}const{forceRender:M}=t.useContext(D);return E.jsx(E.Fragment,{children:C.map(h=>{const o=P(h),y=p&&!r?!1:i===C||u.includes(o),w=()=>{if(g.has(o))g.set(o,!0);else return;let $=!0;g.forEach(k=>{k||($=!1)}),$&&(M==null||M(),j(f.current),p&&(m==null||m()),s&&s())};return E.jsx(U,{isPresent:y,initial:!a.current||e?void 0:!1,custom:l,presenceAffectsLayout:d,mode:n,onExitComplete:y?void 0:w,anchorX:x,children:h},o)})})};export{T as A};
