import{r as o,a5 as C,a6 as I,j as a,a7 as M,a4 as y,a8 as H,a9 as A,t as x}from"./index-G17GlXLb.js";import{u as U}from"./index-B97upM1f.js";var b="Switch",[q,$]=M(b),[z,L]=q(b),P=o.forwardRef((t,s)=>{const{__scopeSwitch:e,name:n,checked:r,defaultChecked:m,required:d,disabled:c,value:u="on",onCheckedChange:w,form:i,...v}=t,[l,p]=o.useState(null),S=C(s,h=>p(h)),k=o.useRef(!1),g=l?i||!!l.closest("form"):!0,[f,B]=I({prop:r,defaultProp:m??!1,onChange:w,caller:b});return a.jsxs(z,{scope:e,checked:f,disabled:c,children:[a.jsx(y.button,{type:"button",role:"switch","aria-checked":f,"aria-required":d,"data-state":j(f),"data-disabled":c?"":void 0,disabled:c,value:u,...v,ref:S,onClick:H(t.onClick,h=>{B(T=>!T),g&&(k.current=h.isPropagationStopped(),k.current||h.stopPropagation())})}),g&&a.jsx(N,{control:l,bubbles:!k.current,name:n,value:u,checked:f,required:d,disabled:c,form:i,style:{transform:"translateX(-100%)"}})]})});P.displayName=b;var E="SwitchThumb",R=o.forwardRef((t,s)=>{const{__scopeSwitch:e,...n}=t,r=L(E,e);return a.jsx(y.span,{"data-state":j(r.checked),"data-disabled":r.disabled?"":void 0,...n,ref:s})});R.displayName=E;var O="SwitchBubbleInput",N=o.forwardRef(({__scopeSwitch:t,control:s,checked:e,bubbles:n=!0,...r},m)=>{const d=o.useRef(null),c=C(d,m),u=U(e),w=A(s);return o.useEffect(()=>{const i=d.current;if(!i)return;const v=window.HTMLInputElement.prototype,p=Object.getOwnPropertyDescriptor(v,"checked").set;if(u!==e&&p){const S=new Event("click",{bubbles:n});p.call(i,e),i.dispatchEvent(S)}},[u,e,n]),a.jsx("input",{type:"checkbox","aria-hidden":!0,defaultChecked:e,...r,tabIndex:-1,ref:c,style:{...r.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});N.displayName=O;function j(t){return t?"checked":"unchecked"}var _=P,D=R;const F=o.forwardRef(({className:t,...s},e)=>a.jsx(_,{className:x("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",t),...s,ref:e,children:a.jsx(D,{className:x("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));F.displayName=_.displayName;export{F as S};
