import{k as de,l as me,I as fe,aa as ue,r as h,ab as he,ac as xe,F as a,j as e,B as w,a1 as pe,n as we,o as ge,p as je,v as ye,s as Ne,w as Ce,A as ve,D as Y,d as z,e as W,f as O,ad as V}from"./index-G17GlXLb.js";import{I as B}from"./input-CWM2bTJL.js";import{L as H}from"./label-aQpKQY_t.js";import{B as be}from"./badge-DogxunLX.js";import{Q as ke}from"./QRCode-DVz9xBTO.js";import{A as Pe}from"./arrow-left-D0Dlhjqr.js";import{P as _}from"./plus-DMc7oH9p.js";import{S as ee}from"./shield-check-DdOtbNeF.js";import{Q as Te}from"./qr-code-BnIF1LDH.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R=de("LockKeyhole",[["circle",{cx:"12",cy:"16",r:"1",key:"1au0dj"}],["rect",{x:"3",y:"10",width:"18",height:"12",rx:"2",key:"6s8ecr"}],["path",{d:"M7 10V7a5 5 0 0 1 10 0v3",key:"1pqi11"}]]),J=P=>{if(!P)return"";let T=P.trim();return console.log(`Standardized claim code from [${P}] to [${T}], length: ${T.length}`),T},Ue=()=>{const P=me(),T=fe(),{address:$}=ue(),[se,K]=h.useState(!1),[t,N]=h.useState(null),[Se,G]=h.useState(""),[re,g]=h.useState(!1),[I,A]=h.useState(!1),[Q,u]=h.useState(""),[X,Ee]=h.useState([]),[L,q]=h.useState(!1),[ae,C]=h.useState(!1),[m,j]=h.useState(""),[v,b]=h.useState(""),[Z,k]=h.useState(""),[F]=he(),{claimTransfer:D,isPasswordProtected:S,getTransferDetails:y}=xe(),te=s=>s?s.length>16?`${s.slice(0,8)}...${s.slice(-8)}`:s:"";h.useEffect(()=>{const s=new URLSearchParams(T.search),c=s.get("id"),n=s.get("code");c&&(n?(j(c),b(n),g(!0)):M(c))},[T.search]),h.useEffect(()=>{const s=F.get("id"),c=F.get("code");if(s){console.log(`Processing transfer ID from URL: ${s}, code present: ${!!c}`);const n=c?J(c):"";E(s,n)}},[F,$]);const ne=s=>{const c=Math.floor(Date.now()/1e3),n=s-c;if(n<=0)return"Expired";const r=Math.floor(n/3600),o=Math.floor(n%3600/60);return`${r}h ${o}m`},U=async(s,c)=>{var n,r,o;if(!$)return a.error("Please connect your wallet to claim this transfer"),!1;A(!0),u("");try{const i=await S(s);if(console.log("Transfer requires password (from contract):",i),i){if(!c)return u("Claim code is required for this transfer"),!1;const x=J(c);console.log("Attempting to claim with password:",x,"length:",x.length);try{if(await D(s,x)){try{const f=await y(s);f?a.success("Password-protected transfer claimed successfully!",{description:`You have received ${f.amount} ${f.tokenSymbol}`}):a.success("Password-protected transfer claimed successfully!")}catch{a.success("Password-protected transfer claimed successfully!")}return g(!1),G(""),!0}return u("Failed to claim transfer. Please check the password."),!1}catch(l){return console.error("Error claiming transfer with password:",l),(n=l.message)!=null&&n.includes("InvalidClaimCode")||(r=l.message)!=null&&r.includes("invalid claim code")?(u("Invalid password. Please double-check and try again."),a.error("Invalid password",{description:"The password you entered does not match the transfer. Please check for typos or spaces."})):(o=l.message)!=null&&o.includes("already claimed")?(u("This transfer has already been claimed."),a.error("Already claimed",{description:"This transfer has already been claimed and cannot be claimed again."})):(u(`Error: ${l instanceof Error?l.message:"Unknown error"}`),a.error("Claim failed",{description:"An error occurred while claiming the transfer. Please try again."})),!1}}return a.info("This transfer does not require a password. Claiming directly..."),await D(s,"")?(a.success("Transfer claimed successfully!"),g(!1),G(""),!0):(u("Failed to claim transfer. Please check the transfer ID."),!1)}catch(i){return console.error("Error claiming transfer:",i),u(`Error: ${i instanceof Error?i.message:"Unknown error"}`),!1}finally{A(!1)}},M=async s=>{var c,n,r,o,i,p,x;if(!$)return a.error("Please connect your wallet to claim this transfer"),!1;q(!0);try{const l=await S(s);if(console.log("Transfer requires password (from contract):",l),l){const d=await y(s);return d?(N({id:s,sender:d.sender,recipient:d.recipient,tokenAddress:d.tokenAddress,tokenSymbol:d.tokenSymbol,amount:d.amount,expiry:d.expiry,status:d.status,createdAt:d.createdAt,isLinkTransfer:d.isLinkTransfer,passwordProtected:!0}),j(s),g(!0),a.info("This transfer requires a claim code. Please enter it to proceed."),!1):(a.error("This transfer requires a claim code. Please enter it to proceed."),!1)}if(a.info("Claiming transfer without password..."),await D(s,"")){try{const d=await y(s);d?a.success("Transfer claimed successfully!",{description:`You have received ${d.amount} ${d.tokenSymbol}`}):a.success("Transfer claimed successfully!")}catch{a.success("Transfer claimed successfully!")}return!0}return a.error("Claim failed",{description:"Could not claim transfer. Please check the transfer ID and try again."}),!1}catch(l){return console.error("Error claiming link transfer:",l),(c=l.message)!=null&&c.includes("rejected")||(n=l.message)!=null&&n.includes("denied")?a.error("Transaction cancelled",{description:"You cancelled the claim transaction"}):(r=l.message)!=null&&r.includes("insufficient funds")?a.error("Insufficient funds",{description:"You do not have enough funds to pay for transaction fees"}):(o=l.message)!=null&&o.includes("Invalid claim code")||(i=l.message)!=null&&i.includes("invalid password")?a.error("Invalid claim code",{description:"The claim code you entered is incorrect"}):(p=l.message)!=null&&p.includes("already claimed")||(x=l.message)!=null&&x.includes("not claimable")?a.error("Transfer not claimable",{description:"This transfer has already been claimed or is not available"}):a.error("Claim failed",{description:"Could not claim transfer. Please try again."}),!1}finally{q(!1)}},ie=s=>{N(s),K(!0)},ce=s=>{navigator.clipboard.writeText(`https://truststream.app/claim/${s}`),a.success("Link Copied",{description:"Transfer link copied to clipboard"})},oe=async s=>{var n,r,o,i,p,x,l;if(s.preventDefault(),!m){u("Transfer ID is required");return}if(await S(m)){if(!v){u("Claim code is required");return}await U(m,v)}else{A(!0);try{if(await D(m,"")){try{const d=await y(m);d?a.success("Transfer claimed successfully!",{description:`You have received ${d.amount} ${d.tokenSymbol}`}):a.success("Transfer claimed successfully!")}catch{a.success("Transfer claimed successfully!")}g(!1)}}catch(f){console.error("Error claiming transfer:",f),(n=f.message)!=null&&n.includes("rejected")||(r=f.message)!=null&&r.includes("denied")?u("Transaction cancelled. You cancelled the claim transaction."):(o=f.message)!=null&&o.includes("insufficient funds")?u("Insufficient funds. You do not have enough funds to pay for transaction fees."):(i=f.message)!=null&&i.includes("Invalid claim code")||(p=f.message)!=null&&p.includes("invalid password")?u("Invalid claim code. The claim code you entered is incorrect."):(x=f.message)!=null&&x.includes("already claimed")||(l=f.message)!=null&&l.includes("not claimable")?u("Transfer not claimable. This transfer has already been claimed or is not available."):u(`Error: ${f instanceof Error?f.message:"Unknown error"}`)}finally{A(!1)}}},le=async s=>{console.log("Scanned QR code in Claims page:",s);try{if(s.startsWith("http")){const r=new URL(s);if(r.pathname.includes("/claim/")){const x=r.pathname.split("/claim/")[1],f=new URLSearchParams(r.search).get("code");if(x){await E(x,f);return}}const o=new URLSearchParams(r.search),i=o.get("id")||o.get("transferId"),p=o.get("code")||o.get("claimCode");if(i!=null&&i.startsWith("0x")){await E(i,p);return}}if(s.startsWith("{")&&s.endsWith("}"))try{const r=JSON.parse(s);if(r.id||r.transferId){const o=r.id||r.transferId,i=r.code||r.claimCode||r.password;if(o!=null&&o.startsWith("0x")){await E(o,i);return}}}catch(r){console.error("Error parsing JSON from QR code:",r)}if(s.startsWith("0x")&&s.length===66){await E(s);return}const c=/0x[a-fA-F0-9]{64}/,n=s.match(c);if(n){const r=n[0];await E(r);return}a.error("Unrecognized QR code format. Please scan a valid transfer QR code.")}catch(c){console.error("Error processing QR code:",c),a.error("Invalid QR Code. Could not parse the QR code data")}},E=async(s,c)=>{var o;if(!s){a.error("Invalid transfer ID");return}let n=s;if(s.includes("/")&&(n=s.split("/").pop()||""),n.includes("?id=")&&(n=((o=n.split("?id=")[1])==null?void 0:o.split("&")[0])||""),console.log("Processing transfer ID:",n),!n||n.length!==66){a.error("Invalid transfer ID format");return}let r="";if(c)try{c.includes("%")?r=decodeURIComponent(c):r=c,r=J(r),console.log("Processed claim code:",r)}catch(i){console.error("Error decoding claim code:",i),r=c}try{const i=await S(n);console.log("Transfer is password protected:",i);const p=await y(n);if(!p){a.error("Transfer not found or has expired"),j(""),b("");return}N({...p,passwordProtected:i}),i?(j(n),r?(b(r),g(!0),n&&r&&(console.log("Auto-attempting claim with provided password..."),setTimeout(()=>{U(n,r)},500))):(g(!0),a.info("This transfer requires a password for claiming."))):(a.info("Attempting to claim transfer without password..."),await M(n))}catch(i){console.error("Error processing transfer ID:",i),a.error("Error processing transfer",{description:i instanceof Error?i.message:"Unknown error"})}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(w,{variant:"ghost",size:"sm",onClick:()=>P(-1),className:"mr-4 p-0 h-auto",children:e.jsx(Pe,{className:"h-5 w-5"})}),e.jsx("h1",{className:"text-xl font-semibold",children:"Claim Transfers"}),e.jsxs("div",{className:"ml-auto flex gap-2",children:[e.jsxs(w,{variant:"outline",size:"sm",onClick:()=>C(!0),children:[e.jsx(_,{className:"h-4 w-4 mr-1"}),"Claim New"]}),e.jsx(pe,{buttonVariant:"outline",buttonSize:"sm",buttonText:"Scan QR",onScanSuccess:le})]})]}),X.length>0?e.jsx("div",{className:"space-y-4",children:X.map(s=>e.jsxs(we,{children:[e.jsx(ge,{className:"pb-2",children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs(je,{className:"text-base flex items-center",children:[e.jsx(ee,{className:"mr-2 h-5 w-5 text-primary"}),"Protected Transfer"]}),e.jsxs("div",{className:"flex items-center gap-2",children:[s.passwordProtected&&e.jsxs(be,{variant:"secondary",className:"flex items-center gap-1",children:[e.jsx(R,{className:"h-3 w-3"}),e.jsx("span",{children:"Password"})]}),e.jsxs("div",{className:"flex items-center text-sm text-muted-foreground",children:[e.jsx(ye,{className:"mr-1 h-4 w-4"}),e.jsx("span",{children:ne(s.expiresAt)})]})]})]})}),e.jsx(Ne,{children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"From:"}),e.jsx("span",{className:"font-medium",children:s.sender})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Amount:"}),e.jsxs("span",{className:"font-medium",children:[s.amount," SEI"]})]}),s.note&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Note:"}),e.jsx("span",{className:"font-medium",children:s.note})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Expires:"}),e.jsx("span",{className:"font-medium",children:s.expiresAt.toLocaleString()})]})]})}),e.jsxs(Ce,{className:"flex flex-col space-y-2",children:[e.jsx(w,{onClick:async()=>{s.passwordProtected?(N(s),j(s.id),g(!0)):(a.info("Claiming transfer without password..."),await M(s.id))},className:"w-full",children:s.passwordProtected?e.jsxs(e.Fragment,{children:[e.jsx(R,{className:"h-4 w-4 mr-1"})," Claim (Password Protected)"]}):e.jsxs(e.Fragment,{children:[e.jsx(Check,{className:"h-4 w-4 mr-1"})," Claim Transfer (No Password)"]})}),e.jsxs("div",{className:"flex w-full gap-2",children:[e.jsxs(w,{variant:"outline",className:"flex-1",onClick:()=>ce(s.id),children:[e.jsx(ve,{className:"h-4 w-4 mr-1"})," Copy Link"]}),e.jsxs(w,{variant:"outline",className:"flex-1",onClick:()=>ie(s),children:[e.jsx(Te,{className:"h-4 w-4 mr-1"})," Show QR"]})]})]})]},s.id))}):e.jsxs("div",{className:"text-center p-8",children:[e.jsx(ee,{className:"h-12 w-12 text-muted-foreground mx-auto mb-3"}),e.jsx("h3",{className:"font-medium mb-1",children:"No Pending Claims"}),e.jsx("p",{className:"text-sm text-muted-foreground mb-4",children:`You don't have any pending transfers to claim. Use the "Claim New" button to claim a transfer using its ID and claim code, or scan a QR code.`}),e.jsxs("div",{className:"flex gap-2 justify-center",children:[e.jsxs(w,{variant:"outline",onClick:()=>C(!0),children:[e.jsx(_,{className:"h-4 w-4 mr-1"}),"Claim New Transfer"]}),e.jsx(w,{onClick:()=>P("/app"),children:"Return to Dashboard"})]})]}),e.jsx(Y,{open:se,onOpenChange:K,children:e.jsxs(z,{children:[e.jsx(W,{children:e.jsx(O,{children:"Transfer QR Code"})}),e.jsx("div",{className:"flex flex-col items-center justify-center space-y-4",children:t&&e.jsxs(e.Fragment,{children:[e.jsx(ke,{value:`https://truststream.app/claim/${t.id}`,size:200}),e.jsxs("p",{className:"text-sm text-center text-muted-foreground",children:["Share this QR code to claim ",t.amount," SEI"]})]})})]})}),e.jsx(Y,{open:re,onOpenChange:g,children:e.jsxs(z,{children:[e.jsx(W,{children:e.jsx(O,{children:t!=null&&t.passwordProtected?"Enter Claim Code":"Claim Transfer (No Password Required)"})}),e.jsx("form",{onSubmit:oe,children:e.jsxs("div",{className:"space-y-4",children:[t?e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-secondary p-3 rounded-lg",children:[e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"From:"}),e.jsx("span",{className:"font-medium",children:t.sender&&t.sender.length>12?`${t.sender.slice(0,6)}...${t.sender.slice(-4)}`:t.sender})]}),e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Amount:"}),e.jsxs("span",{className:"font-medium",children:[t.amount," ",t.tokenSymbol]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Transfer ID:"}),e.jsx("span",{className:"font-mono text-xs",children:te(t.id)})]}),t.expiry&&e.jsxs("div",{className:"flex justify-between mt-2",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Expires:"}),e.jsx("span",{className:"font-medium",children:new Date(t.expiry*1e3).toLocaleString()})]})]}),t.passwordProtected&&e.jsx("div",{className:"text-center",children:e.jsxs("p",{className:"text-sm text-amber-500 font-medium flex items-center justify-center",children:[e.jsx(R,{className:"h-4 w-4 mr-1"}),"This transfer requires a claim code"]})})]}):e.jsx("div",{className:"text-center mb-4",children:e.jsx("p",{className:"text-sm text-muted-foreground",children:"Enter the claim code to claim this transfer"})}),(!t||t.passwordProtected)&&e.jsxs("div",{className:"space-y-2",children:[e.jsx(H,{htmlFor:"claimCode",children:"Claim Code"}),e.jsx(B,{id:"claimCode",value:v,onChange:s=>b(s.target.value),placeholder:"Enter the claim code",className:Q?"border-red-500":"",disabled:I}),Q&&e.jsx("p",{className:"text-sm text-red-500",children:Q})]}),e.jsx(w,{type:"submit",className:"w-full",disabled:I||(t==null?void 0:t.passwordProtected)&&!v,children:I?e.jsxs(e.Fragment,{children:[e.jsx(V,{className:"mr-2 h-4 w-4 animate-spin"}),"Claiming..."]}):t!=null&&t.passwordProtected?"Claim Transfer":"Claim Transfer (No Password Required)"})]})})]})}),e.jsx(Y,{open:ae,onOpenChange:C,children:e.jsxs(z,{children:[e.jsx(W,{children:e.jsx(O,{children:"Claim a Transfer"})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:"Enter the transfer ID and claim code (if required) to claim a transfer"}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(H,{htmlFor:"transferId",children:"Transfer ID"}),e.jsx(B,{id:"transferId",value:m,onChange:s=>j(s.target.value),placeholder:"Enter transfer ID"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(H,{htmlFor:"manualClaimCode",children:"Claim Code (only for password-protected transfers)"}),e.jsx(B,{id:"manualClaimCode",value:v,onChange:s=>b(s.target.value),placeholder:"Only needed for password-protected transfers"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"If the transfer was created without password protection, you can leave this field empty."})]}),e.jsx(w,{variant:"outline",className:"w-full",onClick:async()=>{if(!m){k("Transfer ID is required");return}q(!0);try{const s=await y(m);s?(N({id:m,sender:s.sender,recipient:s.recipient,tokenAddress:s.tokenAddress,tokenSymbol:s.tokenSymbol,amount:s.amount,expiry:s.expiry,status:s.status,createdAt:s.createdAt,isLinkTransfer:s.isLinkTransfer,passwordProtected:await S(m)}),a.success("Transfer details loaded")):k("Transfer not found. Please check the ID and try again.")}catch(s){console.error("Error fetching transfer details:",s),k(`Error: ${s instanceof Error?s.message:"Unknown error"}`)}finally{q(!1)}},disabled:L||!m,children:L?e.jsxs(e.Fragment,{children:[e.jsx(V,{className:"mr-2 h-4 w-4 animate-spin"}),"Loading..."]}):"Check Transfer Details"}),t&&e.jsxs("div",{className:"bg-secondary p-3 rounded-lg",children:[e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"From:"}),e.jsx("span",{className:"font-medium",children:t.sender&&t.sender.length>12?`${t.sender.slice(0,6)}...${t.sender.slice(-4)}`:t.sender})]}),e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Amount:"}),e.jsxs("span",{className:"font-medium",children:[t.amount," ",t.tokenSymbol]})]}),t.expiry&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Expires:"}),e.jsx("span",{className:"font-medium",children:new Date(t.expiry*1e3).toLocaleString()})]}),t.passwordProtected&&e.jsx("div",{className:"mt-2 pt-2 border-t border-border",children:e.jsxs("p",{className:"text-sm text-amber-500 font-medium flex items-center",children:[e.jsx(R,{className:"h-4 w-4 mr-1"}),"This transfer requires a claim code"]})})]}),Z&&e.jsx("p",{className:"text-sm text-red-500",children:Z}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(w,{variant:"outline",className:"flex-1",onClick:()=>C(!1),children:"Cancel"}),e.jsx(w,{className:"flex-1",onClick:async()=>{if(k(""),!m){k("Transfer ID is required");return}a.info("Checking if this transfer requires a password...");try{const s=await y(m);if(!s){k("Transfer not found. Please check the ID and try again.");return}const c=await S(m);if(console.log("Transfer requires password:",c),c){if(!v){N({id:m,sender:s.sender,recipient:s.recipient,tokenAddress:s.tokenAddress,tokenSymbol:s.tokenSymbol,amount:s.amount,expiry:s.expiry,status:s.status,createdAt:s.createdAt,isLinkTransfer:s.isLinkTransfer,passwordProtected:!0}),a.info("This transfer requires a claim code. Please enter it to proceed."),C(!1),g(!0);return}await U(m,v)&&(C(!1),j(""),b(""));return}if(N({id:m,sender:s.sender,recipient:s.recipient,tokenAddress:s.tokenAddress,tokenSymbol:s.tokenSymbol,amount:s.amount,expiry:s.expiry,status:s.status,createdAt:s.createdAt,isLinkTransfer:s.isLinkTransfer,passwordProtected:!1}),a.info("This transfer does not require a password. Claiming directly..."),await D(m,"")){try{const r=await y(m);r?a.success("Transfer claimed successfully! (No password was required)",{description:`You have received ${r.amount} ${r.tokenSymbol}`}):a.success("Transfer claimed successfully! (No password was required)")}catch{a.success("Transfer claimed successfully! (No password was required)")}C(!1),j(""),b("")}}catch(s){console.error("Error in manual claim:",s),k(`Error: ${s instanceof Error?s.message:"Unknown error"}`)}},disabled:L||I,children:L||I?e.jsxs(e.Fragment,{children:[e.jsx(V,{className:"mr-2 h-4 w-4 animate-spin"}),"Claiming..."]}):"Claim Transfer"})]})]})]})})]})};export{Ue as default};
