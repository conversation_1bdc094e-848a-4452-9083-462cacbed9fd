import{bH as Ke,bI as hr,bJ as mr,bK as br,bg as O,bL as Qe,bM as gr,bn as yt,bN as te,bO as Pe,bP as F,bQ as ee,bl as Fe,bR as yr,bS as j,bT as P,bU as de,bV as V,bW as Ye,bX as Z,bY as wt,bZ as I,b_ as _,b$ as $e,c0 as wr,c1 as et,c2 as vr,c3 as vt,c4 as R,c5 as Et,c6 as le,c7 as Er,c8 as Ar,b4 as At,c9 as tt,b1 as xr,ca as xt,b7 as pe,cb as It,cc as Tt,cd as Nt,N as Le,ce as Ir,cf as Oe,cg as ze,ch as M,ci as he,cj as kt,ck as Tr,cl as Nr,cm as kr,cn as Sr,co as Br,cp as Cr,cq as Rr,cr as Pr,cs as Fr,ct as De,cu as Ue,cv as Ie,cw as G,cx as J,cy as D,cz as re,cA as St,cB as $r,bi as W,cC as Lr,be as Or,bf as zr,cD as Dr,cE as Te,cF as Ur,cG as rt,cH as _r,b8 as Mr,cI as Bt,cJ as ue,cK as Hr,cL as ne,cM as me,cN as Y,cO as Ct,cP as nt,cQ as qr,cR as jr,bk as Rt,cS as Gr,cT as Wr,cU as Vr,cV as Zr,cW as Jr,cX as Xr,cY as Kr,cZ as Qr,c_ as Yr,c$ as en,d0 as tn,d1 as rn,d2 as nn,d3 as sn,d4 as an,d5 as on,d6 as cn,d7 as un}from"./index-G17GlXLb.js";import{dw as Oa,dx as za,dy as Da,dz as Ua,dB as _a,dA as Ma,b6 as Ha,b5 as qa,dC as ja,dD as Ga,b9 as Wa,ba as Va,b2 as Za,eM as Ja,dO as Xa,dP as Ka,dL as Qa,dH as Ya,dQ as eo,dR as to,ed as ro,ee as no,ef as so,d8 as ao,eg as oo,dI as io,dJ as co,dK as uo,dS as fo,dM as lo,en as po,eo as ho,ep as mo,eq as bo,eB as go,bm as yo,er as wo,ej as vo,dT as Eo,es as Ao,et as xo,dE as Io,dF as To,d9 as No,bb as ko,ei as So,eh as Bo,em as Co,dG as Ro,da as Po,ek as Fo,eC as $o,db as Lo,dc as Oo,dU as zo,dd as Do,dV as Uo,eD as _o,de as Mo,eE as Ho,df as qo,dW as jo,dX as Go,dN as Wo,dY as Vo,dZ as Zo,eu as Jo,ev as Xo,ew as Ko,d_ as Qo,d$ as Yo,e0 as ei,e1 as ti,e2 as ri,e3 as ni,ez as si,eJ as ai,el as oi,eK as ii,dg as ci,eN as ui,e4 as fi,eA as di,ex as li,eF as pi,eG as hi,eH as mi,e5 as bi,ey as gi,e6 as yi,e7 as wi,e8 as vi,di as Ei,dh as Ai,e9 as xi,ea as Ii,eb as Ti,eL as Ni,ec as ki,eI as Si,fe as Bi,eZ as Ci,e_ as Ri,e$ as Pi,f7 as Fi,fa as $i,f0 as Li,f2 as Oi,f3 as zi,f4 as Di,f5 as Ui,f6 as _i,fm as Mi,f8 as Hi,f9 as qi,fd as ji,dp as Gi,dk as Wi,b3 as Vi,bj as Zi,eO as Ji,ff as Xi,eQ as Ki,eT as Qi,dr as Yi,ds as ec,dl as tc,fj as rc,fk as nc,eR as sc,eU as ac,a3 as oc,fn as ic,b0 as cc,fn as uc,eW as fc,dt as dc,fl as lc,dn as pc,dv as hc,dq as mc,f1 as bc,fq as gc,fr as yc,fs as wc,dj as vc,U as Ec,eP as Ac,eX as xc,eV as Ic,ft as Tc,fu as Nc,fp as kc,dm as Sc,a$ as Bc,fv as Cc,fb as Rc,fc as Pc,fo as Fc,fn as $c,fo as Lc,b0 as Oc,fn as zc,eY as Dc,eS as Uc,du as _c,fg as Mc,fh as Hc,fi as qc}from"./index-G17GlXLb.js";import{l as Pt,i as _e}from"./ccip-BBRczgY0.js";import{c as Gc,c as Wc,d as Vc,e as Zc,a as Jc,o as Xc,b as Kc,f as Qc}from"./ccip-BBRczgY0.js";import{secp256k1 as fn}from"./secp256k1-em1ckmQy.js";function st(e){let t;if(typeof e=="string")t=Ke(e);else{const r=hr(e),n=e.length;for(let s=0;s<n;s++){const a=e[s];if(!mr(a)){t=Ke(a,r);break}}}if(!t)throw new br({signature:e});return t}function Me(e,t){var n,s,a,o,i,u;if(!(e instanceof O))return!1;const r=e.walk(c=>c instanceof Qe);return r instanceof Qe?!!(((n=r.data)==null?void 0:n.errorName)==="ResolverNotFound"||((s=r.data)==null?void 0:s.errorName)==="ResolverWildcardNotSupported"||((a=r.data)==null?void 0:a.errorName)==="ResolverNotContract"||((o=r.data)==null?void 0:o.errorName)==="ResolverError"||((i=r.data)==null?void 0:i.errorName)==="HttpError"||(u=r.reason)!=null&&u.includes("Wildcard on non-extended resolvers is not supported")||t==="reverse"&&r.reason===gr[50]):!1}function Ft(e){if(e.length!==66||e.indexOf("[")!==0||e.indexOf("]")!==65)return null;const t=`0x${e.slice(1,65)}`;return yt(t)?t:null}function ce(e){let t=new Uint8Array(32).fill(0);if(!e)return te(t);const r=e.split(".");for(let n=r.length-1;n>=0;n-=1){const s=Ft(r[n]),a=s?Pe(s):F(ee(r[n]),"bytes");t=F(Fe([t,a]),"bytes")}return te(t)}function dn(e){return`[${e.slice(2)}]`}function ln(e){const t=new Uint8Array(32).fill(0);return e?Ft(e)||F(ee(e)):te(t)}function be(e){const t=e.replace(/^\.|\.$/gm,"");if(t.length===0)return new Uint8Array(1);const r=new Uint8Array(ee(t).byteLength+2);let n=0;const s=t.split(".");for(let a=0;a<s.length;a++){let o=ee(s[a]);o.byteLength>255&&(o=ee(dn(ln(s[a])))),r[n]=o.length,r.set(o,n+1),n+=o.length+1}return r.byteLength!==n+1?r.slice(0,n+1):r}function pn(e,t){if(ot(e)>t)throw new En({givenSize:ot(e),maxSize:t})}const U={zero:48,nine:57,A:65,F:70,a:97,f:102};function at(e){if(e>=U.zero&&e<=U.nine)return e-U.zero;if(e>=U.A&&e<=U.F)return e-(U.A-10);if(e>=U.a&&e<=U.f)return e-(U.a-10)}function hn(e,t={}){const{dir:r,size:n=32}=t;if(n===0)return e;if(e.length>n)throw new An({size:e.length,targetSize:n,type:"Bytes"});const s=new Uint8Array(n);for(let a=0;a<n;a++){const o=r==="right";s[o?a:n-a-1]=e[o?a:e.length-a-1]}return s}const mn=new TextEncoder;function bn(e){return e instanceof Uint8Array?e:typeof e=="string"?yn(e):gn(e)}function gn(e){return e instanceof Uint8Array?e:new Uint8Array(e)}function yn(e,t={}){const{size:r}=t;let n=e;r&&(yr(e,r),n=j(e,r));let s=n.slice(2);s.length%2&&(s=`0${s}`);const a=s.length/2,o=new Uint8Array(a);for(let i=0,u=0;i<a;i++){const c=at(s.charCodeAt(u++)),d=at(s.charCodeAt(u++));if(c===void 0||d===void 0)throw new P(`Invalid byte sequence ("${s[u-2]}${s[u-1]}" in "${s}").`);o[i]=c*16+d}return o}function wn(e,t={}){const{size:r}=t,n=mn.encode(e);return typeof r=="number"?(pn(n,r),vn(n,r)):n}function vn(e,t){return hn(e,{dir:"right",size:t})}function ot(e){return e.length}class En extends P{constructor({givenSize:t,maxSize:r}){super(`Size cannot exceed \`${r}\` bytes. Given size: \`${t}\` bytes.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Bytes.SizeOverflowError"})}}class An extends P{constructor({size:t,targetSize:r,type:n}){super(`${n.charAt(0).toUpperCase()}${n.slice(1).toLowerCase()} size (\`${t}\`) exceeds padding size (\`${r}\`).`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Bytes.SizeExceedsPaddingSizeError"})}}async function xn(e,t){const{blockNumber:r,blockTag:n,coinType:s,name:a,gatewayUrls:o,strict:i}=t,{chain:u}=e,c=(()=>{if(t.universalResolverAddress)return t.universalResolverAddress;if(!u)throw new Error("client chain not configured. universalResolverAddress is required.");return de({blockNumber:r,chain:u,contract:"ensUniversalResolver"})})(),d=u==null?void 0:u.ensTlds;if(d&&!d.some(f=>a.endsWith(f)))return null;try{const f=V({abi:Ye,functionName:"addr",...s!=null?{args:[ce(a),BigInt(s)]}:{args:[ce(a)]}}),l={address:c,abi:wt,functionName:"resolve",args:[Z(be(a)),f,o??[Pt]],blockNumber:r,blockTag:n},m=await I(e,_,"readContract")(l);if(m[0]==="0x")return null;const y=$e({abi:Ye,args:s!=null?[ce(a),BigInt(s)]:void 0,functionName:"addr",data:m[0]});return y==="0x"||wr(y)==="0x00"?null:y}catch(f){if(i)throw f;if(Me(f,"resolve"))return null;throw f}}class In extends O{constructor({data:t}){super("Unable to extract image from metadata. The metadata may be malformed or invalid.",{metaMessages:["- Metadata must be a JSON object with at least an `image`, `image_url` or `image_data` property.","",`Provided data: ${JSON.stringify(t)}`],name:"EnsAvatarInvalidMetadataError"})}}class K extends O{constructor({reason:t}){super(`ENS NFT avatar URI is invalid. ${t}`,{name:"EnsAvatarInvalidNftUriError"})}}class He extends O{constructor({uri:t}){super(`Unable to resolve ENS avatar URI "${t}". The URI may be malformed, invalid, or does not respond with a valid image.`,{name:"EnsAvatarUriResolutionError"})}}class Tn extends O{constructor({namespace:t}){super(`ENS NFT avatar namespace "${t}" is not supported. Must be "erc721" or "erc1155".`,{name:"EnsAvatarUnsupportedNamespaceError"})}}const Nn=/(?<protocol>https?:\/\/[^\/]*|ipfs:\/|ipns:\/|ar:\/)?(?<root>\/)?(?<subpath>ipfs\/|ipns\/)?(?<target>[\w\-.]+)(?<subtarget>\/.*)?/,kn=/^(Qm[1-9A-HJ-NP-Za-km-z]{44,}|b[A-Za-z2-7]{58,}|B[A-Z2-7]{58,}|z[1-9A-HJ-NP-Za-km-z]{48,}|F[0-9A-F]{50,})(\/(?<target>[\w\-.]+))?(?<subtarget>\/.*)?$/,Sn=/^data:([a-zA-Z\-/+]*);base64,([^"].*)/,Bn=/^data:([a-zA-Z\-/+]*)?(;[a-zA-Z0-9].*?)?(,)/;async function Cn(e){try{const t=await fetch(e,{method:"HEAD"});if(t.status===200){const r=t.headers.get("content-type");return r==null?void 0:r.startsWith("image/")}return!1}catch(t){return typeof t=="object"&&typeof t.response<"u"||!globalThis.hasOwnProperty("Image")?!1:new Promise(r=>{const n=new Image;n.onload=()=>{r(!0)},n.onerror=()=>{r(!1)},n.src=e})}}function it(e,t){return e?e.endsWith("/")?e.slice(0,-1):e:t}function $t({uri:e,gatewayUrls:t}){const r=Sn.test(e);if(r)return{uri:e,isOnChain:!0,isEncoded:r};const n=it(t==null?void 0:t.ipfs,"https://ipfs.io"),s=it(t==null?void 0:t.arweave,"https://arweave.net"),a=e.match(Nn),{protocol:o,subpath:i,target:u,subtarget:c=""}=(a==null?void 0:a.groups)||{},d=o==="ipns:/"||i==="ipns/",f=o==="ipfs:/"||i==="ipfs/"||kn.test(e);if(e.startsWith("http")&&!d&&!f){let p=e;return t!=null&&t.arweave&&(p=e.replace(/https:\/\/arweave.net/g,t==null?void 0:t.arweave)),{uri:p,isOnChain:!1,isEncoded:!1}}if((d||f)&&u)return{uri:`${n}/${d?"ipns":"ipfs"}/${u}${c}`,isOnChain:!1,isEncoded:!1};if(o==="ar:/"&&u)return{uri:`${s}/${u}${c||""}`,isOnChain:!1,isEncoded:!1};let l=e.replace(Bn,"");if(l.startsWith("<svg")&&(l=`data:image/svg+xml;base64,${btoa(l)}`),l.startsWith("data:")||l.startsWith("{"))return{uri:l,isOnChain:!0,isEncoded:!1};throw new He({uri:e})}function Lt(e){if(typeof e!="object"||!("image"in e)&&!("image_url"in e)&&!("image_data"in e))throw new In({data:e});return e.image||e.image_url||e.image_data}async function Rn({gatewayUrls:e,uri:t}){try{const r=await fetch(t).then(s=>s.json());return await qe({gatewayUrls:e,uri:Lt(r)})}catch{throw new He({uri:t})}}async function qe({gatewayUrls:e,uri:t}){const{uri:r,isOnChain:n}=$t({uri:t,gatewayUrls:e});if(n||await Cn(r))return r;throw new He({uri:t})}function Pn(e){let t=e;t.startsWith("did:nft:")&&(t=t.replace("did:nft:","").replace(/_/g,"/"));const[r,n,s]=t.split("/"),[a,o]=r.split(":"),[i,u]=n.split(":");if(!a||a.toLowerCase()!=="eip155")throw new K({reason:"Only EIP-155 supported"});if(!o)throw new K({reason:"Chain ID not found"});if(!u)throw new K({reason:"Contract address not found"});if(!s)throw new K({reason:"Token ID not found"});if(!i)throw new K({reason:"ERC namespace not found"});return{chainID:Number.parseInt(o),namespace:i.toLowerCase(),contractAddress:u,tokenID:s}}async function Fn(e,{nft:t}){if(t.namespace==="erc721")return _(e,{address:t.contractAddress,abi:[{name:"tokenURI",type:"function",stateMutability:"view",inputs:[{name:"tokenId",type:"uint256"}],outputs:[{name:"",type:"string"}]}],functionName:"tokenURI",args:[BigInt(t.tokenID)]});if(t.namespace==="erc1155")return _(e,{address:t.contractAddress,abi:[{name:"uri",type:"function",stateMutability:"view",inputs:[{name:"_id",type:"uint256"}],outputs:[{name:"",type:"string"}]}],functionName:"uri",args:[BigInt(t.tokenID)]});throw new Tn({namespace:t.namespace})}async function $n(e,{gatewayUrls:t,record:r}){return/eip155:/i.test(r)?Ln(e,{gatewayUrls:t,record:r}):qe({uri:r,gatewayUrls:t})}async function Ln(e,{gatewayUrls:t,record:r}){const n=Pn(r),s=await Fn(e,{nft:n}),{uri:a,isOnChain:o,isEncoded:i}=$t({uri:s,gatewayUrls:t});if(o&&(a.includes("data:application/json;base64,")||a.startsWith("{"))){const c=i?atob(a.replace("data:application/json;base64,","")):a,d=JSON.parse(c);return qe({uri:Lt(d),gatewayUrls:t})}let u=n.tokenID;return n.namespace==="erc1155"&&(u=u.replace("0x","").padStart(64,"0")),Rn({gatewayUrls:t,uri:a.replace(/(?:0x)?{id}/,u)})}async function Ot(e,t){const{blockNumber:r,blockTag:n,key:s,name:a,gatewayUrls:o,strict:i}=t,{chain:u}=e,c=(()=>{if(t.universalResolverAddress)return t.universalResolverAddress;if(!u)throw new Error("client chain not configured. universalResolverAddress is required.");return de({blockNumber:r,chain:u,contract:"ensUniversalResolver"})})(),d=u==null?void 0:u.ensTlds;if(d&&!d.some(f=>a.endsWith(f)))return null;try{const f={address:c,abi:wt,functionName:"resolve",args:[Z(be(a)),V({abi:et,functionName:"text",args:[ce(a),s]}),o??[Pt]],blockNumber:r,blockTag:n},p=await I(e,_,"readContract")(f);if(p[0]==="0x")return null;const m=$e({abi:et,functionName:"text",data:p[0]});return m===""?null:m}catch(f){if(i)throw f;if(Me(f,"resolve"))return null;throw f}}async function On(e,{blockNumber:t,blockTag:r,assetGatewayUrls:n,name:s,gatewayUrls:a,strict:o,universalResolverAddress:i}){const u=await I(e,Ot,"getEnsText")({blockNumber:t,blockTag:r,key:"avatar",name:s,universalResolverAddress:i,gatewayUrls:a,strict:o});if(!u)return null;try{return await $n(e,{record:u,gatewayUrls:n})}catch{return null}}async function zn(e,{address:t,blockNumber:r,blockTag:n,gatewayUrls:s,strict:a,universalResolverAddress:o}){let i=o;if(!i){if(!e.chain)throw new Error("client chain not configured. universalResolverAddress is required.");i=de({blockNumber:r,chain:e.chain,contract:"ensUniversalResolver"})}const u=`${t.toLowerCase().substring(2)}.addr.reverse`;try{const c={address:i,abi:vr,functionName:"reverse",args:[Z(be(u))],blockNumber:r,blockTag:n},d=I(e,_,"readContract"),[f,l]=s?await d({...c,args:[...c.args,s]}):await d(c);return t.toLowerCase()!==l.toLowerCase()?null:f}catch(c){if(a)throw c;if(Me(c,"reverse"))return null;throw c}}async function Dn(e,t){const{blockNumber:r,blockTag:n,name:s}=t,{chain:a}=e,o=(()=>{if(t.universalResolverAddress)return t.universalResolverAddress;if(!a)throw new Error("client chain not configured. universalResolverAddress is required.");return de({blockNumber:r,chain:a,contract:"ensUniversalResolver"})})(),i=a==null?void 0:a.ensTlds;if(i&&!i.some(c=>s.endsWith(c)))throw new Error(`${s} is not a valid ENS TLD (${i==null?void 0:i.join(", ")}) for chain "${a.name}" (id: ${a.id}).`);const[u]=await I(e,_,"readContract")({address:o,abi:[{inputs:[{type:"bytes"}],name:"findResolver",outputs:[{type:"address"},{type:"bytes32"}],stateMutability:"view",type:"function"}],functionName:"findResolver",args:[Z(be(s))],blockNumber:r,blockTag:n});return u}async function zt(e,t){var v,h,w;const{account:r=e.account,blockNumber:n,blockTag:s="latest",blobs:a,data:o,gas:i,gasPrice:u,maxFeePerBlobGas:c,maxFeePerGas:d,maxPriorityFeePerGas:f,to:l,value:p,...m}=t,y=r?le(r):void 0;try{vt(t);const E=(typeof n=="bigint"?R(n):void 0)||s,N=(w=(h=(v=e.chain)==null?void 0:v.formatters)==null?void 0:h.transactionRequest)==null?void 0:w.format,A=(N||Et)({...Er(m,{format:N}),from:y==null?void 0:y.address,blobs:a,data:o,gas:i,gasPrice:u,maxFeePerBlobGas:c,maxFeePerGas:d,maxPriorityFeePerGas:f,to:l,value:p}),x=await e.request({method:"eth_createAccessList",params:[A,E]});return{accessList:x.accessList,gasUsed:BigInt(x.gasUsed)}}catch(g){throw Ar(g,{...t,account:y,chain:e.chain})}}function ge(e,{method:t}){var n,s;const r={};return e.transport.type==="fallback"&&((s=(n=e.transport).onResponse)==null||s.call(n,({method:a,response:o,status:i,transport:u})=>{i==="success"&&t===a&&(r[o]=u.request)})),a=>r[a]||e.request}async function Un(e){const t=ge(e,{method:"eth_newBlockFilter"}),r=await e.request({method:"eth_newBlockFilter"});return{id:r,request:t(r),type:"block"}}class _n extends O{constructor(t){super(`Filter type "${t}" is not supported.`,{name:"FilterTypeNotSupportedError"})}}const ct="/docs/contract/encodeEventTopics";function se(e){var u;const{abi:t,eventName:r,args:n}=e;let s=t[0];if(r){const c=At({abi:t,name:r});if(!c)throw new tt(r,{docsPath:ct});s=c}if(s.type!=="event")throw new tt(void 0,{docsPath:ct});const a=xr(s),o=xt(a);let i=[];if(n&&"inputs"in s){const c=(u=s.inputs)==null?void 0:u.filter(f=>"indexed"in f&&f.indexed),d=Array.isArray(n)?n:Object.values(n).length>0?(c==null?void 0:c.map(f=>n[f.name]))??[]:[];d.length>0&&(i=(c==null?void 0:c.map((f,l)=>Array.isArray(d[l])?d[l].map((p,m)=>ut({param:f,value:d[l][m]})):typeof d[l]<"u"&&d[l]!==null?ut({param:f,value:d[l]}):null))??[])}return[o,...i]}function ut({param:e,value:t}){if(e.type==="string"||e.type==="bytes")return F(Pe(t));if(e.type==="tuple"||e.type.match(/^(.*)\[(\d+)?\]$/))throw new _n(e.type);return pe([e],[t])}async function Dt(e,t){const{address:r,abi:n,args:s,eventName:a,fromBlock:o,strict:i,toBlock:u}=t,c=ge(e,{method:"eth_newFilter"}),d=a?se({abi:n,args:s,eventName:a}):void 0,f=await e.request({method:"eth_newFilter",params:[{address:r,fromBlock:typeof o=="bigint"?R(o):o,toBlock:typeof u=="bigint"?R(u):u,topics:d}]});return{abi:n,args:s,eventName:a,id:f,request:c(f),strict:!!i,type:"event"}}async function Ut(e,{address:t,args:r,event:n,events:s,fromBlock:a,strict:o,toBlock:i}={}){const u=s??(n?[n]:void 0),c=ge(e,{method:"eth_newFilter"});let d=[];u&&(d=[u.flatMap(p=>se({abi:[p],eventName:p.name,args:r}))],n&&(d=d[0]));const f=await e.request({method:"eth_newFilter",params:[{address:t,fromBlock:typeof a=="bigint"?R(a):a,toBlock:typeof i=="bigint"?R(i):i,...d.length?{topics:d}:{}}]});return{abi:u,args:r,eventName:n?n.name:void 0,fromBlock:a,id:f,request:c(f),strict:!!o,toBlock:i,type:"event"}}async function _t(e){const t=ge(e,{method:"eth_newPendingTransactionFilter"}),r=await e.request({method:"eth_newPendingTransactionFilter"});return{id:r,request:t(r),type:"transaction"}}async function Mn(e,t){const{abi:r,address:n,args:s,functionName:a,dataSuffix:o,...i}=t,u=V({abi:r,args:s,functionName:a});try{return await I(e,It,"estimateGas")({data:`${u}${o?o.replace("0x",""):""}`,to:n,...i})}catch(c){const d=i.account?le(i.account):void 0;throw Tt(c,{abi:r,address:n,args:s,docsPath:"/docs/contract/estimateContractGas",functionName:a,sender:d==null?void 0:d.address})}}async function Hn(e){const t=await e.request({method:"eth_blobBaseFee"});return BigInt(t)}async function qn(e,{blockHash:t,blockNumber:r,blockTag:n="latest"}={}){const s=r!==void 0?R(r):void 0;let a;return t?a=await e.request({method:"eth_getBlockTransactionCountByHash",params:[t]},{dedupe:!0}):a=await e.request({method:"eth_getBlockTransactionCountByNumber",params:[s||n]},{dedupe:!!s}),Nt(a)}async function ft(e,{address:t,blockNumber:r,blockTag:n="latest"}){const s=r!==void 0?R(r):void 0,a=await e.request({method:"eth_getCode",params:[t,s||n]},{dedupe:!!s});if(a!=="0x")return a}function je(e){const{abi:t,args:r,logs:n,strict:s=!0}=e,a=(()=>{if(e.eventName)return Array.isArray(e.eventName)?e.eventName:[e.eventName]})();return n.map(o=>{var i;try{const u=t.find(d=>d.type==="event"&&o.topics[0]===xt(d));if(!u)return null;const c=Le({...o,abi:[u],strict:s});return a&&!a.includes(c.eventName)||!jn({args:c.args,inputs:u.inputs,matchArgs:r})?null:{...c,...o}}catch(u){let c,d;if(u instanceof Ir)return null;if(u instanceof Oe||u instanceof ze){if(s)return null;c=u.abiItem.name,d=(i=u.abiItem.inputs)==null?void 0:i.some(f=>!("name"in f&&f.name))}return{...o,args:d?[]:{},eventName:c}}}).filter(Boolean)}function jn(e){const{args:t,inputs:r,matchArgs:n}=e;if(!n)return!0;if(!t)return!1;function s(a,o,i){try{return a.type==="address"?_e(o,i):a.type==="string"||a.type==="bytes"?F(Pe(o))===i:o===i}catch{return!1}}return Array.isArray(t)&&Array.isArray(n)?n.every((a,o)=>{if(a==null)return!0;const i=r[o];return i?(Array.isArray(a)?a:[a]).some(c=>s(i,c,t[o])):!1}):typeof t=="object"&&!Array.isArray(t)&&typeof n=="object"&&!Array.isArray(n)?Object.entries(n).every(([a,o])=>{if(o==null)return!0;const i=r.find(c=>c.name===a);return i?(Array.isArray(o)?o:[o]).some(c=>s(i,c,t[a])):!1}):!1}async function Ge(e,{address:t,blockHash:r,fromBlock:n,toBlock:s,event:a,events:o,args:i,strict:u}={}){const c=u??!1,d=o??(a?[a]:void 0);let f=[];d&&(f=[d.flatMap(y=>se({abi:[y],eventName:y.name,args:o?void 0:i}))],a&&(f=f[0]));let l;r?l=await e.request({method:"eth_getLogs",params:[{address:t,topics:f,blockHash:r}]}):l=await e.request({method:"eth_getLogs",params:[{address:t,topics:f,fromBlock:typeof n=="bigint"?R(n):n,toBlock:typeof s=="bigint"?R(s):s}]});const p=l.map(m=>M(m));return d?je({abi:d,args:i,logs:p,strict:c}):p}async function Mt(e,t){const{abi:r,address:n,args:s,blockHash:a,eventName:o,fromBlock:i,toBlock:u,strict:c}=t,d=o?At({abi:r,name:o}):void 0,f=d?void 0:r.filter(l=>l.type==="event");return I(e,Ge,"getLogs")({address:n,args:s,blockHash:a,event:d,events:f,fromBlock:i,toBlock:u,strict:c})}class Gn extends O{constructor({address:t}){super(`No EIP-712 domain found on contract "${t}".`,{metaMessages:["Ensure that:",`- The contract is deployed at the address "${t}".`,"- `eip712Domain()` function exists on the contract.","- `eip712Domain()` function matches signature to ERC-5267 specification."],name:"Eip712DomainNotFoundError"})}}async function Wn(e,t){const{address:r,factory:n,factoryData:s}=t;try{const[a,o,i,u,c,d,f]=await I(e,_,"readContract")({abi:Vn,address:r,functionName:"eip712Domain",factory:n,factoryData:s});return{domain:{name:o,version:i,chainId:Number(u),verifyingContract:c,salt:d},extensions:f,fields:a}}catch(a){const o=a;throw o.name==="ContractFunctionExecutionError"&&o.cause.name==="ContractFunctionZeroDataError"?new Gn({address:r}):o}}const Vn=[{inputs:[],name:"eip712Domain",outputs:[{name:"fields",type:"bytes1"},{name:"name",type:"string"},{name:"version",type:"string"},{name:"chainId",type:"uint256"},{name:"verifyingContract",type:"address"},{name:"salt",type:"bytes32"},{name:"extensions",type:"uint256[]"}],stateMutability:"view",type:"function"}];function Zn(e){var t;return{baseFeePerGas:e.baseFeePerGas.map(r=>BigInt(r)),gasUsedRatio:e.gasUsedRatio,oldestBlock:BigInt(e.oldestBlock),reward:(t=e.reward)==null?void 0:t.map(r=>r.map(n=>BigInt(n)))}}async function Jn(e,{blockCount:t,blockNumber:r,blockTag:n="latest",rewardPercentiles:s}){const a=typeof r=="bigint"?R(r):void 0,o=await e.request({method:"eth_feeHistory",params:[R(t),a||n,s]},{dedupe:!!a});return Zn(o)}async function ye(e,{filter:t}){const r="strict"in t&&t.strict,n=await t.request({method:"eth_getFilterChanges",params:[t.id]});if(typeof n[0]=="string")return n;const s=n.map(a=>M(a));return!("abi"in t)||!t.abi?s:je({abi:t.abi,logs:s,strict:r})}async function Xn(e,{filter:t}){const r=t.strict??!1,s=(await t.request({method:"eth_getFilterLogs",params:[t.id]})).map(a=>M(a));return t.abi?je({abi:t.abi,logs:s,strict:r}):s}async function Kn(e,{address:t,blockNumber:r,blockTag:n="latest",slot:s}){const a=r!==void 0?R(r):void 0;return await e.request({method:"eth_getStorageAt",params:[t,s,a||n]})}async function Qn(e,{hash:t,transactionReceipt:r}){const[n,s]=await Promise.all([I(e,he,"getBlockNumber")({}),t?I(e,kt,"getTransaction")({hash:t}):void 0]),a=(r==null?void 0:r.blockNumber)||(s==null?void 0:s.blockNumber);return a?n-a+1n:0n}async function Ne(e,t){const{blockNumber:r,blockTag:n="latest",blocks:s,returnFullTransactions:a,traceTransfers:o,validation:i}=t;try{const u=[];for(const l of s){const p=l.blockOverrides?Tr(l.blockOverrides):void 0,m=l.calls.map(v=>{const h=v,w=h.account?le(h.account):void 0,g={...h,data:h.abi?V(h):h.data,from:h.from??(w==null?void 0:w.address)};return vt(g),Et(g)}),y=l.stateOverrides?Nr(l.stateOverrides):void 0;u.push({blockOverrides:p,calls:m,stateOverrides:y})}const d=(typeof r=="bigint"?R(r):void 0)||n;return(await e.request({method:"eth_simulateV1",params:[{blockStateCalls:u,returnFullTransactions:a,traceTransfers:o,validation:i},d]})).map((l,p)=>({...kr(l),calls:l.calls.map((m,y)=>{var $,B;const{abi:v,args:h,functionName:w,to:g}=s[p].calls[y],E=(($=m.error)==null?void 0:$.data)??m.returnData,N=BigInt(m.gasUsed),k=(B=m.logs)==null?void 0:B.map(C=>M(C)),A=m.status==="0x1"?"success":"failure",x=v&&A==="success"&&E!=="0x"?$e({abi:v,data:E,functionName:w}):null,T=(()=>{var L;if(A==="success")return;let C;if(((L=m.error)==null?void 0:L.data)==="0x"?C=new Sr:m.error&&(C=new Br(m.error)),!!C)return Tt(C,{abi:v??[],address:g,args:h,functionName:w??"<unknown>"})})();return{data:E,gasUsed:N,logs:k,status:A,...A==="success"?{result:x}:{error:T}}})}))}catch(u){const c=u,d=Cr(c,{});throw d instanceof Rr?c:d}}/*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) */function Yn(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function dt(e){if(!Number.isSafeInteger(e)||e<0)throw new Error("positive integer expected, got "+e)}function fe(e,...t){if(!Yn(e))throw new Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw new Error("Uint8Array expected of length "+t+", got length="+e.length)}function lt(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function es(e,t){fe(e);const r=t.outputLen;if(e.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}function ts(e){return new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4))}function Ht(...e){for(let t=0;t<e.length;t++)e[t].fill(0)}const rs=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function ns(e){return e<<24&**********|e<<8&16711680|e>>>8&65280|e>>>24&255}function ss(e){for(let t=0;t<e.length;t++)e[t]=ns(e[t]);return e}const pt=rs?e=>e:ss;function as(e){if(typeof e!="string")throw new Error("string expected");return new Uint8Array(new TextEncoder().encode(e))}function qt(e){return typeof e=="string"&&(e=as(e)),fe(e),e}class os{}function is(e){const t=n=>e().update(qt(n)).digest(),r=e();return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=()=>e(),t}const ie=BigInt(2**32-1),ht=BigInt(32);function cs(e,t=!1){return t?{h:Number(e&ie),l:Number(e>>ht&ie)}:{h:Number(e>>ht&ie)|0,l:Number(e&ie)|0}}function us(e,t=!1){const r=e.length;let n=new Uint32Array(r),s=new Uint32Array(r);for(let a=0;a<r;a++){const{h:o,l:i}=cs(e[a],t);[n[a],s[a]]=[o,i]}return[n,s]}const fs=(e,t,r)=>e<<r|t>>>32-r,ds=(e,t,r)=>t<<r|e>>>32-r,ls=(e,t,r)=>t<<r-32|e>>>64-r,ps=(e,t,r)=>e<<r-32|t>>>64-r,hs=BigInt(0),Q=BigInt(1),ms=BigInt(2),bs=BigInt(7),gs=BigInt(256),ys=BigInt(113),jt=[],Gt=[],Wt=[];for(let e=0,t=Q,r=1,n=0;e<24;e++){[r,n]=[n,(2*r+3*n)%5],jt.push(2*(5*n+r)),Gt.push((e+1)*(e+2)/2%64);let s=hs;for(let a=0;a<7;a++)t=(t<<Q^(t>>bs)*ys)%gs,t&ms&&(s^=Q<<(Q<<BigInt(a))-Q);Wt.push(s)}const Vt=us(Wt,!0),ws=Vt[0],vs=Vt[1],mt=(e,t,r)=>r>32?ls(e,t,r):fs(e,t,r),bt=(e,t,r)=>r>32?ps(e,t,r):ds(e,t,r);function Es(e,t=24){const r=new Uint32Array(10);for(let n=24-t;n<24;n++){for(let o=0;o<10;o++)r[o]=e[o]^e[o+10]^e[o+20]^e[o+30]^e[o+40];for(let o=0;o<10;o+=2){const i=(o+8)%10,u=(o+2)%10,c=r[u],d=r[u+1],f=mt(c,d,1)^r[i],l=bt(c,d,1)^r[i+1];for(let p=0;p<50;p+=10)e[o+p]^=f,e[o+p+1]^=l}let s=e[2],a=e[3];for(let o=0;o<24;o++){const i=Gt[o],u=mt(s,a,i),c=bt(s,a,i),d=jt[o];s=e[d],a=e[d+1],e[d]=u,e[d+1]=c}for(let o=0;o<50;o+=10){for(let i=0;i<10;i++)r[i]=e[o+i];for(let i=0;i<10;i++)e[o+i]^=~r[(i+2)%10]&r[(i+4)%10]}e[0]^=ws[n],e[1]^=vs[n]}Ht(r)}class We extends os{constructor(t,r,n,s=!1,a=24){if(super(),this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,this.enableXOF=!1,this.blockLen=t,this.suffix=r,this.outputLen=n,this.enableXOF=s,this.rounds=a,dt(n),!(0<t&&t<200))throw new Error("only keccak-f1600 function is supported");this.state=new Uint8Array(200),this.state32=ts(this.state)}clone(){return this._cloneInto()}keccak(){pt(this.state32),Es(this.state32,this.rounds),pt(this.state32),this.posOut=0,this.pos=0}update(t){lt(this),t=qt(t),fe(t);const{blockLen:r,state:n}=this,s=t.length;for(let a=0;a<s;){const o=Math.min(r-this.pos,s-a);for(let i=0;i<o;i++)n[this.pos++]^=t[a++];this.pos===r&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;const{state:t,suffix:r,pos:n,blockLen:s}=this;t[n]^=r,r&128&&n===s-1&&this.keccak(),t[s-1]^=128,this.keccak()}writeInto(t){lt(this,!1),fe(t),this.finish();const r=this.state,{blockLen:n}=this;for(let s=0,a=t.length;s<a;){this.posOut>=n&&this.keccak();const o=Math.min(n-this.posOut,a-s);t.set(r.subarray(this.posOut,this.posOut+o),s),this.posOut+=o,s+=o}return t}xofInto(t){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(t)}xof(t){return dt(t),this.xofInto(new Uint8Array(t))}digestInto(t){if(es(t,this),this.finished)throw new Error("digest() was already called");return this.writeInto(t),this.destroy(),t}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,Ht(this.state)}_cloneInto(t){const{blockLen:r,suffix:n,outputLen:s,rounds:a,enableXOF:o}=this;return t||(t=new We(r,n,s,o,a)),t.state32.set(this.state32),t.pos=this.pos,t.posOut=this.posOut,t.finished=this.finished,t.rounds=a,t.suffix=n,t.outputLen=s,t.enableXOF=o,t.destroyed=this.destroyed,t}}const As=(e,t,r)=>is(()=>new We(t,e,r)),xs=As(1,136,256/8);function Zt(e,t={}){const{as:r=typeof e=="string"?"Hex":"Bytes"}=t,n=xs(bn(e));return r==="Bytes"?n:Pr(n)}class Is extends Map{constructor(t){super(),Object.defineProperty(this,"maxSize",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.maxSize=t}get(t){const r=super.get(t);return super.has(t)&&r!==void 0&&(this.delete(t),super.set(t,r)),r}set(t,r){if(super.set(t,r),this.maxSize&&this.size>this.maxSize){const n=this.keys().next().value;n&&this.delete(n)}return this}}const Ts={checksum:new Is(8192)},xe=Ts.checksum,Ns=/^0x[a-fA-F0-9]{40}$/;function we(e,t={}){const{strict:r=!0}=t;if(!Ns.test(e))throw new gt({address:e,cause:new Ss});if(r){if(e.toLowerCase()===e)return;if(ks(e)!==e)throw new gt({address:e,cause:new Bs})}}function ks(e){if(xe.has(e))return xe.get(e);we(e,{strict:!1});const t=e.substring(2).toLowerCase(),r=Zt(wn(t),{as:"Bytes"}),n=t.split("");for(let a=0;a<40;a+=2)r[a>>1]>>4>=8&&n[a]&&(n[a]=n[a].toUpperCase()),(r[a>>1]&15)>=8&&n[a+1]&&(n[a+1]=n[a+1].toUpperCase());const s=`0x${n.join("")}`;return xe.set(e,s),s}function ke(e,t={}){const{strict:r=!0}=t??{};try{return we(e,{strict:r}),!0}catch{return!1}}class gt extends P{constructor({address:t,cause:r}){super(`Address "${t}" is invalid.`,{cause:r}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Address.InvalidAddressError"})}}class Ss extends P{constructor(){super("Address is not a 20 byte (40 hexadecimal character) value."),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Address.InvalidInputError"})}}class Bs extends P{constructor(){super("Address does not match its checksum counterpart."),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"Address.InvalidChecksumError"})}}function Se(e){let t=!0,r="",n=0,s="",a=!1;for(let o=0;o<e.length;o++){const i=e[o];if(["(",")",","].includes(i)&&(t=!0),i==="("&&n++,i===")"&&n--,!!t){if(n===0){if(i===" "&&["event","function","error",""].includes(s))s="";else if(s+=i,i===")"){a=!0;break}continue}if(i===" "){e[o-1]!==","&&r!==","&&r!==",("&&(r="",t=!1);continue}s+=i,r+=i}}if(!a)throw new P("Unable to normalize signature.");return s}function Be(e,t){const r=typeof e,n=t.type;switch(n){case"address":return ke(e,{strict:!1});case"bool":return r==="boolean";case"function":return r==="string";case"string":return r==="string";default:return n==="tuple"&&"components"in t?Object.values(t.components).every((s,a)=>Be(Object.values(e)[a],s)):/^u?int(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/.test(n)?r==="number"||r==="bigint":/^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/.test(n)?r==="string"||e instanceof Uint8Array:/[a-z]+[1-9]{0,3}(\[[0-9]{0,}\])+$/.test(n)?Array.isArray(e)&&e.every(s=>Be(s,{...t,type:n.replace(/(\[[0-9]{0,}\])$/,"")})):!1}}function Jt(e,t,r){for(const n in e){const s=e[n],a=t[n];if(s.type==="tuple"&&a.type==="tuple"&&"components"in s&&"components"in a)return Jt(s.components,a.components,r[n]);const o=[s.type,a.type];if(o.includes("address")&&o.includes("bytes20")?!0:o.includes("address")&&o.includes("string")?ke(r[n],{strict:!1}):o.includes("address")&&o.includes("bytes")?ke(r[n],{strict:!1}):!1)return o}}function Xt(e,t={}){const{prepare:r=!0}=t,n=Array.isArray(e)||typeof e=="string"?st(e):e;return{...n,...r?{hash:q(n)}:{}}}function Cs(e,t,r){const{args:n=[],prepare:s=!0}=r??{},a=Fr(t,{strict:!1}),o=e.filter(c=>a?c.type==="function"||c.type==="error"?Kt(c)===De(t,0,4):c.type==="event"?q(c)===t:!1:"name"in c&&c.name===t);if(o.length===0)throw new Ce({name:t});if(o.length===1)return{...o[0],...s?{hash:q(o[0])}:{}};let i;for(const c of o){if(!("inputs"in c))continue;if(!n||n.length===0){if(!c.inputs||c.inputs.length===0)return{...c,...s?{hash:q(c)}:{}};continue}if(!c.inputs||c.inputs.length===0||c.inputs.length!==n.length)continue;if(n.every((f,l)=>{const p="inputs"in c&&c.inputs[l];return p?Be(f,p):!1})){if(i&&"inputs"in i&&i.inputs){const f=Jt(c.inputs,i.inputs,n);if(f)throw new Ps({abiItem:c,type:f[0]},{abiItem:i,type:f[1]})}i=c}}const u=(()=>{if(i)return i;const[c,...d]=o;return{...c,overloads:d}})();if(!u)throw new Ce({name:t});return{...u,...s?{hash:q(u)}:{}}}function Kt(e){return De(q(e),0,4)}function Rs(e){const t=typeof e=="string"?e:Ie(e);return Se(t)}function q(e){return typeof e!="string"&&"hash"in e&&e.hash?e.hash:Zt(Ue(Rs(e)))}class Ps extends P{constructor(t,r){super("Found ambiguous types in overloaded ABI Items.",{metaMessages:[`\`${t.type}\` in \`${Se(Ie(t.abiItem))}\`, and`,`\`${r.type}\` in \`${Se(Ie(r.abiItem))}\``,"","These types encode differently and cannot be distinguished at runtime.","Remove one of the ambiguous items in the ABI."]}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiItem.AmbiguityError"})}}class Ce extends P{constructor({name:t,data:r,type:n="item"}){const s=t?` with name "${t}"`:r?` with data "${r}"`:"";super(`ABI ${n}${s} not found.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiItem.NotFoundError"})}}const Fs=/^(.*)\[([0-9]*)\]$/,$s=/^bytes([1-9]|1[0-9]|2[0-9]|3[0-2])?$/,Qt=/^(u?int)(8|16|24|32|40|48|56|64|72|80|88|96|104|112|120|128|136|144|152|160|168|176|184|192|200|208|216|224|232|240|248|256)?$/;function Ls({checksumAddress:e,parameters:t,values:r}){const n=[];for(let s=0;s<t.length;s++)n.push(Ve({checksumAddress:e,parameter:t[s],value:r[s]}));return n}function Ve({checksumAddress:e=!1,parameter:t,value:r}){const n=t,s=qs(n.type);if(s){const[a,o]=s;return zs(r,{checksumAddress:e,length:a,parameter:{...n,type:o}})}if(n.type==="tuple")return Hs(r,{checksumAddress:e,parameter:n});if(n.type==="address")return Os(r,{checksum:e});if(n.type==="bool")return Us(r);if(n.type.startsWith("uint")||n.type.startsWith("int")){const a=n.type.startsWith("int"),[,,o="256"]=Qt.exec(n.type)??[];return _s(r,{signed:a,size:Number(o)})}if(n.type.startsWith("bytes"))return Ds(r,{type:n.type});if(n.type==="string")return Ms(r);throw new rr(n.type)}function Ze(e){let t=0;for(let a=0;a<e.length;a++){const{dynamic:o,encoded:i}=e[a];o?t+=32:t+=G(i)}const r=[],n=[];let s=0;for(let a=0;a<e.length;a++){const{dynamic:o,encoded:i}=e[a];o?(r.push(J(t+s,{size:32})),n.push(i),s+=G(i)):r.push(i)}return D(...r,...n)}function Os(e,t){const{checksum:r=!1}=t;return we(e,{strict:r}),{dynamic:!1,encoded:re(e.toLowerCase())}}function zs(e,t){const{checksumAddress:r,length:n,parameter:s}=t,a=n===null;if(!Array.isArray(e))throw new Gs(e);if(!a&&e.length!==n)throw new js({expectedLength:n,givenLength:e.length,type:`${s.type}[${n}]`});let o=!1;const i=[];for(let u=0;u<e.length;u++){const c=Ve({checksumAddress:r,parameter:s,value:e[u]});c.dynamic&&(o=!0),i.push(c)}if(a||o){const u=Ze(i);if(a){const c=J(i.length,{size:32});return{dynamic:!0,encoded:i.length>0?D(c,u):c}}if(o)return{dynamic:!0,encoded:u}}return{dynamic:!1,encoded:D(...i.map(({encoded:u})=>u))}}function Ds(e,{type:t}){const[,r]=t.split("bytes"),n=G(e);if(!r){let s=e;return n%32!==0&&(s=j(s,Math.ceil((e.length-2)/2/32)*32)),{dynamic:!0,encoded:D(re(J(n,{size:32})),s)}}if(n!==Number.parseInt(r))throw new er({expectedSize:Number.parseInt(r),value:e});return{dynamic:!1,encoded:j(e)}}function Us(e){if(typeof e!="boolean")throw new P(`Invalid boolean value: "${e}" (type: ${typeof e}). Expected: \`true\` or \`false\`.`);return{dynamic:!1,encoded:re(St(e))}}function _s(e,{signed:t,size:r}){if(typeof r=="number"){const n=2n**(BigInt(r)-(t?1n:0n))-1n,s=t?-n-1n:0n;if(e>n||e<s)throw new $r({max:n.toString(),min:s.toString(),signed:t,size:r/8,value:e.toString()})}return{dynamic:!1,encoded:J(e,{size:32,signed:t})}}function Ms(e){const t=Ue(e),r=Math.ceil(G(t)/32),n=[];for(let s=0;s<r;s++)n.push(j(De(t,s*32,(s+1)*32)));return{dynamic:!0,encoded:D(j(J(G(t),{size:32})),...n)}}function Hs(e,t){const{checksumAddress:r,parameter:n}=t;let s=!1;const a=[];for(let o=0;o<n.components.length;o++){const i=n.components[o],u=Array.isArray(e)?o:i.name,c=Ve({checksumAddress:r,parameter:i,value:e[u]});a.push(c),c.dynamic&&(s=!0)}return{dynamic:s,encoded:s?Ze(a):D(...a.map(({encoded:o})=>o))}}function qs(e){const t=e.match(/^(.*)\[(\d+)?\]$/);return t?[t[2]?Number(t[2]):null,t[1]]:void 0}function Yt(e,t,r){const{checksumAddress:n=!1}={};if(e.length!==t.length)throw new tr({expectedLength:e.length,givenLength:t.length});const s=Ls({checksumAddress:n,parameters:e,values:t}),a=Ze(s);return a.length===0?"0x":a}function Re(e,t){if(e.length!==t.length)throw new tr({expectedLength:e.length,givenLength:t.length});const r=[];for(let n=0;n<e.length;n++){const s=e[n],a=t[n];r.push(Re.encode(s,a))}return D(...r)}(function(e){function t(r,n,s=!1){if(r==="address"){const u=n;return we(u),re(u.toLowerCase(),s?32:0)}if(r==="string")return Ue(n);if(r==="bytes")return n;if(r==="bool")return re(St(n),s?32:1);const a=r.match(Qt);if(a){const[u,c,d="256"]=a,f=Number.parseInt(d)/8;return J(n,{size:s?32:f,signed:c==="int"})}const o=r.match($s);if(o){const[u,c]=o;if(Number.parseInt(c)!==(n.length-2)/2)throw new er({expectedSize:Number.parseInt(c),value:n});return j(n,s?32:0)}const i=r.match(Fs);if(i&&Array.isArray(n)){const[u,c]=i,d=[];for(let f=0;f<n.length;f++)d.push(t(c,n[f],!0));return d.length===0?"0x":D(...d)}throw new rr(r)}e.encode=t})(Re||(Re={}));class js extends P{constructor({expectedLength:t,givenLength:r,type:n}){super(`Array length mismatch for type \`${n}\`. Expected: \`${t}\`. Given: \`${r}\`.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiParameters.ArrayLengthMismatchError"})}}class er extends P{constructor({expectedSize:t,value:r}){super(`Size of bytes "${r}" (bytes${G(r)}) does not match expected size (bytes${t}).`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiParameters.BytesSizeMismatchError"})}}class tr extends P{constructor({expectedLength:t,givenLength:r}){super(["ABI encoding parameters/values length mismatch.",`Expected length (parameters): ${t}`,`Given length (values): ${r}`].join(`
`)),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiParameters.LengthMismatchError"})}}class Gs extends P{constructor(t){super(`Value \`${t}\` is not a valid array.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiParameters.InvalidArrayError"})}}class rr extends P{constructor(t){super(`Type \`${t}\` is not a valid ABI Type.`),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"AbiParameters.InvalidTypeError"})}}function Ws(e,t){var s;const{bytecode:r,args:n}=t;return D(r,(s=e.inputs)!=null&&s.length&&(n!=null&&n.length)?Yt(e.inputs,n):"0x")}function Vs(e){return Xt(e)}function Zs(e,...t){const{overloads:r}=e,n=r?Js([e,...r],e.name,{args:t[0]}):e,s=Xs(n),a=t.length>0?Yt(n.inputs,t[0]):void 0;return a?D(s,a):s}function H(e,t={}){return Xt(e,t)}function Js(e,t,r){const n=Cs(e,t,r);if(n.type!=="function")throw new Ce({name:t,type:"function"});return n}function Xs(e){return Kt(e)}const Ks="0xeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee",z="0x0000000000000000000000000000000000000000";class Qs extends O{constructor({domain:t}){super(`Invalid domain "${W(t)}".`,{metaMessages:["Must be a valid EIP-712 domain."]})}}class Ys extends O{constructor({primaryType:t,types:r}){super(`Invalid primary type \`${t}\` must be one of \`${JSON.stringify(Object.keys(r))}\`.`,{docsPath:"/api/glossary/Errors#typeddatainvalidprimarytypeerror",metaMessages:["Check that the primary type is a key in `types`."]})}}class ea extends O{constructor({type:t}){super(`Struct type "${t}" is invalid.`,{metaMessages:["Struct type must not be a Solidity type."],name:"InvalidStructTypeError"})}}function ta(e){const{domain:t={},message:r,primaryType:n}=e,s={EIP712Domain:oa({domain:t}),...e.types};aa({domain:t,message:r,primaryType:n,types:s});const a=["0x1901"];return t&&a.push(ra({domain:t,types:s})),n!=="EIP712Domain"&&a.push(nr({data:r,primaryType:n,types:s})),F(Fe(a))}function ra({domain:e,types:t}){return nr({data:e,primaryType:"EIP712Domain",types:t})}function nr({data:e,primaryType:t,types:r}){const n=sr({data:e,primaryType:t,types:r});return F(n)}function sr({data:e,primaryType:t,types:r}){const n=[{type:"bytes32"}],s=[na({primaryType:t,types:r})];for(const a of r[t]){const[o,i]=or({types:r,name:a.name,type:a.type,value:e[a.name]});n.push(o),s.push(i)}return pe(n,s)}function na({primaryType:e,types:t}){const r=Z(sa({primaryType:e,types:t}));return F(r)}function sa({primaryType:e,types:t}){let r="";const n=ar({primaryType:e,types:t});n.delete(e);const s=[e,...Array.from(n).sort()];for(const a of s)r+=`${a}(${t[a].map(({name:o,type:i})=>`${i} ${o}`).join(",")})`;return r}function ar({primaryType:e,types:t},r=new Set){const n=e.match(/^\w*/u),s=n==null?void 0:n[0];if(r.has(s)||t[s]===void 0)return r;r.add(s);for(const a of t[s])ar({primaryType:a.type,types:t},r);return r}function or({types:e,name:t,type:r,value:n}){if(e[r]!==void 0)return[{type:"bytes32"},F(sr({data:n,primaryType:r,types:e}))];if(r==="bytes")return n=`0x${(n.length%2?"0":"")+n.slice(2)}`,[{type:"bytes32"},F(n)];if(r==="string")return[{type:"bytes32"},F(Z(n))];if(r.lastIndexOf("]")===r.length-1){const s=r.slice(0,r.lastIndexOf("[")),a=n.map(o=>or({name:t,type:s,types:e,value:o}));return[{type:"bytes32"},F(pe(a.map(([o])=>o),a.map(([,o])=>o)))]}return[{type:r},n]}function aa(e){const{domain:t,message:r,primaryType:n,types:s}=e,a=(o,i)=>{for(const u of o){const{name:c,type:d}=u,f=i[c],l=d.match(Lr);if(l&&(typeof f=="number"||typeof f=="bigint")){const[y,v,h]=l;R(f,{signed:v==="int",size:Number.parseInt(h)/8})}if(d==="address"&&typeof f=="string"&&!Or(f))throw new zr({address:f});const p=d.match(Dr);if(p){const[y,v]=p;if(v&&Te(f)!==Number.parseInt(v))throw new Ur({expectedSize:Number.parseInt(v),givenSize:Te(f)})}const m=s[d];m&&(ia(d),a(m,f))}};if(s.EIP712Domain&&t){if(typeof t!="object")throw new Qs({domain:t});a(s.EIP712Domain,t)}if(n!=="EIP712Domain")if(s[n])a(s[n],r);else throw new Ys({primaryType:n,types:s})}function oa({domain:e}){return[typeof(e==null?void 0:e.name)=="string"&&{name:"name",type:"string"},(e==null?void 0:e.version)&&{name:"version",type:"string"},(typeof(e==null?void 0:e.chainId)=="number"||typeof(e==null?void 0:e.chainId)=="bigint")&&{name:"chainId",type:"uint256"},(e==null?void 0:e.verifyingContract)&&{name:"verifyingContract",type:"address"},(e==null?void 0:e.salt)&&{name:"salt",type:"bytes32"}].filter(Boolean)}function ia(e){if(e==="address"||e==="bool"||e==="string"||e.startsWith("bytes")||e.startsWith("uint")||e.startsWith("int"))throw new ea({type:e})}const ca=`Ethereum Signed Message:
`;function ua(e){const t=typeof e=="string"?rt(e):typeof e.raw=="string"?e.raw:te(e.raw),r=rt(`${ca}${Te(t)}`);return Fe([r,t])}function ir(e,t){return F(ua(e),t)}const cr="0x6492649264926492649264926492649264926492649264926492649264926492";function fa(e){return _r(e,-32)===cr}function da(e){const{address:t,data:r,signature:n,to:s="hex"}=e,a=Mr([pe([{type:"address"},{type:"bytes"},{type:"bytes"}],[t,r,n]),cr]);return s==="hex"?a:Bt(a)}const la="0x6080604052348015600e575f80fd5b5061016d8061001c5f395ff3fe608060405234801561000f575f80fd5b5060043610610029575f3560e01c8063f8b2cb4f1461002d575b5f80fd5b610047600480360381019061004291906100db565b61005d565b604051610054919061011e565b60405180910390f35b5f8173ffffffffffffffffffffffffffffffffffffffff16319050919050565b5f80fd5b5f73ffffffffffffffffffffffffffffffffffffffff82169050919050565b5f6100aa82610081565b9050919050565b6100ba816100a0565b81146100c4575f80fd5b50565b5f813590506100d5816100b1565b92915050565b5f602082840312156100f0576100ef61007d565b5b5f6100fd848285016100c7565b91505092915050565b5f819050919050565b61011881610106565b82525050565b5f6020820190506101315f83018461010f565b9291505056fea26469706673582212203b9fe929fe995c7cf9887f0bdba8a36dd78e8b73f149b17d2d9ad7cd09d2dc6264736f6c634300081a0033";async function pa(e,t){const{blockNumber:r,blockTag:n,calls:s,stateOverrides:a,traceAssetChanges:o,traceTransfers:i,validation:u}=t,c=t.account?le(t.account):void 0;if(o&&!c)throw new O("`account` is required when `traceAssetChanges` is true");const d=c?Ws(Vs("constructor(bytes, bytes)"),{bytecode:Hr,args:[la,Zs(H("function getBalance(address)"),[c.address])]}):void 0,f=o?await Promise.all(t.calls.map(async b=>{if(!b.data&&!b.abi)return;const{accessList:S}=await zt(e,{account:c.address,...b,data:b.abi?V(b):b.data});return S.map(({address:X,storageKeys:oe})=>oe.length>0?X:null)})).then(b=>b.flat().filter(Boolean)):[],l=a==null?void 0:a.map(b=>b.address===(c==null?void 0:c.address)?{...b,nonce:0}:b),p=await Ne(e,{blockNumber:r,blockTag:n,blocks:[...o?[{calls:[{data:d}],stateOverrides:a},{calls:f.map((b,S)=>({abi:[H("function balanceOf(address) returns (uint256)")],functionName:"balanceOf",args:[c.address],to:b,from:z,nonce:S})),stateOverrides:[{address:z,nonce:0}]}]:[],{calls:[...s,{}].map((b,S)=>({...b,from:c==null?void 0:c.address,nonce:S})),stateOverrides:l},...o?[{calls:[{data:d}]},{calls:f.map((b,S)=>({abi:[H("function balanceOf(address) returns (uint256)")],functionName:"balanceOf",args:[c.address],to:b,from:z,nonce:S})),stateOverrides:[{address:z,nonce:0}]},{calls:f.map((b,S)=>({to:b,abi:[H("function decimals() returns (uint256)")],functionName:"decimals",from:z,nonce:S})),stateOverrides:[{address:z,nonce:0}]},{calls:f.map((b,S)=>({to:b,abi:[H("function tokenURI(uint256) returns (string)")],functionName:"tokenURI",args:[0n],from:z,nonce:S})),stateOverrides:[{address:z,nonce:0}]},{calls:f.map((b,S)=>({to:b,abi:[H("function symbol() returns (string)")],functionName:"symbol",from:z,nonce:S})),stateOverrides:[{address:z,nonce:0}]}]:[]],traceTransfers:i,validation:u}),m=o?p[2]:p[0],[y,v,,h,w,g,E,N]=o?p:[],{calls:k,...A}=m,x=k.slice(0,-1)??[],T=(y==null?void 0:y.calls)??[],$=(v==null?void 0:v.calls)??[],B=[...T,...$].map(b=>b.status==="success"?ue(b.data):null),C=(h==null?void 0:h.calls)??[],L=(w==null?void 0:w.calls)??[],Ee=[...C,...L].map(b=>b.status==="success"?ue(b.data):null),ae=((g==null?void 0:g.calls)??[]).map(b=>b.status==="success"?b.result:null),ur=((N==null?void 0:N.calls)??[]).map(b=>b.status==="success"?b.result:null),fr=((E==null?void 0:E.calls)??[]).map(b=>b.status==="success"?b.result:null),Ae=[];for(const[b,S]of Ee.entries()){const X=B[b];if(typeof S!="bigint"||typeof X!="bigint")continue;const oe=ae[b-1],dr=ur[b-1],lr=fr[b-1],Xe=b===0?{address:Ks,decimals:18,symbol:"ETH"}:{address:f[b-1],decimals:lr||oe?Number(oe??1):void 0,symbol:dr??void 0};Ae.some(pr=>pr.token.address===Xe.address)||Ae.push({token:Xe,value:{pre:X,post:S,diff:S-X}})}return{assetChanges:Ae,block:A,results:x}}function ha(e,{blockTag:t="latest",emitMissed:r=!1,emitOnBegin:n=!1,onBlock:s,onError:a,includeTransactions:o,poll:i,pollingInterval:u=e.pollingInterval}){const c=typeof i<"u"?i:!(e.transport.type==="webSocket"||e.transport.type==="fallback"&&e.transport.transports[0].config.type==="webSocket"),d=o??!1;let f;return c?(()=>{const m=W(["watchBlocks",e.uid,t,r,n,d,u]);return ne(m,{onBlock:s,onError:a},y=>me(async()=>{var v;try{const h=await I(e,Y,"getBlock")({blockTag:t,includeTransactions:d});if(h.number!==null&&(f==null?void 0:f.number)!=null){if(h.number===f.number)return;if(h.number-f.number>1&&r)for(let w=(f==null?void 0:f.number)+1n;w<h.number;w++){const g=await I(e,Y,"getBlock")({blockNumber:w,includeTransactions:d});y.onBlock(g,f),f=g}}((f==null?void 0:f.number)==null||t==="pending"&&(h==null?void 0:h.number)==null||h.number!==null&&h.number>f.number)&&(y.onBlock(h,f),f=h)}catch(h){(v=y.onError)==null||v.call(y,h)}},{emitOnBegin:n,interval:u}))})():(()=>{let m=!0,y=!0,v=()=>m=!1;return(async()=>{try{n&&I(e,Y,"getBlock")({blockTag:t,includeTransactions:d}).then(g=>{m&&y&&(s(g,void 0),y=!1)});const h=(()=>{if(e.transport.type==="fallback"){const g=e.transport.transports.find(E=>E.config.type==="webSocket");return g?g.value:e.transport}return e.transport})(),{unsubscribe:w}=await h.subscribe({params:["newHeads"],async onData(g){if(!m)return;const E=await I(e,Y,"getBlock")({blockNumber:g.blockNumber,includeTransactions:d}).catch(()=>{});m&&(s(E,f),y=!1,f=E)},onError(g){a==null||a(g)}});v=w,m||v()}catch(h){a==null||a(h)}})(),()=>v()})()}async function ve(e,{filter:t}){return t.request({method:"eth_uninstallFilter",params:[t.id]})}function ma(e,{address:t,args:r,batch:n=!0,event:s,events:a,fromBlock:o,onError:i,onLogs:u,poll:c,pollingInterval:d=e.pollingInterval,strict:f}){const l=typeof c<"u"?c:typeof o=="bigint"?!0:!(e.transport.type==="webSocket"||e.transport.type==="fallback"&&e.transport.transports[0].config.type==="webSocket"),p=f??!1;return l?(()=>{const v=W(["watchEvent",t,r,n,e.uid,s,d,o]);return ne(v,{onLogs:u,onError:i},h=>{let w;o!==void 0&&(w=o-1n);let g,E=!1;const N=me(async()=>{var k;if(!E){try{g=await I(e,Ut,"createEventFilter")({address:t,args:r,event:s,events:a,strict:p,fromBlock:o})}catch{}E=!0;return}try{let A;if(g)A=await I(e,ye,"getFilterChanges")({filter:g});else{const x=await I(e,he,"getBlockNumber")({});w&&w!==x?A=await I(e,Ge,"getLogs")({address:t,args:r,event:s,events:a,fromBlock:w+1n,toBlock:x}):A=[],w=x}if(A.length===0)return;if(n)h.onLogs(A);else for(const x of A)h.onLogs([x])}catch(A){g&&A instanceof Ct&&(E=!1),(k=h.onError)==null||k.call(h,A)}},{emitOnBegin:!0,interval:d});return async()=>{g&&await I(e,ve,"uninstallFilter")({filter:g}),N()}})})():(()=>{let v=!0,h=()=>v=!1;return(async()=>{try{const w=(()=>{if(e.transport.type==="fallback"){const k=e.transport.transports.find(A=>A.config.type==="webSocket");return k?k.value:e.transport}return e.transport})(),g=a??(s?[s]:void 0);let E=[];g&&(E=[g.flatMap(A=>se({abi:[A],eventName:A.name,args:r}))],s&&(E=E[0]));const{unsubscribe:N}=await w.subscribe({params:["logs",{address:t,topics:E}],onData(k){var x;if(!v)return;const A=k.result;try{const{eventName:T,args:$}=Le({abi:g??[],data:A.data,topics:A.topics,strict:p}),B=M(A,{args:$,eventName:T});u([B])}catch(T){let $,B;if(T instanceof Oe||T instanceof ze){if(f)return;$=T.abiItem.name,B=(x=T.abiItem.inputs)==null?void 0:x.some(L=>!("name"in L&&L.name))}const C=M(A,{args:B?[]:{},eventName:$});u([C])}},onError(k){i==null||i(k)}});h=N,v||h()}catch(w){i==null||i(w)}})(),()=>h()})()}function ba(e,{batch:t=!0,onError:r,onTransactions:n,poll:s,pollingInterval:a=e.pollingInterval}){return(typeof s<"u"?s:e.transport.type!=="webSocket")?(()=>{const c=W(["watchPendingTransactions",e.uid,t,a]);return ne(c,{onTransactions:n,onError:r},d=>{let f;const l=me(async()=>{var p;try{if(!f)try{f=await I(e,_t,"createPendingTransactionFilter")({});return}catch(y){throw l(),y}const m=await I(e,ye,"getFilterChanges")({filter:f});if(m.length===0)return;if(t)d.onTransactions(m);else for(const y of m)d.onTransactions([y])}catch(m){(p=d.onError)==null||p.call(d,m)}},{emitOnBegin:!0,interval:a});return async()=>{f&&await I(e,ve,"uninstallFilter")({filter:f}),l()}})})():(()=>{let c=!0,d=()=>c=!1;return(async()=>{try{const{unsubscribe:f}=await e.transport.subscribe({params:["newPendingTransactions"],onData(l){if(!c)return;const p=l.result;n([p])},onError(l){r==null||r(l)}});d=f,c||d()}catch(f){r==null||r(f)}})(),()=>d()})()}function ga(e){return e.map(t=>({...t,value:BigInt(t.value)}))}function ya(e){return{...e,balance:e.balance?BigInt(e.balance):void 0,nonce:e.nonce?Nt(e.nonce):void 0,storageProof:e.storageProof?ga(e.storageProof):void 0}}async function wa(e,{address:t,blockNumber:r,blockTag:n,storageKeys:s}){const a=n??"latest",o=r!==void 0?R(r):void 0,i=await e.request({method:"eth_getProof",params:[t,s,o||a]});return ya(i)}function va({r:e,s:t,to:r="hex",v:n,yParity:s}){const a=(()=>{if(s===0||s===1)return s;if(n&&(n===27n||n===28n||n>=35n))return n%2n===0n?1:0;throw new Error("Invalid `v` or `yParity` value")})(),o=`0x${new fn.Signature(ue(e),ue(t)).toCompactHex()}${a===0?"1b":"1c"}`;return r==="hex"?o:Bt(o)}async function Je(e,t){var f,l,p;const{address:r,factory:n,factoryData:s,hash:a,signature:o,universalSignatureVerifierAddress:i=(p=(l=(f=e.chain)==null?void 0:f.contracts)==null?void 0:l.universalSignatureVerifier)==null?void 0:p.address,...u}=t,c=yt(o)?o:typeof o=="object"&&"r"in o&&"s"in o?va(o):te(o),d=await(async()=>!n&&!s||fa(c)?c:da({address:n,data:s,signature:c}))();try{const m=i?{to:i,data:V({abi:nt,functionName:"isValidSig",args:[r,a,d]}),...u}:{data:qr({abi:nt,args:[r,a,d],bytecode:jr}),...u},{data:y}=await I(e,Rt,"call")(m);return Gr(y??"0x0")}catch(m){try{if(_e(Wr(r),await Vr({hash:a,signature:o})))return!0}catch{}if(m instanceof Zr)return!1;throw m}}async function Ea(e,{address:t,message:r,factory:n,factoryData:s,signature:a,...o}){const i=ir(r);return Je(e,{address:t,factory:n,factoryData:s,hash:i,signature:a,...o})}async function Aa(e,t){const{address:r,factory:n,factoryData:s,signature:a,message:o,primaryType:i,types:u,domain:c,...d}=t,f=ta({message:o,primaryType:i,types:u,domain:c});return Je(e,{address:r,factory:n,factoryData:s,hash:f,signature:a,...d})}function xa(e,t){const{abi:r,address:n,args:s,batch:a=!0,eventName:o,fromBlock:i,onError:u,onLogs:c,poll:d,pollingInterval:f=e.pollingInterval,strict:l}=t;return(typeof d<"u"?d:typeof i=="bigint"?!0:!(e.transport.type==="webSocket"||e.transport.type==="fallback"&&e.transport.transports[0].config.type==="webSocket"))?(()=>{const v=l??!1,h=W(["watchContractEvent",n,s,a,e.uid,o,f,v,i]);return ne(h,{onLogs:c,onError:u},w=>{let g;i!==void 0&&(g=i-1n);let E,N=!1;const k=me(async()=>{var A;if(!N){try{E=await I(e,Dt,"createContractEventFilter")({abi:r,address:n,args:s,eventName:o,strict:v,fromBlock:i})}catch{}N=!0;return}try{let x;if(E)x=await I(e,ye,"getFilterChanges")({filter:E});else{const T=await I(e,he,"getBlockNumber")({});g&&g<T?x=await I(e,Mt,"getContractEvents")({abi:r,address:n,args:s,eventName:o,fromBlock:g+1n,toBlock:T,strict:v}):x=[],g=T}if(x.length===0)return;if(a)w.onLogs(x);else for(const T of x)w.onLogs([T])}catch(x){E&&x instanceof Ct&&(N=!1),(A=w.onError)==null||A.call(w,x)}},{emitOnBegin:!0,interval:f});return async()=>{E&&await I(e,ve,"uninstallFilter")({filter:E}),k()}})})():(()=>{const v=l??!1,h=W(["watchContractEvent",n,s,a,e.uid,o,f,v]);let w=!0,g=()=>w=!1;return ne(h,{onLogs:c,onError:u},E=>((async()=>{try{const N=(()=>{if(e.transport.type==="fallback"){const x=e.transport.transports.find(T=>T.config.type==="webSocket");return x?x.value:e.transport}return e.transport})(),k=o?se({abi:r,eventName:o,args:s}):[],{unsubscribe:A}=await N.subscribe({params:["logs",{address:n,topics:k}],onData(x){var $;if(!w)return;const T=x.result;try{const{eventName:B,args:C}=Le({abi:r,data:T.data,topics:T.topics,strict:l}),L=M(T,{args:C,eventName:B});E.onLogs([L])}catch(B){let C,L;if(B instanceof Oe||B instanceof ze){if(l)return;C=B.abiItem.name,L=($=B.abiItem.inputs)==null?void 0:$.some(ae=>!("name"in ae&&ae.name))}const Ee=M(T,{args:L?[]:{},eventName:C});E.onLogs([Ee])}},onError(x){var T;(T=E.onError)==null||T.call(E,x)}});g=A,w||g()}catch(N){u==null||u(N)}})(),()=>g()))})()}function Ia(e){var f,l,p;const{scheme:t,statement:r,...n}=((f=e.match(Ta))==null?void 0:f.groups)??{},{chainId:s,expirationTime:a,issuedAt:o,notBefore:i,requestId:u,...c}=((l=e.match(Na))==null?void 0:l.groups)??{},d=(p=e.split("Resources:")[1])==null?void 0:p.split(`
- `).slice(1);return{...n,...c,...s?{chainId:Number(s)}:{},...a?{expirationTime:new Date(a)}:{},...o?{issuedAt:new Date(o)}:{},...i?{notBefore:new Date(i)}:{},...u?{requestId:u}:{},...d?{resources:d}:{},...t?{scheme:t}:{},...r?{statement:r}:{}}}const Ta=/^(?:(?<scheme>[a-zA-Z][a-zA-Z0-9+-.]*):\/\/)?(?<domain>[a-zA-Z0-9+-.]*(?::[0-9]{1,5})?) (?:wants you to sign in with your Ethereum account:\n)(?<address>0x[a-fA-F0-9]{40})\n\n(?:(?<statement>.*)\n\n)?/,Na=/(?:URI: (?<uri>.+))\n(?:Version: (?<version>.+))\n(?:Chain ID: (?<chainId>\d+))\n(?:Nonce: (?<nonce>[a-zA-Z0-9]+))\n(?:Issued At: (?<issuedAt>.+))(?:\nExpiration Time: (?<expirationTime>.+))?(?:\nNot Before: (?<notBefore>.+))?(?:\nRequest ID: (?<requestId>.+))?/;function ka(e){const{address:t,domain:r,message:n,nonce:s,scheme:a,time:o=new Date}=e;if(r&&n.domain!==r||s&&n.nonce!==s||a&&n.scheme!==a||n.expirationTime&&o>=n.expirationTime||n.notBefore&&o<n.notBefore)return!1;try{if(!n.address||t&&!_e(n.address,t))return!1}catch{return!1}return!0}async function Sa(e,t){const{address:r,domain:n,message:s,nonce:a,scheme:o,signature:i,time:u=new Date,...c}=t,d=Ia(s);if(!d.address||!ka({address:r,domain:n,message:d,nonce:a,scheme:o,time:u}))return!1;const l=ir(s);return Je(e,{address:d.address,hash:l,signature:i,...c})}function Ba(e){return{call:t=>Rt(e,t),createAccessList:t=>zt(e,t),createBlockFilter:()=>Un(e),createContractEventFilter:t=>Dt(e,t),createEventFilter:t=>Ut(e,t),createPendingTransactionFilter:()=>_t(e),estimateContractGas:t=>Mn(e,t),estimateGas:t=>It(e,t),getBalance:t=>cn(e,t),getBlobBaseFee:()=>Hn(e),getBlock:t=>Y(e,t),getBlockNumber:t=>he(e,t),getBlockTransactionCount:t=>qn(e,t),getBytecode:t=>ft(e,t),getChainId:()=>on(e),getCode:t=>ft(e,t),getContractEvents:t=>Mt(e,t),getEip712Domain:t=>Wn(e,t),getEnsAddress:t=>xn(e,t),getEnsAvatar:t=>On(e,t),getEnsName:t=>zn(e,t),getEnsResolver:t=>Dn(e,t),getEnsText:t=>Ot(e,t),getFeeHistory:t=>Jn(e,t),estimateFeesPerGas:t=>an(e,t),getFilterChanges:t=>ye(e,t),getFilterLogs:t=>Xn(e,t),getGasPrice:()=>sn(e),getLogs:t=>Ge(e,t),getProof:t=>wa(e,t),estimateMaxPriorityFeePerGas:t=>nn(e,t),getStorageAt:t=>Kn(e,t),getTransaction:t=>kt(e,t),getTransactionConfirmations:t=>Qn(e,t),getTransactionCount:t=>rn(e,t),getTransactionReceipt:t=>tn(e,t),multicall:t=>en(e,t),prepareTransactionRequest:t=>Yr(e,t),readContract:t=>_(e,t),sendRawTransaction:t=>Qr(e,t),simulate:t=>Ne(e,t),simulateBlocks:t=>Ne(e,t),simulateCalls:t=>pa(e,t),simulateContract:t=>Kr(e,t),verifyMessage:t=>Ea(e,t),verifySiweMessage:t=>Sa(e,t),verifyTypedData:t=>Aa(e,t),uninstallFilter:t=>ve(e,t),waitForTransactionReceipt:t=>Xr(e,t),watchBlocks:t=>ha(e,t),watchBlockNumber:t=>Jr(e,t),watchContractEvent:t=>xa(e,t),watchEvent:t=>ma(e,t),watchPendingTransactions:t=>ba(e,t)}}function Fa(e){const{key:t="public",name:r="Public Client"}=e;return un({...e,key:t,name:r,type:"publicClient"}).extend(Ba)}export{Oa as AbiConstructorNotFoundError,za as AbiConstructorParamsNotFoundError,Da as AbiDecodingDataSizeTooSmallError,Sr as AbiDecodingZeroDataError,Ua as AbiEncodingArrayLengthMismatchError,_a as AbiEncodingBytesSizeMismatchError,Ma as AbiEncodingLengthMismatchError,Ha as AbiErrorInputsNotFoundError,qa as AbiErrorNotFoundError,ja as AbiErrorSignatureNotFoundError,tt as AbiEventNotFoundError,Ga as AbiEventSignatureEmptyTopicsError,Ir as AbiEventSignatureNotFoundError,Wa as AbiFunctionNotFoundError,Va as AbiFunctionOutputsNotFoundError,Za as AbiFunctionSignatureNotFoundError,Ja as AccountStateConflictError,Xa as AtomicReadyWalletRejectedUpgradeError,Ka as AtomicityNotSupportedError,O as BaseError,Qa as BaseFeeScalarError,Ya as BlockNotFoundError,eo as BundleTooLargeError,Ur as BytesSizeMismatchError,Zr as CallExecutionError,to as ChainDisconnectedError,ro as ChainDoesNotSupportContract,no as ChainMismatchError,so as ChainNotFoundError,ao as CircularReferenceError,oo as ClientChainNotConfiguredError,io as ContractFunctionExecutionError,Qe as ContractFunctionRevertedError,co as ContractFunctionZeroDataError,uo as CounterfactualDeploymentFailedError,Oe as DecodeLogDataMismatch,ze as DecodeLogTopicsMismatch,fo as DuplicateIdError,lo as Eip1559FeesNotSupportedError,K as EnsAvatarInvalidNftUriError,Tn as EnsAvatarUnsupportedNamespaceError,He as EnsAvatarUriResolutionError,po as EstimateGasExecutionError,ho as ExecutionRevertedError,mo as FeeCapTooHighError,bo as FeeCapTooLowError,go as FeeConflictError,_n as FilterTypeNotSupportedError,yo as HttpRequestError,wo as InsufficientFundsError,vo as IntegerOutOfRangeError,Eo as InternalRpcError,Ao as IntrinsicGasTooHighError,xo as IntrinsicGasTooLowError,Io as InvalidAbiDecodingTypeError,To as InvalidAbiEncodingTypeError,br as InvalidAbiItemError,No as InvalidAbiTypeParameterError,zr as InvalidAddressError,ko as InvalidArrayError,So as InvalidBytesBooleanError,Bo as InvalidChainIdError,Co as InvalidDecimalNumberError,Ro as InvalidDefinitionTypeError,Qs as InvalidDomainError,Po as InvalidFunctionModifierError,Fo as InvalidHexBooleanError,Ct as InvalidInputRpcError,$o as InvalidLegacyVError,Lo as InvalidModifierError,Oo as InvalidParameterError,zo as InvalidParamsRpcError,Do as InvalidParenthesisError,Ys as InvalidPrimaryTypeError,Uo as InvalidRequestRpcError,_o as InvalidSerializableTransactionError,Mo as InvalidSignatureError,Ho as InvalidStorageKeySizeError,qo as InvalidStructSignatureError,ea as InvalidStructTypeError,jo as JsonRpcVersionUnsupportedError,Go as LimitExceededRpcError,Wo as MaxFeePerGasTooLowError,Vo as MethodNotFoundRpcError,Zo as MethodNotSupportedRpcError,Jo as NonceMaxValueError,Xo as NonceTooHighError,Ko as NonceTooLowError,Qo as ParseRpcError,Yo as ProviderDisconnectedError,ei as ProviderRpcError,Br as RawContractError,ti as ResourceNotFoundRpcError,ri as ResourceUnavailableRpcError,ni as RpcError,si as RpcRequestError,ai as SizeExceedsPaddingSizeError,oi as SizeOverflowError,ii as SliceOffsetOutOfBoundsError,ci as SolidityProtectedKeywordError,ui as StateAssignmentConflictError,fi as SwitchChainError,di as TimeoutError,li as TipAboveFeeCapError,pi as TransactionExecutionError,hi as TransactionNotFoundError,mi as TransactionReceiptNotFoundError,bi as TransactionRejectedRpcError,gi as TransactionTypeNotSupportedError,yi as UnauthorizedProviderError,wi as UnknownBundleIdError,Rr as UnknownNodeError,vi as UnknownRpcError,Ei as UnknownSignatureError,Ai as UnknownTypeError,xi as UnsupportedChainIdError,Ii as UnsupportedNonOptionalCapabilityError,Ti as UnsupportedProviderMethodError,Ni as UrlRequiredError,ki as UserRejectedRequestError,Si as WaitForTransactionReceiptTimeoutError,Bi as assertCurrentChain,vt as assertRequest,Ci as assertTransactionEIP1559,Ri as assertTransactionEIP2930,Pi as assertTransactionLegacy,Fi as blobsToCommitments,$i as blobsToProofs,Li as boolToBytes,Oi as boolToHex,zi as bytesToBigInt,Di as bytesToBool,te as bytesToHex,Ui as bytesToNumber,_i as bytesToString,Gc as ccipFetch,Wc as ccipRequest,Mi as checksumAddress,Hi as commitmentToVersionedHash,qi as commitmentsToVersionedHashes,Fe as concat,ji as concatBytes,Mr as concatHex,un as createClient,Fa as createPublicClient,Gi as createTransport,Wi as custom,Vi as decodeAbiParameters,Zi as decodeErrorResult,Le as decodeEventLog,Vc as decodeFunctionData,$e as decodeFunctionResult,Ji as defineBlock,Xi as defineChain,Ki as defineTransaction,Qi as defineTransactionReceipt,Hr as deploylessCallViaBytecodeBytecode,Yi as deploylessCallViaFactoryBytecode,pe as encodeAbiParameters,qr as encodeDeployData,Zc as encodeErrorResult,se as encodeEventTopics,V as encodeFunctionData,Jc as encodeFunctionResult,Ks as ethAddress,ec as etherUnits,tc as fallback,kr as formatBlock,rc as formatEther,nc as formatGwei,M as formatLog,sc as formatTransaction,ac as formatTransactionReceipt,Et as formatTransactionRequest,oc as formatUnits,At as getAbiItem,Wr as getAddress,de as getChainContractAddress,Tt as getContractError,xt as getEventSelector,ic as getEventSignature,cc as getFunctionSelector,uc as getFunctionSignature,fc as getTransactionType,oa as getTypesForEIP712Domain,dc as gweiUnits,ra as hashDomain,ir as hashMessage,nr as hashStruct,ta as hashTypedData,ue as hexToBigInt,Gr as hexToBool,Bt as hexToBytes,Nt as hexToNumber,lc as hexToString,pc as http,Or as isAddress,_e as isAddressEqual,fa as isErc6492Signature,yt as isHex,F as keccak256,ln as labelhash,hc as maxUint256,mc as multicall3Abi,ce as namehash,bc as numberToBytes,R as numberToHex,Xc as offchainLookup,Kc as offchainLookupAbiItem,Qc as offchainLookupSignature,gc as pad,yc as padBytes,wc as padHex,vc as parseAbi,st as parseAbiItem,je as parseEventLogs,Ec as parseUnits,Ac as prepareEncodeFunctionData,ca as presignMessagePrefix,Ba as publicActions,Vr as recoverAddress,xc as recoverPublicKey,Ic as rpcTransactionType,Tc as serializeAccessList,da as serializeErc6492Signature,va as serializeSignature,Nc as serializeTransaction,kc as sha256,Sc as shouldThrow,va as signatureToHex,Te as size,Bc as slice,Cc as sliceBytes,_r as sliceHex,ee as stringToBytes,rt as stringToHex,W as stringify,Rc as toBlobSidecars,Pc as toBlobs,Pe as toBytes,Fc as toEventHash,xt as toEventSelector,$c as toEventSignature,Lc as toFunctionHash,Oc as toFunctionSelector,zc as toFunctionSignature,Z as toHex,ua as toPrefixedMessage,Dc as toRlp,Uc as transactionType,wr as trim,nt as universalSignatureValidatorAbi,jr as universalSignatureValidatorByteCode,aa as validateTypedData,_c as weiUnits,Mc as withCache,Hc as withRetry,qc as withTimeout,z as zeroAddress};
