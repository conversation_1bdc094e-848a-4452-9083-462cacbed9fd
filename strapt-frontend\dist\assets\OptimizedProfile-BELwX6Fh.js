const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/ProfileActivityTimeline-CPw1Sjf6.js","assets/index-G17GlXLb.js","assets/index-s5rE7cZb.css","assets/separator-BLTLWNIf.js","assets/use-strapt-drop-D-qrso9N.js","assets/StraptDrop-DBtBqCxY.js","assets/skeleton-CLgd6C74.js","assets/gift-Cgh4t_YK.js","assets/shield-check-DdOtbNeF.js","assets/arrow-up-right-D82LWdhe.js","assets/QuickContacts-BkIf0XE4.js","assets/input-CWM2bTJL.js","assets/label-aQpKQY_t.js","assets/search-DYwk6_-u.js","assets/DecentralizedIdentity-Ctas3yLf.js","assets/ScheduledTransfers-BvlLS6TK.js","assets/chevron-left-CRB4yH4j.js","assets/select-uVdWjx2o.js","assets/index-B97upM1f.js","assets/chevron-down-BIMAYvOi.js","assets/badge-DogxunLX.js","assets/plus-DMc7oH9p.js","assets/switch-DAmDmXFQ.js","assets/tabs-CCtap3dh.js","assets/QRCode-DVz9xBTO.js","assets/qr-code-BnIF1LDH.js","assets/info-BYTODGZ9.js"])))=>i.map(i=>d[i]);
import{k as T,r as s,aa as B,ak as q,l as U,G as F,al as G,b as Y,S as X,i as J,j as e,n as p,o as N,p as k,s as j,am as W,an as Z,ao as K,A as C,a1 as ee,B as m,ap as se,v as ae,aq as _,ar as te,as as ce,at as le,C as ne,au as ie,D as re,d as oe,e as de,f as me,av as x,c as xe,aw as ue}from"./index-G17GlXLb.js";import{S as he}from"./switch-DAmDmXFQ.js";import{S as pe}from"./separator-BLTLWNIf.js";import{T as je,a as fe,b as o,c as d}from"./tabs-CCtap3dh.js";import{Q as ve}from"./QRCode-DVz9xBTO.js";import{S,U as be}from"./skeleton-CLgd6C74.js";import{Q as ye}from"./qr-code-BnIF1LDH.js";import{I as ge}from"./info-BYTODGZ9.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ne=T("CalendarClock",[["path",{d:"M21 7.5V6a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h3.5",key:"1osxxc"}],["path",{d:"M16 2v4",key:"4m81vk"}],["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M3 10h5",key:"r794hk"}],["path",{d:"M17.5 17.5 16 16.3V14",key:"akvzfd"}],["circle",{cx:"16",cy:"16",r:"6",key:"qoo3c4"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ke=T("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),Ce=s.lazy(()=>x(()=>import("./ProfileActivityTimeline-CPw1Sjf6.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9]))),Se=s.lazy(()=>x(()=>import("./QuickContacts-BkIf0XE4.js"),__vite__mapDeps([10,1,2,11,12,6,13,9]))),we=s.lazy(()=>x(()=>import("./DecentralizedIdentity-Ctas3yLf.js"),__vite__mapDeps([14,1,2,6]))),Te=s.lazy(()=>x(()=>import("./ScheduledTransfers-BvlLS6TK.js"),__vite__mapDeps([15,1,2,3,11,12,16,17,18,19,20,6,21,22,23,24,25,26]))),r=s.memo(()=>e.jsx("div",{className:"flex justify-center items-center py-12",children:e.jsx(xe,{size:"lg",text:"Loading..."})}));r.displayName="TabLoading";const w=[{title:"Transaction History",icon:ke,onClick:()=>console.log("Transaction History clicked")},{title:"Protected Transfers",icon:le,onClick:()=>console.log("Protected Transfers clicked")},{title:"Streaming Payments",icon:ne,onClick:()=>console.log("Streaming Payments clicked")},{title:"Group Pools",icon:_,onClick:()=>console.log("Group Pools clicked")},{title:"About STRAPT",icon:ge,onClick:()=>console.log("About clicked")}],A=s.memo(({item:c})=>e.jsx(m,{variant:"ghost",className:"w-full justify-start p-3 h-auto",onClick:c.onClick,children:e.jsxs("div",{className:"flex items-center justify-between w-full",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(c.icon,{className:"h-5 w-5 text-muted-foreground"}),e.jsx("span",{children:c.title})]}),e.jsx(ue,{className:"h-4 w-4 text-muted-foreground"})]})}));A.displayName="MenuItem";const _e=()=>{const[c,M]=s.useState(document.documentElement.classList.contains("dark")),[P,f]=s.useState(!1),[i,D]=s.useState("activity"),{toast:n}=J(),{address:l}=B(),{disconnect:v}=q(),b=U(),a=F(),{isLoading:L,idrxBalance:y}=G(),{useUserStreams:I}=Y(),{streams:u,isLoading:O}=I(l),[z,E]=s.useState(0);s.useEffect(()=>{if(u){const t=u.filter(h=>h.status===X.Active).length;E(t)}},[u]);const R=l?`${l.slice(0,6)}...${l.slice(-4)}`:"Not connected",g=s.useCallback(()=>{l&&(navigator.clipboard.writeText(l),n({title:"Address Copied",description:"Your wallet address has been copied to clipboard"}))},[l,n]),Q=s.useCallback(()=>{v(),n({title:"Signed Out",description:"Your wallet has been disconnected"}),b("/")},[v,n,b]),V=s.useCallback(()=>{const t=!c;M(t),t?document.documentElement.classList.add("dark"):document.documentElement.classList.remove("dark"),n({title:`${t?"Dark":"Light"} Mode Enabled`,description:`Switched to ${t?"dark":"light"} mode`})},[c,n]),$={address:l,username:"@vitalik.strapt",timestamp:new Date().toISOString()},H=JSON.stringify($);return e.jsxs("div",{className:"space-y-4 pb-16",children:[e.jsxs(p,{children:[e.jsx(N,{className:"pb-3",children:e.jsx(k,{children:"My Profile"})}),e.jsxs(j,{children:[e.jsxs("div",{className:`flex ${a?"flex-col":"items-center"} gap-4 mb-6`,children:[e.jsxs(W,{className:`${a?"mx-auto":""} h-16 w-16 border-2 border-primary`,children:[e.jsx(Z,{src:""}),e.jsx(K,{className:"text-lg",children:"TS"})]}),e.jsxs("div",{className:`flex-1 ${a?"text-center":""}`,children:[e.jsx("h2",{className:"text-lg font-medium",children:"@vitalik.strapt"}),e.jsxs("div",{className:`flex items-center text-sm text-muted-foreground ${a?"justify-center":""}`,children:[e.jsx("span",{className:"truncate",children:R}),e.jsx("button",{type:"button",onClick:g,className:"ml-1 p-1","aria-label":"Copy address",children:e.jsx(C,{className:"h-3.5 w-3.5"})})]})]}),e.jsxs("div",{className:`flex gap-2 ${a?"justify-center mt-2":"ml-auto"}`,children:[e.jsx(ee,{buttonVariant:"outline",buttonSize:"sm",buttonText:"Scan",iconOnly:a}),e.jsxs(m,{variant:"outline",size:"sm",onClick:()=>f(!0),children:[e.jsx(ye,{className:"h-4 w-4 mr-1"})," ",!a&&"Share"]})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[e.jsxs("div",{className:"bg-secondary/50 rounded-lg p-3 text-center",children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:"Balance"}),L?e.jsx("div",{className:"flex flex-col items-center justify-center",children:e.jsx(S,{className:"h-6 w-24 mt-1 rounded-md"})}):y?e.jsx("p",{className:"font-semibold",children:se(y.value,"IDRX")}):e.jsx("p",{className:"font-semibold",children:"0 IDRX"})]}),e.jsxs("div",{className:"bg-secondary/50 rounded-lg p-3 text-center",children:[e.jsx("p",{className:"text-sm text-muted-foreground",children:"Streams"}),O?e.jsx("div",{className:"flex flex-col items-center justify-center",children:e.jsx(S,{className:"h-6 w-16 mt-1 rounded-md"})}):e.jsxs("p",{className:"font-semibold",children:[z," Active"]})]})]})]})]}),e.jsxs(je,{value:i,onValueChange:D,className:"w-full",children:[e.jsxs(fe,{className:"flex w-full mb-4 bg-black p-0 rounded-none border-b border-purple-800/50",children:[e.jsxs(o,{value:"activity",className:"flex-1 flex flex-col items-center justify-center py-2 h-auto rounded-none data-[state=active]:bg-transparent data-[state=active]:text-purple-500 data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-purple-500",children:[e.jsx(ae,{className:"h-5 w-5 mb-1"}),e.jsx("span",{className:"text-xs",children:"Activity"})]}),e.jsxs(o,{value:"contacts",className:"flex-1 flex flex-col items-center justify-center py-2 h-auto rounded-none data-[state=active]:bg-transparent data-[state=active]:text-purple-500 data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-purple-500",children:[e.jsx(_,{className:"h-5 w-5 mb-1"}),e.jsx("span",{className:"text-xs",children:"Contacts"})]}),e.jsxs(o,{value:"scheduled",className:"flex-1 flex flex-col items-center justify-center py-2 h-auto rounded-none data-[state=active]:bg-transparent data-[state=active]:text-purple-500 data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-purple-500",children:[e.jsx(Ne,{className:"h-5 w-5 mb-1"}),e.jsx("span",{className:"text-xs",children:"Schedule"})]}),e.jsxs(o,{value:"identity",className:"flex-1 flex flex-col items-center justify-center py-2 h-auto rounded-none data-[state=active]:bg-transparent data-[state=active]:text-purple-500 data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-purple-500",children:[e.jsx(be,{className:"h-5 w-5 mb-1"}),e.jsx("span",{className:"text-xs",children:"Profile"})]})]}),e.jsx(d,{value:"activity",className:"space-y-4 mt-2",children:e.jsx(s.Suspense,{fallback:e.jsx(r,{}),children:i==="activity"&&e.jsx(Ce,{})})}),e.jsx(d,{value:"contacts",className:"space-y-4 mt-2",children:e.jsx(s.Suspense,{fallback:e.jsx(r,{}),children:i==="contacts"&&e.jsx(Se,{})})}),e.jsx(d,{value:"scheduled",className:"space-y-4 mt-2",children:e.jsx(s.Suspense,{fallback:e.jsx(r,{}),children:i==="scheduled"&&e.jsx(Te,{})})}),e.jsx(d,{value:"identity",className:"space-y-4 mt-2",children:e.jsx(s.Suspense,{fallback:e.jsx(r,{}),children:i==="identity"&&e.jsx(we,{})})})]}),e.jsxs(p,{children:[e.jsx(N,{className:"pb-3",children:e.jsx(k,{children:"Settings"})}),e.jsx(j,{className:"space-y-4",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[c?e.jsx(te,{className:"h-5 w-5"}):e.jsx(ce,{className:"h-5 w-5"}),e.jsx("span",{children:"Dark Mode"})]}),e.jsx(he,{checked:c,onCheckedChange:V})]})})]}),e.jsx(p,{children:e.jsx(j,{className:"p-0",children:w.map((t,h)=>e.jsxs("div",{children:[e.jsx(A,{item:t}),h<w.length-1&&e.jsx(pe,{})]},t.title))})}),e.jsxs(m,{variant:"destructive",className:"w-full flex items-center gap-2",onClick:Q,children:[e.jsx(ie,{className:"h-4 w-4"})," Sign Out"]}),e.jsx(re,{open:P,onOpenChange:f,children:e.jsxs(oe,{className:a?"sm:max-w-[92%] w-[92%] mx-auto rounded-xl px-3 py-4":"max-w-xs mx-auto sm:max-w-md",children:[e.jsx(de,{children:e.jsx(me,{children:"Your Profile QR Code"})}),e.jsxs("div",{className:"flex flex-col items-center justify-center space-y-4",children:[e.jsx(ve,{value:H,size:a?180:200,renderAsImage:!0}),e.jsx("p",{className:"text-sm text-center text-muted-foreground",children:"Share this code with friends so they can easily find and send you money"}),e.jsxs(m,{size:"sm",variant:"outline",onClick:g,children:[e.jsx(C,{className:"h-4 w-4 mr-1"})," Copy Address"]})]})]})})]})},Ee=Object.freeze(Object.defineProperty({__proto__:null,default:_e},Symbol.toStringTag,{value:"Module"}));export{Ne as C,Ee as O};
