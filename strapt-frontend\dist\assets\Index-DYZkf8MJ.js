import{k as d,aa as u,u as f,l as b,r as i,j as e,B as l,ad as o,at as g,C as p,aq as j,az as v,a0 as N}from"./index-G17GlXLb.js";import{A as c}from"./arrow-right-gM81zKuh.js";import{C as y}from"./circle-check-big-BugZDCTu.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const m=d("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const w=d("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const C=d("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),P=()=>{const{isConnected:r}=u(),{connectWallet:x}=f(),t=b(),[s,n]=i.useState(!1);i.useEffect(()=>{r&&t("/app")},[r,t]);const a=async()=>{if(console.log("Launch App clicked, isConnected:",r),r)console.log("Already connected, navigating to /app"),t("/app");else{console.log("Not connected, attempting to connect wallet"),n(!0);try{console.log("Calling connectWallet()"),await x(),console.log("connectWallet() completed")}catch(h){console.error("Error connecting wallet:",h)}finally{n(!1)}}};return e.jsxs("div",{className:"min-h-screen bg-background",children:[e.jsx("header",{className:"border-b border-border",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"flex justify-between items-center py-4",children:[e.jsx("h1",{className:"text-2xl font-bold gradient-text",children:"STRAPT"}),e.jsxs("div",{className:"hidden md:flex items-center space-x-6",children:[e.jsx("a",{href:"#features",className:"text-muted-foreground hover:text-foreground transition-colors",children:"Features"}),e.jsx("a",{href:"#why",className:"text-muted-foreground hover:text-foreground transition-colors",children:"Why STRAPT"}),e.jsx("a",{href:"#showcase",className:"text-muted-foreground hover:text-foreground transition-colors",children:"Showcase"})]}),e.jsx(l,{onClick:a,className:"md:hidden",disabled:s,children:s?e.jsxs(e.Fragment,{children:[e.jsx(o,{className:"mr-2 h-4 w-4 animate-spin"}),"Connecting..."]}):"Launch App"})]})})}),e.jsxs("section",{className:"pt-10 pb-20 px-4",children:[e.jsx("div",{className:"max-w-7xl mx-auto",children:e.jsxs("div",{className:"md:flex md:items-center md:space-x-12",children:[e.jsxs("div",{className:"text-center md:text-left md:w-1/2 mb-8 md:mb-0",children:[e.jsx("h1",{className:"text-4xl md:text-5xl lg:text-6xl font-bold mb-4 gradient-text",children:"Send It, Stream It, STRAPT It."}),e.jsx("p",{className:"text-lg text-muted-foreground mb-8 max-w-2xl",children:"The fastest way to move crypto — no addresses, no stress. Powered by IDRX."}),e.jsx("div",{className:"flex flex-col sm:flex-row gap-4 justify-center md:justify-start mb-8",children:e.jsx(l,{onClick:a,className:"w-full sm:w-auto text-base font-medium",disabled:s,children:s?e.jsxs(e.Fragment,{children:[e.jsx(o,{className:"mr-2 h-4 w-4 animate-spin"}),"Connecting..."]}):e.jsxs(e.Fragment,{children:["Launch App ",e.jsx(c,{className:"ml-2 h-4 w-4"})]})})})]}),e.jsx("div",{className:"mx-auto max-w-xs md:max-w-sm md:w-1/2",children:e.jsxs("div",{className:"relative",children:[e.jsxs("div",{className:"rounded-[3rem] bg-card p-4 overflow-hidden border-8 border-foreground/10 shadow-xl",children:[e.jsx("div",{className:"absolute top-0 left-1/2 transform -translate-x-1/2 w-1/3 h-6 bg-foreground/10 rounded-b-xl z-10"}),e.jsx("div",{className:"relative rounded-2xl overflow-hidden bg-gradient-to-br from-primary/20 to-accent/20 aspect-[9/19]",children:e.jsx("img",{src:"/home-mobile.png",alt:"STRAPT Home Dashboard",className:"w-full h-auto"})})]}),e.jsx("div",{className:"absolute -bottom-4 -right-4 w-24 h-24 bg-gradient-to-br from-primary to-accent rounded-full blur-xl opacity-30"})]})})]})}),e.jsxs("div",{className:"max-w-7xl mx-auto mt-24",id:"features",children:[e.jsx("h2",{className:"text-2xl md:text-3xl font-bold mb-6 text-center gradient-text",children:"Features"}),e.jsx("p",{className:"text-center text-muted-foreground max-w-2xl mx-auto mb-10",children:"STRAPT offers a comprehensive suite of financial tools designed for the modern crypto user."}),e.jsxs("div",{className:"grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"flex flex-col items-start p-6 rounded-2xl bg-card border border-primary/10 hover:border-primary/30 hover:shadow-lg hover:-translate-y-1 transition-all duration-300 h-full",children:[e.jsx("div",{className:"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4",children:e.jsx(g,{className:"h-6 w-6 text-primary"})}),e.jsx("h3",{className:"font-semibold text-base mb-2",children:"Protected Transfers"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Send crypto with passwords or QR links — no need to drop addresses ever again. Perfect for secure person-to-person payments."})]}),e.jsxs("div",{className:"flex flex-col items-start p-6 rounded-2xl bg-card border border-primary/10 hover:border-primary/30 hover:shadow-lg hover:-translate-y-1 transition-all duration-300 h-full",children:[e.jsx("div",{className:"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4",children:e.jsx(p,{className:"h-6 w-6 text-primary"})}),e.jsx("h3",{className:"font-semibold text-base mb-2",children:"Streaming Payments"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Automate salaries, subscriptions, and project payouts, right from your wallet. Set it up once and let the funds flow continuously."})]}),e.jsxs("div",{className:"flex flex-col items-start p-6 rounded-2xl bg-card border border-primary/10 hover:border-primary/30 hover:shadow-lg hover:-translate-y-1 transition-all duration-300 h-full",children:[e.jsx("div",{className:"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4",children:e.jsx(j,{className:"h-6 w-6 text-primary"})}),e.jsx("h3",{className:"font-semibold text-base mb-2",children:"Group Pools"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Easily collect, split, or manage pooled funds with friends or teams. Perfect for group expenses, projects, or community treasuries."})]}),e.jsxs("div",{className:"flex flex-col items-start p-6 rounded-2xl bg-card border border-primary/10 hover:border-primary/30 hover:shadow-lg hover:-translate-y-1 transition-all duration-300 h-full",children:[e.jsx("div",{className:"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4",children:e.jsx(v,{className:"h-6 w-6 text-primary"})}),e.jsx("h3",{className:"font-semibold text-base mb-2",children:"Crypto Savings"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Set targets, earn while you wait. Your DeFi piggy bank, simplified. Create savings goals and watch your assets grow over time."})]}),e.jsxs("div",{className:"flex flex-col items-start p-6 rounded-2xl bg-card border border-primary/10 hover:border-primary/30 hover:shadow-lg hover:-translate-y-1 transition-all duration-300 h-full",children:[e.jsx("div",{className:"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4",children:e.jsx(N,{className:"h-6 w-6 text-primary"})}),e.jsx("h3",{className:"font-semibold text-base mb-2",children:"Token Faucet"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"No IDRX? No problem. Grab free tokens and start exploring instantly. Our faucet provides the tokens you need to test all features."})]}),e.jsxs("div",{className:"flex flex-col items-start p-6 rounded-2xl bg-card border border-primary/10 hover:border-primary/30 hover:shadow-lg hover:-translate-y-1 transition-all duration-300 h-full",children:[e.jsx("div",{className:"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-4",children:e.jsx(m,{className:"h-6 w-6 text-primary"})}),e.jsx("h3",{className:"font-semibold text-base mb-2",children:"Mini App"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Coming soon: Send and receive payments directly from your Telegram chats. Seamless integration with your favorite messaging platform."})]})]})]}),e.jsxs("div",{className:"max-w-7xl mx-auto mt-24",id:"why",children:[e.jsx("h2",{className:"text-2xl md:text-3xl font-bold mb-10 text-center gradient-text",children:"Why STRAPT?"}),e.jsxs("div",{className:"md:grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"flex items-start p-6 rounded-2xl bg-card border border-primary/10 hover:border-primary/30 hover:shadow-lg transition-all duration-300 mb-4 md:mb-0",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-4 flex-shrink-0",children:e.jsx(y,{className:"h-5 w-5 text-primary"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-2",children:"No scams, no stress"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Built-in protections ensure your crypto arrives safely to its destination."})]})]}),e.jsxs("div",{className:"flex items-start p-6 rounded-2xl bg-card border border-primary/10 hover:border-primary/30 hover:shadow-lg transition-all duration-300 mb-4 md:mb-0",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-4 flex-shrink-0",children:e.jsx(C,{className:"h-5 w-5 text-primary"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-2",children:"Easy as Venmo, secure as blockchain"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Familiar payment experience, but with the security of Web3."})]})]}),e.jsxs("div",{className:"flex items-start p-6 rounded-2xl bg-card border border-primary/10 hover:border-primary/30 hover:shadow-lg transition-all duration-300 mb-4 md:mb-0",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center mr-4 flex-shrink-0",children:e.jsx(w,{className:"h-5 w-5 text-primary"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-semibold mb-2",children:"Mobile-first & responsive design"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Optimized for smartphones and desktops with intuitive flows."})]})]})]})]}),e.jsxs("div",{className:"max-w-7xl mx-auto mt-24 px-4",id:"showcase",children:[e.jsxs("div",{className:"mb-12",children:[e.jsx("h2",{className:"text-2xl md:text-3xl font-bold mb-6 text-center gradient-text",children:"Experience STRAPT"}),e.jsx("p",{className:"text-center text-muted-foreground max-w-2xl mx-auto",children:"Explore our intuitive interface designed for seamless crypto transactions"})]}),e.jsx("div",{className:"flex flex-col items-center justify-center",children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 lg:gap-12 max-w-6xl mx-auto",children:[e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("div",{className:"mb-4 text-center",children:e.jsx("h3",{className:"font-medium text-primary",children:"Protected Transfers"})}),e.jsxs("div",{className:"relative mx-auto hover:-translate-y-2 transition-transform duration-300",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/30 to-accent/30 rounded-[2.5rem] blur-xl opacity-30 transform -rotate-6"}),e.jsxs("div",{className:"relative rounded-[2.5rem] border-8 border-foreground/10 shadow-xl overflow-hidden bg-card",children:[e.jsx("div",{className:"absolute top-0 left-1/2 transform -translate-x-1/2 w-1/3 h-6 bg-foreground/10 rounded-b-xl z-10"}),e.jsx("img",{src:"/transfer-mobile.png",alt:"STRAPT Transfer Interface",className:"w-full h-auto"})]})]})]}),e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("div",{className:"mb-4 text-center",children:e.jsx("h3",{className:"font-medium text-primary",children:"Home Dashboard"})}),e.jsxs("div",{className:"relative mx-auto hover:-translate-y-2 transition-transform duration-300",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/30 to-accent/30 rounded-[2.5rem] blur-xl opacity-30 transform rotate-3"}),e.jsxs("div",{className:"relative rounded-[2.5rem] border-8 border-foreground/10 shadow-xl overflow-hidden bg-card",children:[e.jsx("div",{className:"absolute top-0 left-1/2 transform -translate-x-1/2 w-1/3 h-6 bg-foreground/10 rounded-b-xl z-10"}),e.jsx("img",{src:"/home-mobile.png",alt:"STRAPT Home Dashboard",className:"w-full h-auto"})]})]})]}),e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("div",{className:"mb-4 text-center",children:e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsx("h3",{className:"font-bold text-lg text-primary",children:"STRAPT Drop"}),e.jsx("div",{className:"ml-2 bg-gradient-to-r from-primary to-accent text-white text-xs font-bold px-2 py-0.5 rounded-full",children:"Featured"})]})}),e.jsxs("div",{className:"relative mx-auto hover:-translate-y-2 transition-transform duration-300",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-primary/40 to-accent/40 rounded-[2.5rem] blur-xl opacity-40"}),e.jsxs("div",{className:"relative rounded-[2.5rem] border-8 border-foreground/10 shadow-xl overflow-hidden bg-card",children:[e.jsx("div",{className:"absolute top-0 left-1/2 transform -translate-x-1/2 w-1/3 h-6 bg-foreground/10 rounded-b-xl z-10"}),e.jsx("img",{src:"/drop-mobile.png",alt:"STRAPT Drop Interface",className:"w-full h-auto"})]})]})]}),e.jsxs("div",{className:"flex flex-col items-center",children:[e.jsx("div",{className:"mb-4 text-center",children:e.jsx("h3",{className:"font-medium text-accent",children:"Payment Streams"})}),e.jsxs("div",{className:"relative mx-auto hover:-translate-y-2 transition-transform duration-300",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-br from-accent/30 to-primary/30 rounded-[2.5rem] blur-xl opacity-30 transform rotate-6"}),e.jsxs("div",{className:"relative rounded-[2.5rem] border-8 border-foreground/10 shadow-xl overflow-hidden bg-card",children:[e.jsx("div",{className:"absolute top-0 left-1/2 transform -translate-x-1/2 w-1/3 h-6 bg-foreground/10 rounded-b-xl z-10"}),e.jsx("img",{src:"/payment-streams-mobile.png",alt:"STRAPT Payment Streams Interface",className:"w-full h-auto"})]})]})]})]})})]}),e.jsx("div",{className:"max-w-4xl mx-auto mt-24 text-center",children:e.jsxs("div",{className:"bg-gradient-to-r from-primary/20 to-accent/20 rounded-2xl p-8 md:p-12 border border-primary/10 shadow-lg",children:[e.jsx("h2",{className:"text-2xl md:text-3xl font-bold mb-4 gradient-text",children:"Ready to get started?"}),e.jsx("p",{className:"text-lg mb-8 max-w-2xl mx-auto text-muted-foreground",children:"Thousands already STRAPT their crypto — it's time to send smarter."}),e.jsx(l,{onClick:a,className:"w-full sm:w-auto text-base font-medium",disabled:s,children:s?e.jsxs(e.Fragment,{children:[e.jsx(o,{className:"mr-2 h-4 w-4 animate-spin"}),"Connecting..."]}):e.jsxs(e.Fragment,{children:["Launch App ",e.jsx(c,{className:"ml-2 h-4 w-4"})]})})]})}),e.jsxs("footer",{className:"max-w-7xl mx-auto mt-24 pt-8 border-t border-border",children:[e.jsxs("div",{className:"md:flex md:justify-between",children:[e.jsxs("div",{className:"mb-6 md:mb-0",children:[e.jsx("h1",{className:"text-xl font-bold gradient-text mb-4",children:"STRAPT"}),e.jsx("p",{className:"text-sm text-muted-foreground max-w-xs",children:"Secure crypto payments, simplified for Web3 users."})]}),e.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-3 gap-8",children:[e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-sm font-semibold uppercase",children:"Resources"}),e.jsxs("ul",{className:"text-sm space-y-2",children:[e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-muted-foreground hover:text-foreground",children:"Docs"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-muted-foreground hover:text-foreground",children:"GitHub"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-muted-foreground hover:text-foreground",children:"Blog"})})]})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-sm font-semibold uppercase",children:"Legal"}),e.jsxs("ul",{className:"text-sm space-y-2",children:[e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-muted-foreground hover:text-foreground",children:"Privacy Policy"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-muted-foreground hover:text-foreground",children:"Terms of Service"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-muted-foreground hover:text-foreground",children:"Cookies"})})]})]}),e.jsxs("div",{children:[e.jsx("h2",{className:"mb-4 text-sm font-semibold uppercase",children:"Contact"}),e.jsxs("ul",{className:"text-sm space-y-2",children:[e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-muted-foreground hover:text-foreground",children:"Contact Us"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-muted-foreground hover:text-foreground",children:"Support"})}),e.jsx("li",{children:e.jsx("a",{href:"#",className:"text-muted-foreground hover:text-foreground",children:"Twitter"})})]})]})]})]}),e.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mt-8 pt-8 border-t border-border",children:[e.jsxs("p",{className:"text-xs text-muted-foreground mb-4 sm:mb-0",children:["© ",new Date().getFullYear()," STRAPT. All rights reserved."]}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("a",{href:"#",className:"p-2 rounded-full bg-secondary hover:bg-secondary/70 transition-colors",children:e.jsx(m,{className:"h-5 w-5"})}),e.jsx("a",{href:"#",className:"p-2 rounded-full bg-secondary hover:bg-secondary/70 transition-colors",children:e.jsx("svg",{className:"h-5 w-5",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M22.675 0H1.325C0.593 0 0 0.593 0 1.325V22.676C0 23.407 0.593 24 1.325 24H12.82V14.706H9.692V11.084H12.82V8.413C12.82 5.313 14.713 3.625 17.479 3.625C18.804 3.625 19.942 3.724 20.274 3.768V7.008H18.356C16.852 7.008 16.561 7.724 16.561 8.772V11.085H20.148L19.681 14.707H16.561V24H22.677C23.407 24 24 23.407 24 22.675V1.325C24 0.593 23.407 0 22.675 0Z",fill:"currentColor"})})}),e.jsx("a",{href:"#",className:"p-2 rounded-full bg-secondary hover:bg-secondary/70 transition-colors",children:e.jsx("svg",{className:"h-5 w-5",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M23.643 4.937C22.808 5.307 21.911 5.557 20.968 5.67C21.941 5.08 22.669 4.17 23.016 3.092C22.116 3.626 21.119 4.01 20.058 4.222C19.208 3.319 17.998 2.75 16.658 2.75C14.086 2.75 12 4.836 12 7.407C12 7.766 12.042 8.115 12.12 8.45C8.247 8.261 4.81 6.416 2.518 3.639C2.118 4.323 1.891 5.08 1.891 5.887C1.891 7.409 2.664 8.744 3.868 9.498C3.106 9.474 2.389 9.267 1.758 8.921C1.758 8.941 1.758 8.962 1.758 8.983C1.758 11.255 3.352 13.152 5.465 13.574C5.075 13.681 4.665 13.738 4.242 13.738C3.939 13.738 3.644 13.709 3.357 13.654C3.956 15.517 5.692 16.873 7.742 16.91C6.129 18.175 4.097 18.922 1.89 18.922C1.515 18.922 1.143 18.9 0.779 18.855C2.85 20.196 5.303 20.969 7.958 20.969C16.647 20.969 21.389 13.815 21.389 7.619C21.389 7.419 21.385 7.22 21.376 7.023C22.286 6.35 23.073 5.513 23.641 4.542L23.643 4.937Z",fill:"currentColor"})})})]})]})]})]})]})};export{P as default};
