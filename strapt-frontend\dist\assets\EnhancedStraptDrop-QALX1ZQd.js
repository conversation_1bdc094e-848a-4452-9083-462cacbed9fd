import{l as M,u as q,a as G,r as t,m as V,j as e,B as l,n as W,o as Y,p as J,q as K,s as Z,t as _,v as $,w as ee,c as C,D,d as k,e as S,f as T,x as se,g as R,T as ae,y as te,z as re,A as ie,E as le,h as A,F as ne}from"./index-G17GlXLb.js";import{I as f}from"./input-CWM2bTJL.js";import{L as c}from"./label-aQpKQY_t.js";import{S as oe}from"./switch-DAmDmXFQ.js";import{u as ce}from"./use-strapt-drop-D-qrso9N.js";import{Q as me}from"./QRCode-DVz9xBTO.js";import{T as de}from"./TokenSelect-DYUulU6S.js";import{C as xe}from"./chevron-left-CRB4yH4j.js";import{A as pe}from"./index-94PVxzUY.js";import{m as ue}from"./proxy-B8vTGf2f.js";import{G as E}from"./gift-Cgh4t_YK.js";import{S as he,C as fe}from"./shuffle-CGODj-xY.js";import{Q as I}from"./qr-code-BnIF1LDH.js";import"./index-B97upM1f.js";import"./StraptDrop-DBtBqCxY.js";import"./search-DYwk6_-u.js";import"./chevron-down-BIMAYvOi.js";const Fe=()=>{const x=M();q();const{createDrop:L,isLoading:F,isApproving:b,isCreating:j,currentDropId:p}=ce(),{tokens:u}=G(),[N,g]=t.useState("IDRX"),[i,y]=t.useState(""),[r,v]=t.useState("10"),[m,w]=t.useState(!1),Q=24,a=u.find(s=>s.symbol===N)||u[0],[P,o]=t.useState(!1),[B,d]=t.useState(!1),[h,X]=t.useState(""),[n,z]=t.useState({});t.useEffect(()=>()=>{o(!1),d(!1)},[]),t.useEffect(()=>{if(p){const s=V(p);X(s),o(!0)}},[p]);const O=()=>{const s={};return i?Number.isNaN(Number(i))||Number(i)<=0?s.amount="Amount must be a positive number":a&&a.balance!==void 0&&Number(i)>a.balance&&(s.amount="Amount exceeds your balance"):s.amount="Amount is required",r?Number.isNaN(Number(r))||Number(r)<=0||!Number.isInteger(Number(r))?s.recipients="Recipients must be a positive integer":Number(r)>100&&(s.recipients="Maximum 100 recipients allowed"):s.recipients="Number of recipients is required",z(s),Object.keys(s).length===0},U=async()=>{if(O())try{await L(N,i,Number.parseInt(r),m,Q,"")}catch(s){console.error("Error creating drop:",s)}},H=()=>{navigator.clipboard.writeText(h),ne.success("Link copied to clipboard")};return e.jsxs("div",{className:"container max-w-3xl mx-auto py-4 px-4 sm:px-6 sm:py-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(l,{variant:"ghost",size:"icon",onClick:()=>x("/app"),className:"mr-1","aria-label":"Back to App",children:e.jsx(xe,{className:"h-5 w-5"})}),e.jsx("h1",{className:"text-xl sm:text-2xl font-bold",children:"Create STRAPT Drop"})]}),e.jsx(l,{variant:"outline",onClick:()=>x("/app/strapt-drop/my-drops"),className:"w-full sm:w-auto",children:"View My Drops"})]}),e.jsx(pe,{mode:"wait",children:e.jsx(ue.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},transition:{duration:.3},children:e.jsxs(W,{className:"border-primary/20 shadow-md overflow-hidden",children:[e.jsxs(Y,{className:"bg-muted/20 border-b border-border pb-4",children:[e.jsxs(J,{className:"flex items-center gap-2 text-lg",children:[e.jsx(E,{className:"h-5 w-5 text-primary"}),"Create New STRAPT Drop"]}),e.jsx(K,{children:"Distribute tokens to multiple recipients with a single transaction"})]}),e.jsxs(Z,{className:"space-y-5 pt-5",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx("div",{className:"flex justify-between",children:e.jsx(c,{htmlFor:"token-type",className:"text-sm font-medium",children:"Token"})}),e.jsx(de,{tokens:u.map(s=>({symbol:s.symbol,name:s.name||s.symbol,balance:s.balance,icon:s.symbol==="USDC"?"/usd-coin-usdc-logo.svg":s.symbol==="IDRX"?"/IDRX BLUE COIN.svg":void 0})),selectedToken:{symbol:a.symbol,name:a.name||a.symbol,balance:a.balance,icon:a.symbol==="USDC"?"/usd-coin-usdc-logo.svg":a.symbol==="IDRX"?"/IDRX BLUE COIN.svg":void 0},onTokenChange:s=>g(s.symbol),className:"w-full"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx(c,{htmlFor:"amount",className:"text-sm font-medium",children:"Total Amount"}),a&&a.balance!==void 0&&e.jsxs("span",{className:"text-xs text-muted-foreground",children:["Balance: ",a.balance.toLocaleString()," ",a.symbol]})]}),e.jsx(f,{id:"amount",type:"text",inputMode:"decimal",placeholder:"Enter amount",value:i,onChange:s=>y(s.target.value),className:n.amount?"border-destructive":""}),n.amount&&e.jsx("p",{className:"text-xs text-destructive",children:n.amount})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(c,{htmlFor:"recipients",className:"text-sm font-medium",children:"Number of Recipients"}),e.jsx(f,{id:"recipients",type:"number",min:"1",max:"100",placeholder:"Enter number of recipients",value:r,onChange:s=>v(s.target.value),className:n.recipients?"border-destructive":""}),n.recipients&&e.jsx("p",{className:"text-xs text-destructive",children:n.recipients})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(c,{htmlFor:"distribution-type",className:"text-sm font-medium",children:"Random Distribution"}),e.jsx(oe,{id:"distribution-type",checked:m,onCheckedChange:w})]}),e.jsx("div",{className:_("p-3 rounded-lg border",m?"bg-amber-500/10 border-amber-500/30":"bg-blue-500/10 border-blue-500/30"),children:m?e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(he,{className:"h-5 w-5 text-amber-500 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-amber-600 dark:text-amber-400",children:"Random Distribution"}),e.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:"Recipients will receive random amounts between 1% and 200% of the average amount."})]})]}):e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(fe,{className:"h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-blue-600 dark:text-blue-400",children:"Fixed Distribution"}),e.jsxs("p",{className:"text-xs text-muted-foreground mt-1",children:["Each recipient will receive exactly ",i&&r?(Number(i)/Number(r)).toLocaleString():"0"," ",a==null?void 0:a.symbol,"."]})]})]})})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(c,{className:"text-sm font-medium",children:"Expiry Time"}),e.jsxs("div",{className:"flex items-start gap-3 p-3 rounded-lg bg-muted/30 border border-muted",children:[e.jsx($,{className:"h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:"24 Hours"}),e.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:"Unclaimed tokens can be refunded after 24 hours."})]})]})]})]}),e.jsx(ee,{className:"flex justify-end pt-2 pb-6 px-6 border-t border-border",children:e.jsx(l,{onClick:U,disabled:F||b||j,className:"min-w-32",children:b?e.jsxs(e.Fragment,{children:[e.jsx(C,{size:"sm",className:"mr-2"}),"Approving..."]}):j?e.jsxs(e.Fragment,{children:[e.jsx(C,{size:"sm",className:"mr-2"}),"Creating..."]}):e.jsxs(e.Fragment,{children:[e.jsx(E,{className:"h-4 w-4 mr-2"}),"Create Drop"]})})})]})},"form")}),e.jsx(D,{open:P,onOpenChange:o,children:e.jsxs(k,{className:"sm:max-w-md p-0 overflow-hidden",children:[e.jsxs(S,{className:"p-6 pb-2",children:[e.jsxs(T,{className:"flex items-center gap-2 text-xl",children:[e.jsx("div",{className:"w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center",children:e.jsx(se,{className:"h-5 w-5 text-green-500"})}),"STRAPT Drop Created!"]}),e.jsx(R,{className:"text-base pt-2",children:"Your STRAPT Drop has been created successfully. Share the link with recipients."})]}),e.jsxs("div",{className:"px-6 py-4 space-y-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(f,{readOnly:!0,value:h,className:"flex-1 text-sm"}),e.jsx(ae,{children:e.jsxs(te,{children:[e.jsx(re,{asChild:!0,children:e.jsx(l,{size:"icon",variant:"outline",onClick:H,children:e.jsx(ie,{className:"h-4 w-4"})})}),e.jsx(le,{children:e.jsx("p",{children:"Copy link"})})]})})]}),e.jsx("div",{className:"flex justify-center",children:e.jsxs(l,{variant:"outline",onClick:()=>d(!0),className:"w-full sm:w-auto",children:[e.jsx(I,{className:"h-4 w-4 mr-2"}),"Show QR Code"]})})]}),e.jsx(A,{className:"px-6 py-4 border-t border-border",children:e.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 w-full sm:justify-between",children:[e.jsx(l,{variant:"outline",onClick:()=>{o(!1),x("/app/strapt-drop/my-drops")},className:"w-full sm:w-auto order-2 sm:order-1",children:"View My Drops"}),e.jsx(l,{onClick:()=>{o(!1),g("IDRX"),y(""),v("10"),w(!1)},className:"w-full sm:w-auto order-1 sm:order-2",children:"Create Another Drop"})]})})]})}),e.jsx(D,{open:B,onOpenChange:d,children:e.jsxs(k,{className:"sm:max-w-md p-0 overflow-hidden",children:[e.jsxs(S,{className:"p-6 pb-2",children:[e.jsxs(T,{className:"flex items-center gap-2",children:[e.jsx(I,{className:"h-5 w-5 text-primary"}),"STRAPT Drop QR Code"]}),e.jsx(R,{children:"Scan this QR code to claim tokens from the STRAPT Drop"})]}),e.jsx("div",{className:"flex justify-center py-6",children:e.jsx(me,{value:h,size:250})}),e.jsx(A,{className:"px-6 py-4 border-t border-border",children:e.jsx(l,{onClick:()=>d(!1),className:"w-full sm:w-auto",children:"Close"})})]})})]})};export{Fe as default};
