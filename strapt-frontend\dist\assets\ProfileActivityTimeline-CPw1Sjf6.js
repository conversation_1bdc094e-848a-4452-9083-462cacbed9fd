import{k as N,r as o,u as v,ax as A,ay as C,Z as k,j as t,n as $,o as I,p as T,t as l,s as E,C as L,aq as z}from"./index-G17GlXLb.js";import{S as O}from"./separator-BLTLWNIf.js";import{u as R}from"./use-strapt-drop-D-qrso9N.js";import{S as p}from"./skeleton-CLgd6C74.js";import{G as U}from"./gift-Cgh4t_YK.js";import{S as P}from"./shield-check-DdOtbNeF.js";import{A as F}from"./arrow-up-right-D82LWdhe.js";import"./StraptDrop-DBtBqCxY.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const G=N("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),y=[{id:"1",type:"transfer",title:"Transfer sent to Sarah",amount:"245 SEI",status:"completed",timestamp:new Date(Date.now()-864e5*1).toISOString()},{id:"2",type:"claim",title:"Protected transfer created",amount:"100 SEI",status:"pending",timestamp:new Date(Date.now()-864e5*2).toISOString()},{id:"3",type:"stream",title:"Stream payment to Team",amount:"500 SEI",status:"active",timestamp:new Date(Date.now()-864e5*3).toISOString()},{id:"4",type:"pool",title:"Group pool contribution",amount:"50 SEI",status:"completed",timestamp:new Date(Date.now()-864e5*5).toISOString()},{id:"5",type:"transfer",title:"Transfer received from Alex",amount:"75 SEI",status:"completed",timestamp:new Date(Date.now()-864e5*6).toISOString()},{id:"6",type:"claim",title:"Transfer claim expired",amount:"15 SEI",status:"failed",timestamp:new Date(Date.now()-864e5*8).toISOString()}];function q(){const[c,u]=o.useState([]),[x,d]=o.useState(!0),{address:a}=v(),{transfers:r,isLoading:f}=A(),{transfers:m,isLoading:h}=C(),{streams:s,isLoading:i}=k(),{getUserCreatedDrops:w,isLoadingUserDrops:S}=R(),[g,b]=o.useState([]);return o.useEffect(()=>{(async()=>{if(a)try{const n=await w();b(n||[])}catch(n){console.error("Error fetching user drops:",n),b([])}})()},[a,w]),o.useEffect(()=>{(()=>{if(!a){u(y),d(!1);return}if(f||h||i||S)return;const n=[];r&&r.length>0&&r.forEach(e=>{n.push({id:`transfer-sent-${e.id}`,type:"transfer",title:`Transfer sent to ${e.recipient?`${e.recipient.slice(0,6)}...${e.recipient.slice(-4)}`:"recipient"}`,amount:`${e.amount} ${e.tokenSymbol||"tokens"}`,status:e.status==="Claimed"?"completed":e.status==="Refunded"?"failed":"pending",timestamp:new Date(Number(e.createdAt)*1e3).toISOString()})}),m&&m.length>0&&m.forEach(e=>{n.push({id:`transfer-received-${e.id}`,type:"claim",title:`Transfer received from ${e.sender?`${e.sender.slice(0,6)}...${e.sender.slice(-4)}`:"sender"}`,amount:`${e.amount} ${e.tokenSymbol||"tokens"}`,status:e.status==="Claimed"?"completed":e.status==="Refunded"?"failed":"pending",timestamp:new Date(Number(e.createdAt)*1e3).toISOString()})}),s&&s.length>0&&s.forEach(e=>{n.push({id:`stream-${e.streamId}`,type:"stream",title:e.sender===a?`Stream payment to ${e.recipient.slice(0,6)}...${e.recipient.slice(-4)}`:`Stream payment from ${e.sender.slice(0,6)}...${e.sender.slice(-4)}`,amount:`${e.totalAmount} ${e.tokenSymbol||"tokens"}`,status:e.status==="Active"?"active":e.status==="Completed"?"completed":e.status==="Cancelled"?"failed":"pending",timestamp:new Date(Number(e.startTime)*1e3).toISOString()})}),g&&g.length>0&&g.forEach(e=>{n.push({id:`drop-${e.id}`,type:"drop",title:"STRAPT Drop created",amount:`${Number(e.info.totalAmount)/10**(e.info.tokenAddress==="0xD63029C1a3dA68b51c67c6D1DeC3DEe50D681661"?2:6)} ${e.info.tokenAddress==="0xD63029C1a3dA68b51c67c6D1DeC3DEe50D681661"?"IDRX":"USDC"}`,status:e.info.isActive?"active":e.info.remainingAmount===BigInt(0)?"completed":"pending",timestamp:new Date(Number(e.info.expiryTime)*1e3-864e5).toISOString()})}),n.sort((e,j)=>new Date(j.timestamp).getTime()-new Date(e.timestamp).getTime()),n.length>0?u(n):u(y),d(!1)})()},[a,r,m,s,g,f,h,i,S]),{activities:c,isLoading:x}}const K=()=>{const{activities:c,isLoading:u}=q(),[x,d]=o.useState([]),[a,r]=o.useState(null);o.useEffect(()=>{if(!c){d([]);return}if(!a){d(c);return}const s=c.filter(i=>i.type===a);d(s)},[c,a]);const f=s=>{try{const i=new Date(s);return new Intl.DateTimeFormat("en-US",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(i)}catch(i){return console.error("Error formatting date:",i),"Unknown date"}},m=s=>{switch(s){case"transfer":return t.jsx(F,{className:"h-5 w-5"});case"claim":return t.jsx(P,{className:"h-5 w-5"});case"pool":return t.jsx(z,{className:"h-5 w-5"});case"stream":return t.jsx(L,{className:"h-5 w-5"});case"drop":return t.jsx(U,{className:"h-5 w-5"});default:return t.jsx(G,{className:"h-5 w-5"})}},h=s=>{switch(s){case"completed":return"bg-green-500";case"pending":return"bg-amber-500";case"active":return"bg-blue-500";case"failed":return"bg-red-500";default:return"bg-gray-500"}};return t.jsxs($,{className:"bg-black rounded-t-none border-t-0",children:[t.jsxs(I,{className:"pb-2",children:[t.jsx(T,{className:"text-xl",children:"Recent Activity"}),t.jsxs("div",{className:"flex gap-2 mt-2 overflow-x-auto pb-1 no-scrollbar",children:[t.jsx("button",{type:"button",onClick:()=>r(null),className:l("text-xs px-3 py-1 rounded-full whitespace-nowrap",a?"bg-zinc-800 text-zinc-400":"bg-purple-600 text-white"),children:"All"}),t.jsx("button",{type:"button",onClick:()=>r("transfer"),className:l("text-xs px-3 py-1 rounded-full whitespace-nowrap",a==="transfer"?"bg-purple-600 text-white":"bg-zinc-800 text-zinc-400"),children:"Transfers"}),t.jsx("button",{type:"button",onClick:()=>r("stream"),className:l("text-xs px-3 py-1 rounded-full whitespace-nowrap",a==="stream"?"bg-purple-600 text-white":"bg-zinc-800 text-zinc-400"),children:"Streams"}),t.jsx("button",{type:"button",onClick:()=>r("claim"),className:l("text-xs px-3 py-1 rounded-full whitespace-nowrap",a==="claim"?"bg-purple-600 text-white":"bg-zinc-800 text-zinc-400"),children:"Claims"}),t.jsx("button",{type:"button",onClick:()=>r("drop"),className:l("text-xs px-3 py-1 rounded-full whitespace-nowrap",a==="drop"?"bg-purple-600 text-white":"bg-zinc-800 text-zinc-400"),children:"Drops"})]})]}),t.jsx(E,{children:t.jsx("div",{className:"space-y-2",children:u?Array.from({length:4}).map((s,i)=>t.jsxs("div",{className:"flex items-start gap-3 py-2",children:[t.jsx(p,{className:"h-9 w-9 rounded-full"}),t.jsx("div",{className:"flex-1",children:t.jsxs("div",{className:"flex justify-between items-start",children:[t.jsxs("div",{children:[t.jsx(p,{className:"h-4 w-32 mb-2"}),t.jsx(p,{className:"h-3 w-20"})]}),t.jsxs("div",{className:"text-right",children:[t.jsx(p,{className:"h-4 w-16 mb-2"}),t.jsx(p,{className:"h-3 w-12"})]})]})})]},`skeleton-${i}-${Date.now()}`)):x.length===0?t.jsx("p",{className:"text-center text-muted-foreground py-8",children:"No recent activity to display"}):x.map((s,i)=>t.jsxs("div",{children:[t.jsxs("div",{className:"flex items-start gap-3 py-2",children:[t.jsx("div",{className:l("rounded-full p-2","bg-muted flex items-center justify-center"),children:m(s.type)}),t.jsx("div",{className:"flex-1",children:t.jsxs("div",{className:"flex justify-between items-start",children:[t.jsxs("div",{children:[t.jsx("p",{className:"font-medium",children:s.title}),t.jsx("p",{className:"text-sm text-muted-foreground",children:s.amount})]}),t.jsxs("div",{className:"text-right",children:[t.jsxs("div",{className:"flex items-center gap-1",children:[t.jsx("div",{className:l("h-2.5 w-2.5 rounded-full",h(s.status))}),t.jsx("span",{className:"text-sm font-medium",children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})]}),t.jsx("p",{className:"text-xs text-muted-foreground",children:f(s.timestamp)})]})]})})]}),i<c.length-1&&t.jsx(O,{})]},s.id))})})]})};export{K as default};
