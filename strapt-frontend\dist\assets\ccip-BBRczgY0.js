import{a$ as g,b0 as x,b1 as F,b2 as C,b3 as I,b4 as M,b5 as E,b6 as T,b7 as m,b8 as D,b9 as A,ba as G,bb as j,bc as b,bd as v,be as R,bf as k,bg as w,bh as P,bi as S,bj as _,bk as H,bl as U,bm as O,bn as B}from"./index-G17GlXLb.js";function z(t){const{abi:s,data:a}=t,r=g(a,0,4),e=s.find(o=>o.type==="function"&&r===x(F(o)));if(!e)throw new C(r,{docsPath:"/docs/contract/decodeFunctionData"});return{functionName:e.name,args:"inputs"in e&&e.inputs&&e.inputs.length>0?I(e.inputs,g(a,4)):void 0}}const p="/docs/contract/encodeErrorResult";function L(t){const{abi:s,errorName:a,args:r}=t;let e=s[0];if(a){const u=M({abi:s,args:r,name:a});if(!u)throw new E(a,{docsPath:p});e=u}if(e.type!=="error")throw new E(void 0,{docsPath:p});const o=F(e),n=x(o);let c="0x";if(r&&r.length>0){if(!e.inputs)throw new T(e.name,{docsPath:p});c=m(e.inputs,r)}return D([n,c])}const h="/docs/contract/encodeFunctionResult";function J(t){const{abi:s,functionName:a,result:r}=t;let e=s[0];if(a){const n=M({abi:s,name:a});if(!n)throw new A(a,{docsPath:h});e=n}if(e.type!=="function")throw new A(void 0,{docsPath:h});if(!e.outputs)throw new G(e.name,{docsPath:h});const o=(()=>{if(e.outputs.length===0)return[];if(e.outputs.length===1)return[r];if(Array.isArray(r))return r;throw new j(r)})();return m(e.outputs,o)}const W="x-batch-gateway:true";async function K(t){const{data:s,ccipRequest:a}=t,{args:[r]}=z({abi:b,data:s}),e=[],o=[];return await Promise.all(r.map(async(n,c)=>{try{o[c]=await a(n),e[c]=!1}catch(u){e[c]=!0,o[c]=Q(u)}})),J({abi:b,functionName:"query",result:[e,o]})}function Q(t){return t.name==="HttpRequestError"&&t.status?L({abi:b,errorName:"HttpError",args:[t.status,t.shortMessage]}):L({abi:[v],errorName:"Error",args:["shortMessage"in t?t.shortMessage:t.message]})}function V(t,s){if(!R(t,{strict:!1}))throw new k({address:t});if(!R(s,{strict:!1}))throw new k({address:s});return t.toLowerCase()===s.toLowerCase()}class X extends w{constructor({callbackSelector:s,cause:a,data:r,extraData:e,sender:o,urls:n}){var c;super(a.shortMessage||"An error occurred while fetching for an offchain result.",{cause:a,metaMessages:[...a.metaMessages||[],(c=a.metaMessages)!=null&&c.length?"":[],"Offchain Gateway Call:",n&&["  Gateway URL(s):",...n.map(u=>`    ${P(u)}`)],`  Sender: ${o}`,`  Data: ${r}`,`  Callback selector: ${s}`,`  Extra data: ${e}`].flat(),name:"OffchainLookupError"})}}class Y extends w{constructor({result:s,url:a}){super("Offchain gateway response is malformed. Response data must be a hex value.",{metaMessages:[`Gateway URL: ${P(a)}`,`Response: ${S(s)}`],name:"OffchainLookupResponseMalformedError"})}}class Z extends w{constructor({sender:s,to:a}){super("Reverted sender address does not match target contract address (`to`).",{metaMessages:[`Contract address: ${a}`,`OffchainLookup sender address: ${s}`],name:"OffchainLookupSenderMismatchError"})}}const ee="0x556f1830",q={name:"OffchainLookup",type:"error",inputs:[{name:"sender",type:"address"},{name:"urls",type:"string[]"},{name:"callData",type:"bytes"},{name:"callbackFunction",type:"bytes4"},{name:"extraData",type:"bytes"}]};async function te(t,{blockNumber:s,blockTag:a,data:r,to:e}){const{args:o}=_({data:r,abi:[q]}),[n,c,u,f,d]=o,{ccipRead:i}=t,y=i&&typeof(i==null?void 0:i.request)=="function"?i.request:N;try{if(!V(e,n))throw new Z({sender:n,to:e});const l=c.includes(W)?await K({data:u,ccipRequest:y}):await y({data:u,sender:n,urls:c}),{data:$}=await H(t,{blockNumber:s,blockTag:a,data:U([f,m([{type:"bytes"},{type:"bytes"}],[l,d])]),to:e});return $}catch(l){throw new X({callbackSelector:f,cause:l,data:r,extraData:d,sender:n,urls:c})}}async function N({data:t,sender:s,urls:a}){var e;let r=new Error("An unknown error occurred.");for(let o=0;o<a.length;o++){const n=a[o],c=n.includes("{data}")?"GET":"POST",u=c==="POST"?{data:t,sender:s}:void 0,f=c==="POST"?{"Content-Type":"application/json"}:{};try{const d=await fetch(n.replace("{sender}",s.toLowerCase()).replace("{data}",t),{body:JSON.stringify(u),headers:f,method:c});let i;if((e=d.headers.get("Content-Type"))!=null&&e.startsWith("application/json")?i=(await d.json()).data:i=await d.text(),!d.ok){r=new O({body:u,details:i!=null&&i.error?S(i.error):d.statusText,headers:d.headers,status:d.status,url:n});continue}if(!B(i)){r=new Y({result:i,url:n});continue}return i}catch(d){r=new O({body:u,details:d.message,url:n})}}throw r}const se=Object.freeze(Object.defineProperty({__proto__:null,ccipRequest:N,offchainLookup:te,offchainLookupAbiItem:q,offchainLookupSignature:ee},Symbol.toStringTag,{value:"Module"}));export{J as a,q as b,N as c,z as d,L as e,ee as f,se as g,V as i,W as l,te as o};
