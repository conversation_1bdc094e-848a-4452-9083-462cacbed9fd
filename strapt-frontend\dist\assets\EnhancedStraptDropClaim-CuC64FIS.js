import{k as ae,l as re,I as ie,u as oe,r as n,j as e,B as o,n as j,s as g,c as S,o as ne,p as le,q as ce,$ as de,t as E,v as me,x as z,w as xe,a0 as F,a1 as pe,D as he,d as ue,e as fe,f as je,g as ge,a2 as ye,J as y,a3 as Ne,i as be}from"./index-G17GlXLb.js";import{u as ve}from"./use-strapt-drop-D-qrso9N.js";import{c as M}from"./confetti.module-BxKCmZ95.js";import{C as U}from"./chevron-left-CRB4yH4j.js";import{Q}from"./qr-code-BnIF1LDH.js";import{G as N}from"./gift-Cgh4t_YK.js";import{m as r}from"./proxy-B8vTGf2f.js";import{T as we}from"./triangle-alert-C7Etu4yh.js";import{C as X,S as Ce}from"./shuffle-CGODj-xY.js";import{A as H}from"./index-94PVxzUY.js";import"./StraptDrop-DBtBqCxY.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ke=ae("PartyPopper",[["path",{d:"M5.8 11.3 2 22l10.7-3.79",key:"gwxi1d"}],["path",{d:"M4 3h.01",key:"1vcuye"}],["path",{d:"M22 8h.01",key:"1mrtc2"}],["path",{d:"M15 2h.01",key:"1cjtqr"}],["path",{d:"M22 20h.01",key:"1mrys2"}],["path",{d:"m22 2-2.24.75a2.9 2.9 0 0 0-1.96 3.12c.1.86-.57 1.63-1.45 1.63h-.38c-.86 0-1.6.6-1.76 1.44L14 10",key:"hbicv8"}],["path",{d:"m22 13-.82-.33c-.86-.34-1.82.2-1.98 1.11c-.11.7-.72 1.22-1.43 1.22H17",key:"1i94pl"}],["path",{d:"m11 2 .33.82c.34.86-.2 1.82-1.11 1.98C9.52 4.9 9 5.52 9 6.23V7",key:"1cofks"}],["path",{d:"M11 13c1.93 1.93 2.83 4.17 2 5-.83.83-3.07-.07-5-2-1.93-1.93-2.83-4.17-2-5 .83-.83 3.07.07 5 2Z",key:"4kbmks"}]]),Me=()=>{const m=re(),B=ie(),{toast:x}=be(),{isConnected:Y,address:p}=oe(),{getDropInfo:T,claimDrop:G,hasAddressClaimed:R,isLoading:b,isClaiming:A}=ve(),l=B.pathname.split("/").pop()||"",[s,$]=n.useState(null),[u,v]=n.useState(!1),[w,q]=n.useState(""),[O,C]=n.useState(!1),[V,k]=n.useState(!1),[W,I]=n.useState(!1),[J,Z]=n.useState(null);n.useEffect(()=>{(async()=>{if(l)try{const a=await T(l);if($(a),p){const i=await R(l,p);v(i)}}catch(a){console.error("Error loading drop info:",a),Z("Failed to load drop information")}})()},[l,p,T,R]);const K=()=>{const a=Date.now()+3e3,i={startVelocity:30,spread:360,ticks:60,zIndex:0};function c(h,f){return Math.random()*(f-h)+h}const te=setInterval(()=>{const h=a-Date.now();if(h<=0)return clearInterval(te);const f=50*(h/3e3);M({...i,particleCount:f,origin:{x:c(.1,.3),y:c(0,.2)}}),M({...i,particleCount:f,origin:{x:c(.7,.9),y:c(0,.2)}})},250)},D=t=>Date.now()/1e3>Number(t),_=t=>p?p.toLowerCase()===t.creator.toLowerCase():!1,L=(t,a)=>{const i=a.toLowerCase()===y.StraptDrop.supportedTokens.USDC.toLowerCase()?6:a.toLowerCase()===y.StraptDrop.supportedTokens.IDRX.toLowerCase()?2:18;return Ne(t,i)},d=t=>t.toLowerCase()===y.StraptDrop.supportedTokens.USDC.toLowerCase()?"USDC":t.toLowerCase()===y.StraptDrop.supportedTokens.IDRX.toLowerCase()?"IDRX":"Tokens",ee=async()=>{if(!(!l||!s||!Y))try{const t=await G(l);q(t),v(!0),k(!0),K(),setTimeout(()=>{k(!1)},5e3),x({title:"Success",description:"Successfully claimed tokens!"})}catch(t){console.error("Error claiming drop:",t);const a=t instanceof Error?t.message:"Unknown error";a.includes("already claimed")?(x({title:"Already Claimed",description:"You have already claimed tokens from this drop",variant:"destructive"}),v(!0)):a.includes("drop is not active")?x({title:"Drop Inactive",description:"This drop is no longer active",variant:"destructive"}):x({title:"Claim Failed",description:"Failed to claim tokens. Please try again.",variant:"destructive"})}},se=t=>{try{const i=new URL(t).pathname.split("/"),c=i[i.length-1];c&&m(`/app/strapt-drop/claim/${c}`)}catch(a){console.error("Invalid QR code data:",a),x({title:"Invalid QR Code",description:"The scanned QR code is not a valid STRAPT Drop",variant:"destructive"})}},P=()=>s?Number(s.claimedCount)/Number(s.totalRecipients)*100:0;return e.jsxs("div",{className:"container max-w-3xl mx-auto py-4 px-4 sm:px-6 sm:py-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(o,{variant:"ghost",size:"icon",onClick:()=>m("/app/strapt-drop"),className:"mr-1","aria-label":"Back to STRAPT Drop",children:e.jsx(U,{className:"h-5 w-5"})}),e.jsx("h1",{className:"text-xl sm:text-2xl font-bold",children:"Claim STRAPT Drop"})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsxs(o,{variant:"outline",size:"sm",onClick:()=>C(!0),children:[e.jsx(Q,{className:"h-4 w-4 mr-2"}),"Scan QR Code"]}),e.jsxs(o,{size:"sm",onClick:()=>m("/app/strapt-drop"),children:[e.jsx(N,{className:"h-4 w-4 mr-2"}),"Create Drop"]})]})]}),l?J?e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:e.jsx(j,{className:"border border-destructive/20 shadow-md overflow-hidden",children:e.jsxs(g,{className:"flex flex-col items-center justify-center py-12 sm:py-16 px-4 sm:px-6 text-center",children:[e.jsx(we,{className:"h-16 w-16 text-destructive mb-6"}),e.jsx("h2",{className:"text-xl font-medium mb-3",children:"Drop Not Found"}),e.jsx("p",{className:"text-base text-muted-foreground mb-8 max-w-md",children:"The STRAPT Drop you're looking for doesn't exist or has been removed"}),e.jsx(o,{onClick:()=>m("/app/strapt-drop"),children:"Create a New Drop"})]})})}):b?e.jsx(j,{className:"border border-primary/20 shadow-md overflow-hidden",children:e.jsx(g,{className:"flex flex-col items-center justify-center py-12 sm:py-16 px-4 sm:px-6 text-center",children:e.jsx(S,{text:"Loading drop information..."})})}):s?e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:"max-w-xl mx-auto",children:e.jsxs(j,{className:"overflow-hidden border border-primary/20 shadow-lg",children:[e.jsxs(ne,{className:"bg-muted/20 border-b border-border pb-4",children:[e.jsxs(le,{className:"flex items-center gap-2 text-xl",children:[e.jsx(N,{className:"h-6 w-6 text-primary"}),"STRAPT Drop"]}),e.jsx(ce,{children:"Claim your tokens from this STRAPT Drop"})]}),e.jsxs(g,{className:"p-6 space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"w-10 h-10 rounded-full flex items-center justify-center overflow-hidden",children:d(s.tokenAddress)==="USDC"?e.jsx("img",{src:"/usd-coin-usdc-logo.svg",alt:"USDC",className:"w-full h-full object-cover"}):d(s.tokenAddress)==="IDRX"?e.jsx("img",{src:"/IDRX BLUE COIN.svg",alt:"IDRX",className:"w-full h-full object-cover"}):e.jsx(X,{className:"h-5 w-5 text-primary"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:d(s.tokenAddress)}),e.jsx("p",{className:"text-xs text-muted-foreground",children:s.isRandom?"Random amount":L(s.amountPerRecipient,s.tokenAddress)})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("h3",{className:"font-medium",children:[Number(s.claimedCount).toLocaleString()," / ",Number(s.totalRecipients).toLocaleString()]}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Recipients claimed"})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(de,{value:P(),className:"h-2"}),e.jsxs("div",{className:"flex justify-between text-xs text-muted-foreground",children:[e.jsx("span",{children:"Progress"}),e.jsxs("span",{children:[Math.round(P()),"%"]})]})]}),e.jsx("div",{className:E("p-3 rounded-lg border",s.isRandom?"bg-amber-500/10 border-amber-500/30":"bg-blue-500/10 border-blue-500/30"),children:s.isRandom?e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(Ce,{className:"h-5 w-5 text-amber-500 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-amber-600 dark:text-amber-400",children:"Random Distribution"}),e.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:"You'll receive a random amount between 1% and 200% of the average."})]})]}):e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(X,{className:"h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-blue-600 dark:text-blue-400",children:"Fixed Distribution"}),e.jsxs("p",{className:"text-xs text-muted-foreground mt-1",children:["Each recipient receives exactly ",L(s.amountPerRecipient,s.tokenAddress)," ",d(s.tokenAddress),"."]})]})]})}),e.jsxs("div",{className:"flex items-start gap-3 p-3 rounded-lg bg-muted/30 border border-muted",children:[e.jsx(me,{className:"h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium",children:D(s.expiryTime)?"Expired":"Expires in"}),e.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:D(s.expiryTime)?"This drop has expired. Unclaimed tokens can be refunded by the creator.":`Expires ${new Date(Number(s.expiryTime)*1e3).toLocaleString()}`})]})]}),e.jsx(H,{children:u&&w&&e.jsxs(r.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"flex items-start gap-3 p-3 rounded-lg bg-green-500/10 border border-green-500/30",children:[e.jsx(z,{className:"h-5 w-5 text-green-500 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("p",{className:"text-sm font-medium text-green-600 dark:text-green-400",children:"Claimed Successfully"}),e.jsxs("p",{className:"text-xs text-muted-foreground mt-1",children:["You received ",w," ",d(s.tokenAddress)]})]})]})})]}),e.jsx(xe,{className:"pt-2 pb-6 px-6 border-t border-border",children:e.jsxs("div",{className:"flex gap-3 w-full",children:[e.jsx(r.div,{className:"flex-1",whileHover:{scale:1.02},whileTap:{scale:.98},children:e.jsx(o,{className:E("w-full text-base py-6",u&&"bg-green-500 hover:bg-green-600"),size:"lg",disabled:b||A||u||!s.isActive||D(s.expiryTime)||s.claimedCount>=s.totalRecipients||_(s),onClick:ee,children:A?e.jsxs(e.Fragment,{children:[e.jsx(S,{size:"sm",className:"mr-2"}),"Claiming..."]}):b?e.jsxs(e.Fragment,{children:[e.jsx(S,{size:"sm",className:"mr-2"}),"Processing..."]}):u?e.jsxs(e.Fragment,{children:[e.jsx(z,{className:"h-5 w-5 mr-2"}),"Already Claimed"]}):e.jsxs(e.Fragment,{children:[e.jsx(N,{className:"h-5 w-5 mr-2"}),"Claim Tokens"]})})}),e.jsx(r.div,{whileHover:{scale:1.02},whileTap:{scale:.98},children:e.jsx(o,{variant:"outline",size:"lg",className:"py-6 px-4",onClick:()=>I(!0),children:e.jsx(F,{className:"h-5 w-5"})})})]})})]})}):null:e.jsx(r.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:e.jsx(j,{className:"border border-primary/20 shadow-md overflow-hidden",children:e.jsxs(g,{className:"flex flex-col items-center justify-center py-12 sm:py-16 px-4 sm:px-6 text-center",children:[e.jsx(N,{className:"h-16 w-16 text-primary/70 mb-6"}),e.jsx("h2",{className:"text-xl font-medium mb-3",children:"No Drop ID Provided"}),e.jsx("p",{className:"text-base text-muted-foreground mb-8 max-w-md",children:"Please scan a QR code or use a link to claim tokens from a STRAPT Drop"}),e.jsxs("div",{className:"flex gap-4",children:[e.jsxs(o,{variant:"outline",size:"lg",onClick:()=>m("/app/strapt-drop"),children:[e.jsx(U,{className:"h-4 w-4 mr-2"}),"Back"]}),e.jsxs(o,{size:"lg",onClick:()=>C(!0),children:[e.jsx(Q,{className:"h-4 w-4 mr-2"}),"Scan QR Code"]})]})]})})}),e.jsx(pe,{open:O,onClose:()=>C(!1),onScan:se}),e.jsx(H,{children:V&&e.jsx(r.div,{className:"fixed inset-0 flex items-center justify-center z-50 bg-black/50 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:e.jsxs(r.div,{className:"bg-card rounded-xl p-8 flex flex-col items-center max-w-md mx-4",initial:{scale:.8,y:20,opacity:0},animate:{scale:1,y:0,opacity:1},exit:{scale:.8,y:20,opacity:0},transition:{type:"spring",damping:15},children:[e.jsx(r.div,{initial:{scale:0},animate:{scale:[0,1.2,1]},transition:{duration:.5,times:[0,.7,1]},children:e.jsx("div",{className:"w-20 h-20 rounded-full bg-gradient-to-br from-primary to-green-500 flex items-center justify-center mb-4 animate-pulse shadow-lg",children:e.jsx(ke,{className:"h-10 w-10 text-white"})})}),e.jsx(r.h2,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},className:"text-xl font-bold mb-2",children:"Tokens Claimed!"}),e.jsxs(r.p,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},className:"text-center text-muted-foreground mb-6",children:["You received ",w," ",s?d(s.tokenAddress):"tokens"]}),e.jsx(r.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.6},children:e.jsx(o,{onClick:()=>k(!1),className:"px-8 bg-gradient-to-r from-primary to-accent hover:opacity-90 transition-all",children:"Continue"})})]})})}),e.jsx(he,{open:W,onOpenChange:I,children:e.jsxs(ue,{className:"sm:max-w-md",children:[e.jsxs(fe,{children:[e.jsxs(je,{className:"flex items-center gap-2",children:[e.jsx(F,{className:"h-5 w-5"}),"Top Up Tokens"]}),e.jsx(ge,{children:"Get free test tokens to use with STRAPT"})]}),e.jsx(ye,{})]})})]})};export{Me as default};
