import{bo as se,bp as ce,bq as fe,br as Ut,bs as ae,bt as ue,bu as le,bv as de}from"./index-G17GlXLb.js";/*! noble-curves - MIT License (c) 2022 <PERSON> (paulmillr.com) */const Lt=BigInt(0),At=BigInt(1);function it(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function Zt(e){if(!it(e))throw new Error("Uint8Array expected")}function at(e,n){if(typeof n!="boolean")throw new Error(e+" boolean expected, got "+n)}function dt(e){const n=e.toString(16);return n.length&1?"0"+n:n}function Pt(e){if(typeof e!="string")throw new Error("hex string expected, got "+typeof e);return e===""?Lt:BigInt("0x"+e)}const $t=typeof Uint8Array.from([]).toHex=="function"&&typeof Uint8Array.fromHex=="function",he=Array.from({length:256},(e,n)=>n.toString(16).padStart(2,"0"));function ut(e){if(Zt(e),$t)return e.toHex();let n="";for(let t=0;t<e.length;t++)n+=he[e[t]];return n}const $={_0:48,_9:57,A:65,F:70,a:97,f:102};function kt(e){if(e>=$._0&&e<=$._9)return e-$._0;if(e>=$.A&&e<=$.F)return e-($.A-10);if(e>=$.a&&e<=$.f)return e-($.a-10)}function ht(e){if(typeof e!="string")throw new Error("hex string expected, got "+typeof e);if($t)return Uint8Array.fromHex(e);const n=e.length,t=n/2;if(n%2)throw new Error("hex string expected, got unpadded hex of length "+n);const r=new Uint8Array(t);for(let s=0,i=0;s<t;s++,i+=2){const c=kt(e.charCodeAt(i)),f=kt(e.charCodeAt(i+1));if(c===void 0||f===void 0){const o=e[i]+e[i+1];throw new Error('hex string expected, got non-hex character "'+o+'" at index '+i)}r[s]=c*16+f}return r}function tt(e){return Pt(ut(e))}function Wt(e){return Zt(e),Pt(ut(Uint8Array.from(e).reverse()))}function lt(e,n){return ht(e.toString(16).padStart(n*2,"0"))}function Gt(e,n){return lt(e,n).reverse()}function K(e,n,t){let r;if(typeof n=="string")try{r=ht(n)}catch(i){throw new Error(e+" must be hex string or Uint8Array, cause: "+i)}else if(it(n))r=Uint8Array.from(n);else throw new Error(e+" must be hex string or Uint8Array");const s=r.length;if(typeof t=="number"&&s!==t)throw new Error(e+" of length "+t+" expected, got "+s);return r}function wt(...e){let n=0;for(let r=0;r<e.length;r++){const s=e[r];Zt(s),n+=s.length}const t=new Uint8Array(n);for(let r=0,s=0;r<e.length;r++){const i=e[r];t.set(i,s),s+=i.length}return t}const bt=e=>typeof e=="bigint"&&Lt<=e;function zt(e,n,t){return bt(e)&&bt(n)&&bt(t)&&n<=e&&e<t}function ot(e,n,t,r){if(!zt(n,t,r))throw new Error("expected valid "+e+": "+t+" <= n < "+r+", got "+n)}function we(e){let n;for(n=0;e>Lt;e>>=At,n+=1);return n}const gt=e=>(At<<BigInt(e))-At,Et=e=>new Uint8Array(e),Tt=e=>Uint8Array.from(e);function ge(e,n,t){if(typeof e!="number"||e<2)throw new Error("hashLen must be a number");if(typeof n!="number"||n<2)throw new Error("qByteLen must be a number");if(typeof t!="function")throw new Error("hmacFn must be a function");let r=Et(e),s=Et(e),i=0;const c=()=>{r.fill(1),s.fill(0),i=0},f=(...I)=>t(s,r,...I),o=(I=Et(0))=>{s=f(Tt([0]),I),r=f(),I.length!==0&&(s=f(Tt([1]),I),r=f())},u=()=>{if(i++>=1e3)throw new Error("drbg: tried 1000 values");let I=0;const d=[];for(;I<n;){r=f();const S=r.slice();d.push(S),I+=r.length}return wt(...d)};return(I,d)=>{c(),o(I);let S;for(;!(S=d(u()));)o();return c(),S}}const pe={bigint:e=>typeof e=="bigint",function:e=>typeof e=="function",boolean:e=>typeof e=="boolean",string:e=>typeof e=="string",stringOrUint8Array:e=>typeof e=="string"||it(e),isSafeInteger:e=>Number.isSafeInteger(e),array:e=>Array.isArray(e),field:(e,n)=>n.Fp.isValid(e),hash:e=>typeof e=="function"&&Number.isSafeInteger(e.outputLen)};function pt(e,n,t={}){const r=(s,i,c)=>{const f=pe[i];if(typeof f!="function")throw new Error("invalid validator function");const o=e[s];if(!(c&&o===void 0)&&!f(o,e))throw new Error("param "+String(s)+" is invalid. Expected "+i+", got "+o)};for(const[s,i]of Object.entries(n))r(s,i,!1);for(const[s,i]of Object.entries(t))r(s,i,!0);return e}function Rt(e){const n=new WeakMap;return(t,...r)=>{const s=n.get(t);if(s!==void 0)return s;const i=e(t,...r);return n.set(t,i),i}}class Xt extends se{constructor(n,t){super(),this.finished=!1,this.destroyed=!1,ce(n);const r=fe(t);if(this.iHash=n.create(),typeof this.iHash.update!="function")throw new Error("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;const s=this.blockLen,i=new Uint8Array(s);i.set(r.length>s?n.create().update(r).digest():r);for(let c=0;c<i.length;c++)i[c]^=54;this.iHash.update(i),this.oHash=n.create();for(let c=0;c<i.length;c++)i[c]^=106;this.oHash.update(i),i.fill(0)}update(n){return Ut(this),this.iHash.update(n),this}digestInto(n){Ut(this),ae(n,this.outputLen),this.finished=!0,this.iHash.digestInto(n),this.oHash.update(n),this.oHash.digestInto(n),this.destroy()}digest(){const n=new Uint8Array(this.oHash.outputLen);return this.digestInto(n),n}_cloneInto(n){n||(n=Object.create(Object.getPrototypeOf(this),{}));const{oHash:t,iHash:r,finished:s,destroyed:i,blockLen:c,outputLen:f}=this;return n=n,n.finished=s,n.destroyed=i,n.blockLen=c,n.outputLen=f,n.oHash=t._cloneInto(n.oHash),n.iHash=r._cloneInto(n.iHash),n}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}}const Dt=(e,n,t)=>new Xt(e,n).update(t).digest();Dt.create=(e,n)=>new Xt(e,n);/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const T=BigInt(0),z=BigInt(1),J=BigInt(2),ye=BigInt(3),It=BigInt(4),Ct=BigInt(5),_t=BigInt(8);function V(e,n){const t=e%n;return t>=T?t:n+t}function me(e,n,t){if(n<T)throw new Error("invalid exponent, negatives unsupported");if(t<=T)throw new Error("invalid modulus");if(t===z)return T;let r=z;for(;n>T;)n&z&&(r=r*e%t),e=e*e%t,n>>=z;return r}function Y(e,n,t){let r=e;for(;n-- >T;)r*=r,r%=t;return r}function Nt(e,n){if(e===T)throw new Error("invert: expected non-zero number");if(n<=T)throw new Error("invert: expected positive modulus, got "+n);let t=V(e,n),r=n,s=T,i=z;for(;t!==T;){const f=r/t,o=r%t,u=s-i*f;r=t,t=o,s=i,i=u}if(r!==z)throw new Error("invert: does not exist");return V(s,n)}function be(e){const n=(e-z)/J;let t,r,s;for(t=e-z,r=0;t%J===T;t/=J,r++);for(s=J;s<e&&me(s,n,e)!==e-z;s++)if(s>1e3)throw new Error("Cannot find square root: likely non-prime P");if(r===1){const c=(e+z)/It;return function(o,u){const m=o.pow(u,c);if(!o.eql(o.sqr(m),u))throw new Error("Cannot find square root");return m}}const i=(t+z)/J;return function(f,o){if(f.pow(o,n)===f.neg(f.ONE))throw new Error("Cannot find square root");let u=r,m=f.pow(f.mul(f.ONE,s),t),I=f.pow(o,i),d=f.pow(o,t);for(;!f.eql(d,f.ONE);){if(f.eql(d,f.ZERO))return f.ZERO;let S=1;for(let p=f.sqr(d);S<u&&!f.eql(p,f.ONE);S++)p=f.sqr(p);const L=f.pow(m,z<<BigInt(u-S-1));m=f.sqr(L),I=f.mul(I,L),d=f.mul(d,m),u=S}return I}}function Ee(e){if(e%It===ye){const n=(e+z)/It;return function(r,s){const i=r.pow(s,n);if(!r.eql(r.sqr(i),s))throw new Error("Cannot find square root");return i}}if(e%_t===Ct){const n=(e-Ct)/_t;return function(r,s){const i=r.mul(s,J),c=r.pow(i,n),f=r.mul(s,c),o=r.mul(r.mul(f,J),c),u=r.mul(f,r.sub(o,r.ONE));if(!r.eql(r.sqr(u),s))throw new Error("Cannot find square root");return u}}return be(e)}const Be=["create","isValid","is0","neg","inv","sqrt","sqr","eql","add","sub","mul","pow","div","addN","subN","mulN","sqrN"];function ve(e){const n={ORDER:"bigint",MASK:"bigint",BYTES:"isSafeInteger",BITS:"isSafeInteger"},t=Be.reduce((r,s)=>(r[s]="function",r),n);return pt(e,t)}function xe(e,n,t){if(t<T)throw new Error("invalid exponent, negatives unsupported");if(t===T)return e.ONE;if(t===z)return n;let r=e.ONE,s=n;for(;t>T;)t&z&&(r=e.mul(r,s)),s=e.sqr(s),t>>=z;return r}function Se(e,n){const t=new Array(n.length),r=n.reduce((i,c,f)=>e.is0(c)?i:(t[f]=i,e.mul(i,c)),e.ONE),s=e.inv(r);return n.reduceRight((i,c,f)=>e.is0(c)?i:(t[f]=e.mul(i,t[f]),e.mul(i,c)),s),t}function Qt(e,n){const t=n!==void 0?n:e.toString(2).length,r=Math.ceil(t/8);return{nBitLength:t,nByteLength:r}}function Jt(e,n,t=!1,r={}){if(e<=T)throw new Error("invalid field: expected ORDER > 0, got "+e);const{nBitLength:s,nByteLength:i}=Qt(e,n);if(i>2048)throw new Error("invalid field: expected ORDER of <= 2048 bytes");let c;const f=Object.freeze({ORDER:e,isLE:t,BITS:s,BYTES:i,MASK:gt(s),ZERO:T,ONE:z,create:o=>V(o,e),isValid:o=>{if(typeof o!="bigint")throw new Error("invalid field element: expected bigint, got "+typeof o);return T<=o&&o<e},is0:o=>o===T,isOdd:o=>(o&z)===z,neg:o=>V(-o,e),eql:(o,u)=>o===u,sqr:o=>V(o*o,e),add:(o,u)=>V(o+u,e),sub:(o,u)=>V(o-u,e),mul:(o,u)=>V(o*u,e),pow:(o,u)=>xe(f,o,u),div:(o,u)=>V(o*Nt(u,e),e),sqrN:o=>o*o,addN:(o,u)=>o+u,subN:(o,u)=>o-u,mulN:(o,u)=>o*u,inv:o=>Nt(o,e),sqrt:r.sqrt||(o=>(c||(c=Ee(e)),c(f,o))),invertBatch:o=>Se(f,o),cmov:(o,u,m)=>m?u:o,toBytes:o=>t?Gt(o,i):lt(o,i),fromBytes:o=>{if(o.length!==i)throw new Error("Field.fromBytes: expected "+i+" bytes, got "+o.length);return t?Wt(o):tt(o)}});return Object.freeze(f)}function te(e){if(typeof e!="bigint")throw new Error("field order must be bigint");const n=e.toString(2).length;return Math.ceil(n/8)}function ee(e){const n=te(e);return n+Math.ceil(n/2)}function Ae(e,n,t=!1){const r=e.length,s=te(n),i=ee(n);if(r<16||r<i||r>1024)throw new Error("expected "+i+"-1024 bytes of input, got "+r);const c=t?Wt(e):tt(e),f=V(c,n-z)+z;return t?Gt(f,s):lt(f,s)}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const Mt=BigInt(0),qt=BigInt(1);function Bt(e,n){const t=n.negate();return e?t:n}function ne(e,n){if(!Number.isSafeInteger(e)||e<=0||e>n)throw new Error("invalid window size, expected [1.."+n+"], got W="+e)}function vt(e,n){ne(e,n);const t=Math.ceil(n/e)+1,r=2**(e-1),s=2**e,i=gt(e),c=BigInt(e);return{windows:t,windowSize:r,mask:i,maxNumber:s,shiftBy:c}}function Vt(e,n,t){const{windowSize:r,mask:s,maxNumber:i,shiftBy:c}=t;let f=Number(e&s),o=e>>c;f>r&&(f-=i,o+=qt);const u=n*r,m=u+Math.abs(f)-1,I=f===0,d=f<0,S=n%2!==0;return{nextN:o,offset:m,isZero:I,isNeg:d,isNegF:S,offsetF:u}}function Ie(e,n){if(!Array.isArray(e))throw new Error("array expected");e.forEach((t,r)=>{if(!(t instanceof n))throw new Error("invalid point at index "+r)})}function Ne(e,n){if(!Array.isArray(e))throw new Error("array of scalars expected");e.forEach((t,r)=>{if(!n.isValid(t))throw new Error("invalid scalar at index "+r)})}const xt=new WeakMap,re=new WeakMap;function St(e){return re.get(e)||1}function qe(e,n){return{constTimeNegate:Bt,hasPrecomputes(t){return St(t)!==1},unsafeLadder(t,r,s=e.ZERO){let i=t;for(;r>Mt;)r&qt&&(s=s.add(i)),i=i.double(),r>>=qt;return s},precomputeWindow(t,r){const{windows:s,windowSize:i}=vt(r,n),c=[];let f=t,o=f;for(let u=0;u<s;u++){o=f,c.push(o);for(let m=1;m<i;m++)o=o.add(f),c.push(o);f=o.double()}return c},wNAF(t,r,s){let i=e.ZERO,c=e.BASE;const f=vt(t,n);for(let o=0;o<f.windows;o++){const{nextN:u,offset:m,isZero:I,isNeg:d,isNegF:S,offsetF:L}=Vt(s,o,f);s=u,I?c=c.add(Bt(S,r[L])):i=i.add(Bt(d,r[m]))}return{p:i,f:c}},wNAFUnsafe(t,r,s,i=e.ZERO){const c=vt(t,n);for(let f=0;f<c.windows&&s!==Mt;f++){const{nextN:o,offset:u,isZero:m,isNeg:I}=Vt(s,f,c);if(s=o,!m){const d=r[u];i=i.add(I?d.negate():d)}}return i},getPrecomputes(t,r,s){let i=xt.get(r);return i||(i=this.precomputeWindow(r,t),t!==1&&xt.set(r,s(i))),i},wNAFCached(t,r,s){const i=St(t);return this.wNAF(i,this.getPrecomputes(i,t,s),r)},wNAFCachedUnsafe(t,r,s,i){const c=St(t);return c===1?this.unsafeLadder(t,r,i):this.wNAFUnsafe(c,this.getPrecomputes(c,t,s),r,i)},setWindowSize(t,r){ne(r,n),re.set(t,r),xt.delete(t)}}}function Oe(e,n,t,r){if(Ie(t,e),Ne(r,n),t.length!==r.length)throw new Error("arrays of points and scalars must have equal length");const s=e.ZERO,i=we(BigInt(t.length)),c=i>12?i-3:i>4?i-2:i?2:1,f=gt(c),o=new Array(Number(f)+1).fill(s),u=Math.floor((n.BITS-1)/c)*c;let m=s;for(let I=u;I>=0;I-=c){o.fill(s);for(let S=0;S<r.length;S++){const L=r[S],p=Number(L>>BigInt(I)&f);o[p]=o[p].add(t[S])}let d=s;for(let S=o.length-1,L=s;S>0;S--)L=L.add(o[S]),d=d.add(L);if(m=m.add(d),I!==0)for(let S=0;S<c;S++)m=m.double()}return m}function oe(e){return ve(e.Fp),pt(e,{n:"bigint",h:"bigint",Gx:"field",Gy:"field"},{nBitLength:"isSafeInteger",nByteLength:"isSafeInteger"}),Object.freeze({...Qt(e.n,e.nBitLength),...e,p:e.Fp.ORDER})}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */function jt(e){e.lowS!==void 0&&at("lowS",e.lowS),e.prehash!==void 0&&at("prehash",e.prehash)}function He(e){const n=oe(e);pt(n,{a:"field",b:"field"},{allowedPrivateKeyLengths:"array",wrapPrivateKey:"boolean",isTorsionFree:"function",clearCofactor:"function",allowInfinityPoint:"boolean",fromBytes:"function",toBytes:"function"});const{endo:t,Fp:r,a:s}=n;if(t){if(!r.eql(s,r.ZERO))throw new Error("invalid endomorphism, can only be defined for Koblitz curves that have a=0");if(typeof t!="object"||typeof t.beta!="bigint"||typeof t.splitScalar!="function")throw new Error("invalid endomorphism, expected beta: bigint and splitScalar: function")}return Object.freeze({...n})}class Le extends Error{constructor(n=""){super(n)}}const W={Err:Le,_tlv:{encode:(e,n)=>{const{Err:t}=W;if(e<0||e>256)throw new t("tlv.encode: wrong tag");if(n.length&1)throw new t("tlv.encode: unpadded data");const r=n.length/2,s=dt(r);if(s.length/2&128)throw new t("tlv.encode: long form length too big");const i=r>127?dt(s.length/2|128):"";return dt(e)+i+s+n},decode(e,n){const{Err:t}=W;let r=0;if(e<0||e>256)throw new t("tlv.encode: wrong tag");if(n.length<2||n[r++]!==e)throw new t("tlv.decode: wrong tlv");const s=n[r++],i=!!(s&128);let c=0;if(!i)c=s;else{const o=s&127;if(!o)throw new t("tlv.decode(long): indefinite length not supported");if(o>4)throw new t("tlv.decode(long): byte length is too big");const u=n.subarray(r,r+o);if(u.length!==o)throw new t("tlv.decode: length bytes not complete");if(u[0]===0)throw new t("tlv.decode(long): zero leftmost byte");for(const m of u)c=c<<8|m;if(r+=o,c<128)throw new t("tlv.decode(long): not minimal encoding")}const f=n.subarray(r,r+c);if(f.length!==c)throw new t("tlv.decode: wrong value length");return{v:f,l:n.subarray(r+c)}}},_int:{encode(e){const{Err:n}=W;if(e<G)throw new n("integer: negative integers are not allowed");let t=dt(e);if(Number.parseInt(t[0],16)&8&&(t="00"+t),t.length&1)throw new n("unexpected DER parsing assertion: unpadded hex");return t},decode(e){const{Err:n}=W;if(e[0]&128)throw new n("invalid signature integer: negative");if(e[0]===0&&!(e[1]&128))throw new n("invalid signature integer: unnecessary leading zero");return tt(e)}},toSig(e){const{Err:n,_int:t,_tlv:r}=W,s=K("signature",e),{v:i,l:c}=r.decode(48,s);if(c.length)throw new n("invalid signature: left bytes after parsing");const{v:f,l:o}=r.decode(2,i),{v:u,l:m}=r.decode(2,o);if(m.length)throw new n("invalid signature: left bytes after parsing");return{r:t.decode(f),s:t.decode(u)}},hexFromSig(e){const{_tlv:n,_int:t}=W,r=n.encode(2,t.encode(e.r)),s=n.encode(2,t.encode(e.s)),i=r+s;return n.encode(48,i)}},G=BigInt(0),k=BigInt(1);BigInt(2);const Yt=BigInt(3);BigInt(4);function Ze(e){const n=He(e),{Fp:t}=n,r=Jt(n.n,n.nBitLength),s=n.toBytes||((p,a,h)=>{const y=a.toAffine();return wt(Uint8Array.from([4]),t.toBytes(y.x),t.toBytes(y.y))}),i=n.fromBytes||(p=>{const a=p.subarray(1),h=t.fromBytes(a.subarray(0,t.BYTES)),y=t.fromBytes(a.subarray(t.BYTES,2*t.BYTES));return{x:h,y}});function c(p){const{a,b:h}=n,y=t.sqr(p),b=t.mul(y,p);return t.add(t.add(b,t.mul(p,a)),h)}if(!t.eql(t.sqr(n.Gy),c(n.Gx)))throw new Error("bad generator point: equation left != right");function f(p){return zt(p,k,n.n)}function o(p){const{allowedPrivateKeyLengths:a,nByteLength:h,wrapPrivateKey:y,n:b}=n;if(a&&typeof p!="bigint"){if(it(p)&&(p=ut(p)),typeof p!="string"||!a.includes(p.length))throw new Error("invalid private key");p=p.padStart(h*2,"0")}let N;try{N=typeof p=="bigint"?p:tt(K("private key",p,h))}catch{throw new Error("invalid private key, expected hex or "+h+" bytes, got "+typeof p)}return y&&(N=V(N,b)),ot("private key",N,k,b),N}function u(p){if(!(p instanceof d))throw new Error("ProjectivePoint expected")}const m=Rt((p,a)=>{const{px:h,py:y,pz:b}=p;if(t.eql(b,t.ONE))return{x:h,y};const N=p.is0();a==null&&(a=N?t.ONE:t.inv(b));const O=t.mul(h,a),A=t.mul(y,a),E=t.mul(b,a);if(N)return{x:t.ZERO,y:t.ZERO};if(!t.eql(E,t.ONE))throw new Error("invZ was invalid");return{x:O,y:A}}),I=Rt(p=>{if(p.is0()){if(n.allowInfinityPoint&&!t.is0(p.py))return;throw new Error("bad point: ZERO")}const{x:a,y:h}=p.toAffine();if(!t.isValid(a)||!t.isValid(h))throw new Error("bad point: x or y not FE");const y=t.sqr(h),b=c(a);if(!t.eql(y,b))throw new Error("bad point: equation left != right");if(!p.isTorsionFree())throw new Error("bad point: not in prime-order subgroup");return!0});class d{constructor(a,h,y){if(a==null||!t.isValid(a))throw new Error("x required");if(h==null||!t.isValid(h))throw new Error("y required");if(y==null||!t.isValid(y))throw new Error("z required");this.px=a,this.py=h,this.pz=y,Object.freeze(this)}static fromAffine(a){const{x:h,y}=a||{};if(!a||!t.isValid(h)||!t.isValid(y))throw new Error("invalid affine point");if(a instanceof d)throw new Error("projective point not allowed");const b=N=>t.eql(N,t.ZERO);return b(h)&&b(y)?d.ZERO:new d(h,y,t.ONE)}get x(){return this.toAffine().x}get y(){return this.toAffine().y}static normalizeZ(a){const h=t.invertBatch(a.map(y=>y.pz));return a.map((y,b)=>y.toAffine(h[b])).map(d.fromAffine)}static fromHex(a){const h=d.fromAffine(i(K("pointHex",a)));return h.assertValidity(),h}static fromPrivateKey(a){return d.BASE.multiply(o(a))}static msm(a,h){return Oe(d,r,a,h)}_setWindowSize(a){L.setWindowSize(this,a)}assertValidity(){I(this)}hasEvenY(){const{y:a}=this.toAffine();if(t.isOdd)return!t.isOdd(a);throw new Error("Field doesn't support isOdd")}equals(a){u(a);const{px:h,py:y,pz:b}=this,{px:N,py:O,pz:A}=a,E=t.eql(t.mul(h,A),t.mul(N,b)),x=t.eql(t.mul(y,A),t.mul(O,b));return E&&x}negate(){return new d(this.px,t.neg(this.py),this.pz)}double(){const{a,b:h}=n,y=t.mul(h,Yt),{px:b,py:N,pz:O}=this;let A=t.ZERO,E=t.ZERO,x=t.ZERO,B=t.mul(b,b),R=t.mul(N,N),Z=t.mul(O,O),H=t.mul(b,N);return H=t.add(H,H),x=t.mul(b,O),x=t.add(x,x),A=t.mul(a,x),E=t.mul(y,Z),E=t.add(A,E),A=t.sub(R,E),E=t.add(R,E),E=t.mul(A,E),A=t.mul(H,A),x=t.mul(y,x),Z=t.mul(a,Z),H=t.sub(B,Z),H=t.mul(a,H),H=t.add(H,x),x=t.add(B,B),B=t.add(x,B),B=t.add(B,Z),B=t.mul(B,H),E=t.add(E,B),Z=t.mul(N,O),Z=t.add(Z,Z),B=t.mul(Z,H),A=t.sub(A,B),x=t.mul(Z,R),x=t.add(x,x),x=t.add(x,x),new d(A,E,x)}add(a){u(a);const{px:h,py:y,pz:b}=this,{px:N,py:O,pz:A}=a;let E=t.ZERO,x=t.ZERO,B=t.ZERO;const R=n.a,Z=t.mul(n.b,Yt);let H=t.mul(h,N),j=t.mul(y,O),l=t.mul(b,A),w=t.add(h,y),g=t.add(N,O);w=t.mul(w,g),g=t.add(H,j),w=t.sub(w,g),g=t.add(h,b);let v=t.add(N,A);return g=t.mul(g,v),v=t.add(H,l),g=t.sub(g,v),v=t.add(y,b),E=t.add(O,A),v=t.mul(v,E),E=t.add(j,l),v=t.sub(v,E),B=t.mul(R,g),E=t.mul(Z,l),B=t.add(E,B),E=t.sub(j,B),B=t.add(j,B),x=t.mul(E,B),j=t.add(H,H),j=t.add(j,H),l=t.mul(R,l),g=t.mul(Z,g),j=t.add(j,l),l=t.sub(H,l),l=t.mul(R,l),g=t.add(g,l),H=t.mul(j,g),x=t.add(x,H),H=t.mul(v,g),E=t.mul(w,E),E=t.sub(E,H),H=t.mul(w,j),B=t.mul(v,B),B=t.add(B,H),new d(E,x,B)}subtract(a){return this.add(a.negate())}is0(){return this.equals(d.ZERO)}wNAF(a){return L.wNAFCached(this,a,d.normalizeZ)}multiplyUnsafe(a){const{endo:h,n:y}=n;ot("scalar",a,G,y);const b=d.ZERO;if(a===G)return b;if(this.is0()||a===k)return this;if(!h||L.hasPrecomputes(this))return L.wNAFCachedUnsafe(this,a,d.normalizeZ);let{k1neg:N,k1:O,k2neg:A,k2:E}=h.splitScalar(a),x=b,B=b,R=this;for(;O>G||E>G;)O&k&&(x=x.add(R)),E&k&&(B=B.add(R)),R=R.double(),O>>=k,E>>=k;return N&&(x=x.negate()),A&&(B=B.negate()),B=new d(t.mul(B.px,h.beta),B.py,B.pz),x.add(B)}multiply(a){const{endo:h,n:y}=n;ot("scalar",a,k,y);let b,N;if(h){const{k1neg:O,k1:A,k2neg:E,k2:x}=h.splitScalar(a);let{p:B,f:R}=this.wNAF(A),{p:Z,f:H}=this.wNAF(x);B=L.constTimeNegate(O,B),Z=L.constTimeNegate(E,Z),Z=new d(t.mul(Z.px,h.beta),Z.py,Z.pz),b=B.add(Z),N=R.add(H)}else{const{p:O,f:A}=this.wNAF(a);b=O,N=A}return d.normalizeZ([b,N])[0]}multiplyAndAddUnsafe(a,h,y){const b=d.BASE,N=(A,E)=>E===G||E===k||!A.equals(b)?A.multiplyUnsafe(E):A.multiply(E),O=N(this,h).add(N(a,y));return O.is0()?void 0:O}toAffine(a){return m(this,a)}isTorsionFree(){const{h:a,isTorsionFree:h}=n;if(a===k)return!0;if(h)return h(d,this);throw new Error("isTorsionFree() has not been declared for the elliptic curve")}clearCofactor(){const{h:a,clearCofactor:h}=n;return a===k?this:h?h(d,this):this.multiplyUnsafe(n.h)}toRawBytes(a=!0){return at("isCompressed",a),this.assertValidity(),s(d,this,a)}toHex(a=!0){return at("isCompressed",a),ut(this.toRawBytes(a))}}d.BASE=new d(n.Gx,n.Gy,t.ONE),d.ZERO=new d(t.ZERO,t.ONE,t.ZERO);const S=n.nBitLength,L=qe(d,n.endo?Math.ceil(S/2):S);return{CURVE:n,ProjectivePoint:d,normPrivateKeyToScalar:o,weierstrassEquation:c,isWithinCurveOrder:f}}function ze(e){const n=oe(e);return pt(n,{hash:"hash",hmac:"function",randomBytes:"function"},{bits2int:"function",bits2int_modN:"function",lowS:"boolean"}),Object.freeze({lowS:!0,...n})}function Ue(e){const n=ze(e),{Fp:t,n:r}=n,s=t.BYTES+1,i=2*t.BYTES+1;function c(l){return V(l,r)}function f(l){return Nt(l,r)}const{ProjectivePoint:o,normPrivateKeyToScalar:u,weierstrassEquation:m,isWithinCurveOrder:I}=Ze({...n,toBytes(l,w,g){const v=w.toAffine(),q=t.toBytes(v.x),U=wt;return at("isCompressed",g),g?U(Uint8Array.from([w.hasEvenY()?2:3]),q):U(Uint8Array.from([4]),q,t.toBytes(v.y))},fromBytes(l){const w=l.length,g=l[0],v=l.subarray(1);if(w===s&&(g===2||g===3)){const q=tt(v);if(!zt(q,k,t.ORDER))throw new Error("Point is not on curve");const U=m(q);let _;try{_=t.sqrt(U)}catch(F){const M=F instanceof Error?": "+F.message:"";throw new Error("Point is not on curve"+M)}const C=(_&k)===k;return(g&1)===1!==C&&(_=t.neg(_)),{x:q,y:_}}else if(w===i&&g===4){const q=t.fromBytes(v.subarray(0,t.BYTES)),U=t.fromBytes(v.subarray(t.BYTES,2*t.BYTES));return{x:q,y:U}}else{const q=s,U=i;throw new Error("invalid Point, expected length of "+q+", or uncompressed "+U+", got "+w)}}}),d=l=>ut(lt(l,n.nByteLength));function S(l){const w=r>>k;return l>w}function L(l){return S(l)?c(-l):l}const p=(l,w,g)=>tt(l.slice(w,g));class a{constructor(w,g,v){ot("r",w,k,r),ot("s",g,k,r),this.r=w,this.s=g,v!=null&&(this.recovery=v),Object.freeze(this)}static fromCompact(w){const g=n.nByteLength;return w=K("compactSignature",w,g*2),new a(p(w,0,g),p(w,g,2*g))}static fromDER(w){const{r:g,s:v}=W.toSig(K("DER",w));return new a(g,v)}assertValidity(){}addRecoveryBit(w){return new a(this.r,this.s,w)}recoverPublicKey(w){const{r:g,s:v,recovery:q}=this,U=A(K("msgHash",w));if(q==null||![0,1,2,3].includes(q))throw new Error("recovery id invalid");const _=q===2||q===3?g+n.n:g;if(_>=t.ORDER)throw new Error("recovery id 2 or 3 invalid");const C=q&1?"03":"02",P=o.fromHex(C+d(_)),F=f(_),M=c(-U*F),et=c(v*F),X=o.BASE.multiplyAndAddUnsafe(P,M,et);if(!X)throw new Error("point at infinify");return X.assertValidity(),X}hasHighS(){return S(this.s)}normalizeS(){return this.hasHighS()?new a(this.r,c(-this.s),this.recovery):this}toDERRawBytes(){return ht(this.toDERHex())}toDERHex(){return W.hexFromSig({r:this.r,s:this.s})}toCompactRawBytes(){return ht(this.toCompactHex())}toCompactHex(){return d(this.r)+d(this.s)}}const h={isValidPrivateKey(l){try{return u(l),!0}catch{return!1}},normPrivateKeyToScalar:u,randomPrivateKey:()=>{const l=ee(n.n);return Ae(n.randomBytes(l),n.n)},precompute(l=8,w=o.BASE){return w._setWindowSize(l),w.multiply(BigInt(3)),w}};function y(l,w=!0){return o.fromPrivateKey(l).toRawBytes(w)}function b(l){const w=it(l),g=typeof l=="string",v=(w||g)&&l.length;return w?v===s||v===i:g?v===2*s||v===2*i:l instanceof o}function N(l,w,g=!0){if(b(l))throw new Error("first arg must be private key");if(!b(w))throw new Error("second arg must be public key");return o.fromHex(w).multiply(u(l)).toRawBytes(g)}const O=n.bits2int||function(l){if(l.length>8192)throw new Error("input is too large");const w=tt(l),g=l.length*8-n.nBitLength;return g>0?w>>BigInt(g):w},A=n.bits2int_modN||function(l){return c(O(l))},E=gt(n.nBitLength);function x(l){return ot("num < 2^"+n.nBitLength,l,G,E),lt(l,n.nByteLength)}function B(l,w,g=R){if(["recovered","canonical"].some(D=>D in g))throw new Error("sign() legacy options not supported");const{hash:v,randomBytes:q}=n;let{lowS:U,prehash:_,extraEntropy:C}=g;U==null&&(U=!0),l=K("msgHash",l),jt(g),_&&(l=K("prehashed msgHash",v(l)));const P=A(l),F=u(w),M=[x(F),x(P)];if(C!=null&&C!==!1){const D=C===!0?q(t.BYTES):C;M.push(K("extraEntropy",D))}const et=wt(...M),X=P;function yt(D){const nt=O(D);if(!I(nt))return;const mt=f(nt),st=o.BASE.multiply(nt).toAffine(),Q=c(st.x);if(Q===G)return;const ct=c(mt*c(X+Q*F));if(ct===G)return;let ft=(st.x===Q?0:2)|Number(st.y&k),rt=ct;return U&&S(ct)&&(rt=L(ct),ft^=1),new a(Q,rt,ft)}return{seed:et,k2sig:yt}}const R={lowS:n.lowS,prehash:!1},Z={lowS:n.lowS,prehash:!1};function H(l,w,g=R){const{seed:v,k2sig:q}=B(l,w,g),U=n;return ge(U.hash.outputLen,U.nByteLength,U.hmac)(v,q)}o.BASE._setWindowSize(8);function j(l,w,g,v=Z){var ft;const q=l;w=K("msgHash",w),g=K("publicKey",g);const{lowS:U,prehash:_,format:C}=v;if(jt(v),"strict"in v)throw new Error("options.strict was renamed to lowS");if(C!==void 0&&C!=="compact"&&C!=="der")throw new Error("format must be compact or der");const P=typeof q=="string"||it(q),F=!P&&!C&&typeof q=="object"&&q!==null&&typeof q.r=="bigint"&&typeof q.s=="bigint";if(!P&&!F)throw new Error("invalid signature, expected Uint8Array, hex string or Signature instance");let M,et;try{if(F&&(M=new a(q.r,q.s)),P){try{C!=="compact"&&(M=a.fromDER(q))}catch(rt){if(!(rt instanceof W.Err))throw rt}!M&&C!=="der"&&(M=a.fromCompact(q))}et=o.fromHex(g)}catch{return!1}if(!M||U&&M.hasHighS())return!1;_&&(w=n.hash(w));const{r:X,s:yt}=M,D=A(w),nt=f(yt),mt=c(D*nt),st=c(X*nt),Q=(ft=o.BASE.multiplyAndAddUnsafe(et,mt,st))==null?void 0:ft.toAffine();return Q?c(Q.x)===X:!1}return{CURVE:n,getPublicKey:y,getSharedSecret:N,sign:H,verify:j,ProjectivePoint:o,Signature:a,utils:h}}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */function ke(e){return{hash:e,hmac:(n,...t)=>Dt(e,n,le(...t)),randomBytes:ue}}function Te(e,n){const t=r=>Ue({...e,...ke(r)});return{...t(n),create:t}}/*! noble-curves - MIT License (c) 2022 Paul Miller (paulmillr.com) */const ie=BigInt("0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f"),Ft=BigInt("0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141"),Re=BigInt(1),Ot=BigInt(2),Kt=(e,n)=>(e+n/Ot)/n;function Ce(e){const n=ie,t=BigInt(3),r=BigInt(6),s=BigInt(11),i=BigInt(22),c=BigInt(23),f=BigInt(44),o=BigInt(88),u=e*e*e%n,m=u*u*e%n,I=Y(m,t,n)*m%n,d=Y(I,t,n)*m%n,S=Y(d,Ot,n)*u%n,L=Y(S,s,n)*S%n,p=Y(L,i,n)*L%n,a=Y(p,f,n)*p%n,h=Y(a,o,n)*a%n,y=Y(h,f,n)*p%n,b=Y(y,t,n)*m%n,N=Y(b,c,n)*L%n,O=Y(N,r,n)*u%n,A=Y(O,Ot,n);if(!Ht.eql(Ht.sqr(A),e))throw new Error("Cannot find square root");return A}const Ht=Jt(ie,void 0,void 0,{sqrt:Ce}),_e=Te({a:BigInt(0),b:BigInt(7),Fp:Ht,n:Ft,Gx:BigInt("55066263022277343669578718895168534326250603453777594175500187360389116729240"),Gy:BigInt("32670510020758816978083085130507043184471273380659243275938904335757337482424"),h:BigInt(1),lowS:!0,endo:{beta:BigInt("0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee"),splitScalar:e=>{const n=Ft,t=BigInt("0x3086d221a7d46bcde86c90e49284eb15"),r=-Re*BigInt("0xe4437ed6010e88286f547fa90abfe4c3"),s=BigInt("0x114ca50f7a8e2f3f657c1108d9d44cfd8"),i=t,c=BigInt("0x100000000000000000000000000000000"),f=Kt(i*e,n),o=Kt(-r*e,n);let u=V(e-f*t-o*s,n),m=V(-f*r-o*i,n);const I=u>c,d=m>c;if(I&&(u=n-u),d&&(m=n-m),u>c||m>c)throw new Error("splitScalar: Endomorphism failed, k="+e);return{k1neg:I,k1:u,k2neg:d,k2:m}}}},de);BigInt(0);_e.ProjectivePoint;export{_e as secp256k1};
