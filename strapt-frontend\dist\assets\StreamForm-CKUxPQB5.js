import{k as $,r as N,S,j as e,n as _,o as J,p as K,s as O,v as Q,w as W,B as y,i as V,_ as H,bw as Z,c as ee}from"./index-G17GlXLb.js";import{B as I}from"./badge-DogxunLX.js";import{A as se}from"./arrow-down-left-DZ4sL2ld.js";import{A as te}from"./arrow-up-right-D82LWdhe.js";import{C as L,S as ae,a as re,b as ne,c as ie,d as A}from"./select-uVdWjx2o.js";import{I as ce}from"./info-BYTODGZ9.js";import{C as E}from"./circle-check-big-BugZDCTu.js";import{I as D}from"./input-CWM2bTJL.js";import{L as F}from"./label-aQpKQY_t.js";import{T as le}from"./TokenSelect-DYUulU6S.js";import{A as oe}from"./arrow-right-gM81zKuh.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const q=$("CircleDollarSign",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8",key:"1h4pet"}],["path",{d:"M12 18V6",key:"zqpxq5"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const de=$("CirclePlus",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M8 12h8",key:"1wcyev"}],["path",{d:"M12 8v8",key:"napkw2"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const B=$("CircleStop",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["rect",{x:"9",y:"9",width:"6",height:"6",rx:"1",key:"1ssd4o"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const me=$("Milestone",[["path",{d:"M12 13v8",key:"1l5pq0"}],["path",{d:"M12 3v3",key:"1n5kay"}],["path",{d:"M4 6a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h13a2 2 0 0 0 1.152-.365l3.424-2.317a1 1 0 0 0 0-1.635l-3.424-2.318A2 2 0 0 0 17 6z",key:"1btarq"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const X=$("Pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R=$("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]),ue=({startTime:s,endTime:i,amount:o,streamed:d,status:m,token:h,streamId:j,onStreamComplete:t})=>{const[b,l]=N.useState(d),[a,n]=N.useState(0);N.useEffect(()=>{if(m===S.Completed||m===S.Canceled){Number(d)<=0?(l("0"),n(0)):(l(o),n(100));return}if(m===S.Paused){l(d),n(Number(d)/Number(o)*100);return}l(d),n(Number(d)/Number(o)*100)},[d,o,m]),N.useEffect(()=>{if(m!==S.Active)return;const x=()=>{const r=Math.floor(Date.now()/1e3),g=i-s,f=Number(o);if(r>=i&&j&&t){console.log("Stream completed by time:",j),t(j),l(f.toFixed(6)),n(100);return}const M=Math.min(r-s,g),k=Math.min(f*(M/g),f);l(k.toFixed(6));const C=k/f*100;n(C),C>=99.9&&j&&t&&(console.log("Stream completed by amount:",j),t(j))};x();const v=setInterval(x,5e3);return()=>clearInterval(v)},[s,i,o,m,j,t]);const u=x=>{const v=Number(x);return Number.parseFloat(v.toFixed(4)).toString()};return e.jsxs("div",{className:"flex flex-col space-y-1",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsxs("span",{className:"text-muted-foreground flex items-center",children:["Streamed",m===S.Active&&e.jsx("span",{className:"ml-1 inline-block w-1.5 h-1.5 rounded-full bg-primary animate-pulse"})]}),e.jsxs("div",{className:"font-medium",children:[e.jsx("span",{className:m===S.Active?"text-primary":"",children:u(Number(b))}),e.jsx("span",{className:"mx-1 text-muted-foreground",children:"/"}),e.jsxs("span",{children:[u(Number(o))," ",h]})]})]}),e.jsxs("div",{className:"flex justify-between text-xs text-muted-foreground",children:[e.jsx("span",{children:"Progress"}),e.jsxs("span",{className:a>=99.9?"text-green-600 dark:text-green-400 font-medium":"",children:[a.toFixed(1),"%"]})]})]})},xe=(s,i=1)=>{if(s==="completed"&&i<=0)return e.jsx(E,{className:"h-5 w-5 text-white"});switch(s){case"active":return e.jsx(R,{className:"h-5 w-5 text-white"});case"paused":return e.jsx(X,{className:"h-5 w-5 text-amber-500"});case"completed":return e.jsx(E,{className:"h-5 w-5 text-white"});case"canceled":return e.jsx(B,{className:"h-5 w-5 text-red-500"});default:return e.jsx(R,{className:"h-5 w-5 text-white"})}},he=s=>{switch(s){case"active":return"success";case"paused":return"warning";case"completed":return"secondary";case"canceled":return"destructive";default:return"success"}},pe=(s,i=1)=>{if(s==="completed"&&i<=0)return"bg-green-500";switch(s){case"active":return"bg-blue-500";case"paused":return"bg-orange-500";case"completed":return"bg-green-500";case"canceled":return"bg-red-500";default:return"bg-blue-500"}},Ne=s=>!s.milestones||s.milestones.length===0?null:e.jsx("div",{className:"relative h-1 mt-1",children:s.milestones.map(i=>e.jsx("div",{className:"absolute top-0 w-1 h-3 bg-primary rounded",style:{left:`${i.percentage}%`,transform:"translateX(-50%)"},title:`${i.description} (${i.percentage}%)`},i.id))});function je(s){return`${s.slice(0,6)}...${s.slice(-4)}`}function U(s){return new Date(s*1e3).toLocaleDateString("en-US",{month:"short",day:"numeric",year:"numeric"})}function ge(s){const i=Math.floor(Date.now()/1e3);if(i>=s)return"Completed";const o=s-i;if(o<60)return"in less than a minute";if(o<3600){const m=Math.floor(o/60);return`in ${m} minute${m>1?"s":""}`}if(o<86400){const m=Math.floor(o/3600);return`in ${m} hour${m>1?"s":""}`}const d=Math.floor(o/86400);return`in ${d} day${d>1?"s":""}`}const fe=N.memo(({stream:s,onPause:i,onResume:o,onCancel:d,onWithdraw:m,onReleaseMilestone:h,onStreamComplete:j})=>{const{toast:t}=V(),b=Math.floor(Date.now()/1e3),l=s.status==="active",a=s.status==="paused",n=s.status==="completed",u=s.status==="canceled",x=n||u,v=s.isRecipient&&s.streamed>0,r=s.streamed/s.total*100,g=b>=s.endTime;b>=s.startTime;const f=ge(s.endTime),T=async()=>{if(i)try{await i(s.id),t({title:"Stream Paused",description:"The payment stream has been paused successfully."})}catch(c){console.error("Error pausing stream:",c),t({title:"Error",description:"Failed to pause the stream. Please try again.",variant:"destructive"})}},M=async()=>{if(o)try{await o(s.id),t({title:"Stream Resumed",description:"The payment stream has been resumed successfully."})}catch(c){console.error("Error resuming stream:",c),t({title:"Error",description:"Failed to resume the stream. Please try again.",variant:"destructive"})}},k=async()=>{if(d)try{await d(s.id),t({title:"Stream Canceled",description:"The payment stream has been canceled successfully."})}catch(c){console.error("Error canceling stream:",c),t({title:"Error",description:"Failed to cancel the stream. Please try again.",variant:"destructive"})}},C=async()=>{if(m)try{await m(s.id),t({title:"Tokens Claimed",description:"Successfully claimed tokens from the stream."})}catch(c){console.error("Error withdrawing from stream:",c),t({title:"Error",description:"Failed to claim tokens. Please try again.",variant:"destructive"})}},P=()=>!s.milestones||s.milestones.length===0||!s.isSender?null:e.jsxs("div",{className:"mt-3 space-y-2",children:[e.jsx("div",{className:"text-xs font-medium text-muted-foreground",children:"Milestone Releases:"}),e.jsx("div",{className:"grid grid-cols-2 gap-2",children:s.milestones.map(c=>{const p=r>=c.percentage;return e.jsxs(y,{variant:c.released?"ghost":"outline",size:"sm",className:`h-auto py-1 text-xs ${c.released?"bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-400":""}`,disabled:!p||c.released||!h,onClick:()=>h==null?void 0:h(s,c),children:[c.released?e.jsx(E,{className:"h-3 w-3 mr-1 text-white"}):e.jsx(q,{className:"h-3 w-3 mr-1"}),c.released?e.jsxs(e.Fragment,{children:["Released ",c.percentage,"%"]}):e.jsxs(e.Fragment,{children:["Release ",c.percentage,"%"]})]},c.id)})})]});return e.jsxs(_,{className:"overflow-hidden border-primary/20 hover:border-primary/40 transition-colors",children:[e.jsx(J,{className:"pb-2",children:e.jsxs("div",{className:"flex justify-between items-start",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsxs(I,{variant:he(s.status),className:"capitalize",children:[xe(s.status,s.streamed),e.jsx("span",{className:"ml-1",children:s.status})]}),s.isRecipient&&e.jsxs(I,{variant:"outline",className:"bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400 border-blue-200 dark:border-blue-800",children:[e.jsx(se,{className:"h-3 w-3 mr-1"}),"Receiving"]}),s.isSender&&e.jsxs(I,{variant:"outline",className:"bg-purple-50 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400 border-purple-200 dark:border-purple-800",children:[e.jsx(te,{className:"h-3 w-3 mr-1"}),"Sending"]})]}),e.jsxs(K,{className:"text-base mt-2",children:[s.isRecipient?"From: ":"To: ",je(s.isRecipient?s.sender:s.recipient)]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"text-sm font-medium",children:[s.total," ",s.token]}),e.jsx("div",{className:"text-xs text-muted-foreground",children:s.rate})]})]})}),e.jsx(O,{className:"pb-3",children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsx(ue,{startTime:s.startTime,endTime:s.endTime,amount:s.total.toString(),streamed:s.streamed.toString(),status:S[s.status],token:s.token,streamId:s.id,onStreamComplete:j}),e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"relative h-4 w-full overflow-hidden rounded-full bg-secondary/30",children:e.jsx("div",{className:`h-full transition-all duration-300 ${pe(s.status,s.streamed)}`,style:{width:`${s.streamed/s.total*100}%`}})}),s.milestones&&s.milestones.length>0&&Ne(s)]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{className:"text-xs text-muted-foreground flex items-center",children:[e.jsx(L,{className:"h-3 w-3 mr-1"})," Start Date"]}),e.jsx("div",{children:U(s.startTime)})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{className:"text-xs text-muted-foreground flex items-center",children:[e.jsx(L,{className:"h-3 w-3 mr-1"})," End Date"]}),e.jsx("div",{children:U(s.endTime)})]})]}),e.jsxs("div",{className:"bg-secondary/30 p-2 rounded-md text-sm flex items-center",children:[e.jsx(Q,{className:"h-4 w-4 mr-2 text-muted-foreground"}),e.jsx("span",{children:x?"Stream has ended":l?`Ends ${f}`:a?"Stream is paused":"Stream is inactive"})]}),P()]})}),e.jsxs(W,{className:"pt-0",children:[s.isSender&&!x&&!g&&r<99.9&&e.jsx("div",{className:"grid grid-cols-2 gap-2 w-full",children:l?e.jsxs(e.Fragment,{children:[e.jsxs(y,{variant:"outline",size:"sm",onClick:T,disabled:!i,children:[e.jsx(X,{className:"h-4 w-4 mr-1"})," Pause"]}),e.jsxs(y,{variant:"destructive",size:"sm",onClick:k,disabled:!d,children:[e.jsx(B,{className:"h-4 w-4 mr-1"})," Cancel"]})]}):a?e.jsxs(e.Fragment,{children:[e.jsxs(y,{variant:"default",size:"sm",onClick:M,disabled:!o,children:[e.jsx(R,{className:"h-4 w-4 mr-1"})," Resume"]}),e.jsxs(y,{variant:"destructive",size:"sm",onClick:k,disabled:!d,children:[e.jsx(B,{className:"h-4 w-4 mr-1"})," Cancel"]})]}):null}),s.isSender&&(g||r>=99.9)&&s.streamed>0&&!x&&e.jsxs("div",{className:"w-full text-center text-sm text-primary bg-primary/10 p-2 rounded-md border border-primary/20",children:[e.jsx(ce,{className:"h-4 w-4 inline-block mr-1"}),"Waiting for recipient to claim tokens"]}),s.isRecipient&&v&&e.jsxs(y,{variant:"default",className:"w-full",onClick:C,disabled:!m||s.streamed<=0,children:[e.jsx(q,{className:"h-4 w-4 mr-1"}),"Claim ",s.streamed," ",s.token]}),x&&s.streamed<=0&&e.jsxs("div",{className:"w-full text-center text-sm text-green-600 dark:text-green-400 font-medium py-1",children:[e.jsx(E,{className:"h-4 w-4 inline-block mr-1 text-white"})," All tokens have been claimed"]})]})]})});fe.displayName="EnhancedStreamCard";const be=({milestones:s,onChange:i,duration:o})=>{const[d,m]=N.useState(""),[h,j]=N.useState(25),t=()=>{if(d.trim()==="")return;const a={id:`milestone-${Date.now()}`,percentage:h,description:d.trim()};i([...s,a]),m(""),j(25)},b=a=>{i(s.filter(n=>n.id!==a))},l=a=>{const n=Math.round(a/100*o);if(n<60)return`${n}m`;const u=Math.floor(n/60),x=n%60;return x>0?`${u}h ${x}m`:`${u}h`};return e.jsxs("div",{className:"space-y-1.5",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(F,{className:"text-xs",children:"Milestones"}),e.jsx(H,{content:e.jsxs("div",{children:[e.jsx("p",{className:"font-medium mb-1",children:"About Milestones"}),e.jsx("p",{className:"mb-1",children:"Milestones let you mark important points in your payment stream."}),e.jsxs("ul",{className:"list-disc pl-4 text-xs space-y-1",children:[e.jsx("li",{children:"Each milestone represents a percentage of the total stream"}),e.jsx("li",{children:"Milestones can be released manually by the sender"}),e.jsx("li",{children:"Released milestones immediately transfer tokens to the recipient"}),e.jsx("li",{children:"Great for project-based work with deliverable checkpoints"})]})]}),iconSize:12,className:"ml-1"})]}),e.jsxs("div",{className:"p-2 bg-secondary/30 rounded-lg space-y-2",children:[e.jsxs("div",{className:"flex gap-2",children:[e.jsx("div",{className:"flex-1",children:e.jsx(D,{placeholder:"Milestone description",value:d,onChange:a=>m(a.target.value),className:"h-8 text-xs"})}),e.jsxs("div",{className:"w-20 relative",children:[e.jsx(D,{type:"number",min:1,max:99,value:h,onChange:a=>j(Number.parseInt(a.target.value,10)||25),"aria-label":"Milestone percentage",title:"Percentage of total stream amount for this milestone",className:"h-8 text-xs pr-6"}),e.jsx("div",{className:"absolute right-0 top-0 bottom-0 flex items-center pr-2",children:e.jsx("span",{className:"text-xs text-muted-foreground",children:"%"})})]}),e.jsx(y,{type:"button",size:"sm",onClick:t,disabled:!d.trim(),className:"h-8 w-8 p-0",children:e.jsx(de,{className:"h-4 w-4"})})]}),s.length>0&&e.jsx("div",{className:"max-h-[100px] overflow-y-auto pr-1",children:s.map(a=>e.jsxs("div",{className:"flex items-center justify-between bg-background p-1.5 rounded text-xs mb-1.5",children:[e.jsxs("div",{className:"flex items-center overflow-hidden max-w-[70%]",children:[e.jsx(me,{className:"h-3.5 w-3.5 mr-1.5 flex-shrink-0 text-primary"}),e.jsx("span",{className:"truncate",children:a.description})]}),e.jsxs("div",{className:"flex items-center flex-shrink-0",children:[e.jsxs("div",{className:"flex items-center text-muted-foreground mr-1.5",children:[e.jsx(L,{className:"h-3 w-3 mr-1 hidden sm:inline"}),e.jsx("span",{className:"hidden sm:inline",children:l(a.percentage)}),e.jsx("span",{className:"mx-1",children:"•"}),e.jsxs("span",{children:[a.percentage,"%"]})]}),e.jsx(y,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",onClick:()=>b(a.id),children:e.jsx(Z,{className:"h-3.5 w-3.5"})})]})]},a.id))})]})]})},ve=({value:s,unit:i,onChange:o,label:d="Duration",id:m="duration",className:h})=>{const j=a=>{const n=Number.parseInt(a.target.value)||0;o(n,i)},t=a=>{o(s,a)},b=(a,n)=>{switch(n){case"seconds":return a;case"minutes":return a*60;case"hours":return a*60*60;case"days":return a*24*60*60}},l=(a,n)=>{const u=b(a,n);if(u<60)return`${u} seconds`;if(u<3600){const r=Math.floor(u/60),g=u%60;return g?`${r} min ${g} sec`:`${r} minutes`}if(u<86400){const r=Math.floor(u/3600),g=Math.floor(u%3600/60);return g?`${r} hr ${g} min`:`${r} hours`}const x=Math.floor(u/86400),v=Math.floor(u%86400/3600);return v?`${x} day ${v} hr`:`${x} days`};return e.jsxs("div",{className:h,children:[d&&e.jsxs("div",{className:"flex items-center",children:[e.jsx(F,{htmlFor:m,className:"text-xs",children:d}),e.jsx(H,{content:e.jsxs("div",{children:[e.jsx("p",{className:"font-medium mb-1",children:"Stream Duration"}),e.jsx("p",{className:"mb-1",children:"Set how long the payment stream will last."}),e.jsxs("ul",{className:"list-disc pl-4 text-xs space-y-1",children:[e.jsx("li",{children:"Tokens will be streamed continuously during this period"}),e.jsx("li",{children:"Longer durations create slower token release rates"}),e.jsx("li",{children:"Recipients can claim tokens at any time during the stream"})]})]}),iconSize:12,className:"ml-1"})]}),e.jsxs("div",{className:"flex space-x-2",children:[e.jsx(D,{id:m,type:"number",min:"1",value:s,onChange:j,className:"flex-1 h-9 text-sm",placeholder:"Duration"}),e.jsxs(ae,{value:i,onValueChange:a=>t(a),children:[e.jsx(re,{className:"w-[90px] h-9 text-sm",children:e.jsx(ne,{placeholder:"Unit"})}),e.jsxs(ie,{children:[e.jsx(A,{value:"seconds",className:"text-sm",children:"Seconds"}),e.jsx(A,{value:"minutes",className:"text-sm",children:"Minutes"}),e.jsx(A,{value:"hours",className:"text-sm",children:"Hours"}),e.jsx(A,{value:"days",className:"text-sm",children:"Days"})]})]})]}),s>0&&e.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:l(s,i)})]})},Ae=({onCancel:s,onSubmit:i,isCreatingStream:o,tokens:d,isLoadingTokens:m})=>{var P;const[h,j]=N.useState(""),[t,b]=N.useState(""),[l,a]=N.useState(60),[n,u]=N.useState("minutes"),[x,v]=N.useState([]),[r,g]=N.useState(d[0]),{toast:f}=V(),T=N.useCallback((c,p)=>{a(c),u(p)},[]),M=N.useCallback(()=>{if(!t||!l)return"0";const c=Number.parseFloat(t);let p=l;n==="minutes"&&(p*=60),n==="hours"&&(p*=3600),n==="days"&&(p*=86400);const w=c/p;return w>=1?`${w.toFixed(2)} ${r.symbol}/second`:w*60>=1?`${(w*60).toFixed(2)} ${r.symbol}/minute`:w*3600>=1?`${(w*3600).toFixed(2)} ${r.symbol}/hour`:`${(w*86400).toFixed(4)} ${r.symbol}/day`},[t,l,n,r.symbol]),k=N.useCallback(()=>{switch(n){case"seconds":return l/60;case"minutes":return l;case"hours":return l*60;case"days":return l*24*60;default:return l}},[l,n]),C=N.useCallback(async c=>{if(c.preventDefault(),!h||!t||!l){f({title:"Missing Information",description:"Please fill in all required fields",variant:"destructive"});return}if(!t||Number.isNaN(Number(t))||Number(t)<=0){f({title:"Invalid Amount",description:"Please enter a valid amount greater than 0",variant:"destructive"});return}if(r.balance&&Number(t)>r.balance){f({title:"Insufficient Balance",description:`You only have ${r.balance} ${r.symbol} available`,variant:"destructive"});return}try{let p=l;n==="minutes"&&(p*=60),n==="hours"&&(p*=3600),n==="days"&&(p*=86400);const w=x.map(z=>z.percentage),G=x.map(z=>z.description),Y=r.symbol;await i({recipient:h,tokenType:Y,amount:t,durationInSeconds:p,milestonePercentages:w,milestoneDescriptions:G})}catch(p){console.error("Error creating stream:",p),f({title:"Error Creating Stream",description:p instanceof Error?p.message:"An unknown error occurred",variant:"destructive"})}},[h,t,l,n,x,r,f,i]);return e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center mb-1",children:[e.jsx(y,{variant:"ghost",size:"sm",onClick:s,className:"mr-2 p-0 h-auto",children:e.jsx(oe,{className:"h-4 w-4 rotate-180"})}),e.jsx("h2",{className:"text-base font-semibold",children:"Create Stream"})]}),e.jsx("form",{onSubmit:C,children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"space-y-1.5",children:[e.jsx(F,{htmlFor:"recipient",className:"text-xs",children:"Recipient"}),e.jsx(D,{id:"recipient",placeholder:"wallet address 0x...",value:h,onChange:c=>j(c.target.value),required:!0,className:"h-9 text-sm"})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[e.jsxs("div",{className:"space-y-1.5",children:[e.jsx(F,{htmlFor:"token",className:"text-xs",children:"Token"}),e.jsx(le,{tokens:d,selectedToken:r,onTokenChange:g,isLoading:m})]}),e.jsxs("div",{className:"space-y-1.5",children:[e.jsx(F,{htmlFor:"amount",className:"text-xs",children:"Total Amount"}),e.jsxs("div",{className:"relative",children:[e.jsx(D,{id:"amount",type:"number",placeholder:"0.00",min:"0.01",step:"0.01",value:t,onChange:c=>b(c.target.value),required:!0,className:`pr-12 h-9 text-sm ${t&&(Number.isNaN(Number(t))||Number(t)<=0||r.balance&&Number(t)>r.balance)?"border-red-500 focus-visible:ring-red-500":""}`}),e.jsx("button",{type:"button",className:"absolute right-1 top-1/2 -translate-y-1/2 px-2 py-0.5 text-xs rounded bg-secondary text-secondary-foreground",onClick:()=>{const c=r.balance||0;b(c.toString())},children:"MAX"})]}),t&&(Number.isNaN(Number(t))||Number(t)<=0||r.balance&&Number(t)>r.balance)&&e.jsx("p",{className:"text-xs text-red-500",children:Number.isNaN(Number(t))?"Invalid number":Number(t)<=0?"Amount > 0":"Insufficient balance"}),!t||!Number.isNaN(Number(t))&&Number(t)>0&&(!r.balance||Number(t)<=r.balance)&&e.jsxs("p",{className:"text-xs text-muted-foreground",children:["Available: ",((P=r.balance)==null?void 0:P.toFixed(2))||0," ",r.symbol]})]})]}),e.jsx(ve,{value:l,unit:n,onChange:T,label:"Duration",className:"space-y-1.5"}),t&&l&&e.jsx("div",{className:"p-2 bg-secondary/50 rounded-lg",children:e.jsxs("div",{className:"flex justify-between text-xs",children:[e.jsx("span",{className:"text-muted-foreground",children:"Rate:"}),e.jsx("span",{children:M()})]})}),l>0&&e.jsx(be,{milestones:x,onChange:v,duration:k()}),e.jsx(y,{type:"submit",className:"w-full h-9 mt-1",disabled:o||!h||!t||Number.isNaN(Number(t))||Number(t)<=0||r.balance&&Number(t)>r.balance,children:o?e.jsxs(e.Fragment,{children:[e.jsx(ee,{size:"sm",className:"mr-2"})," Creating Stream..."]}):e.jsxs(e.Fragment,{children:["Start Stream ",e.jsx(R,{className:"ml-2 h-4 w-4"})]})})]})})]})};export{de as C,fe as E,Ae as S};
