import{k as H,r as t,j as e,B as i,n as J,o as K,p as Q,s as Z,$ as F,w as _,D as V,d as I,e as G,f as L,h as E,i as ee}from"./index-G17GlXLb.js";import{I as v}from"./input-CWM2bTJL.js";import{L as l}from"./label-aQpKQY_t.js";import{T as $,a as se,b as y,c as N}from"./tabs-CCtap3dh.js";import{C as ae,S as te,a as ne,b as re,c as oe,d as ie}from"./select-uVdWjx2o.js";import{T as le}from"./TokenSelect-DYUulU6S.js";import{ComingSoon as M}from"./ComingSoon-CSZhSK_X.js";import{P as U}from"./plus-DMc7oH9p.js";import"./index-B97upM1f.js";import"./chevron-down-BIMAYvOi.js";import"./search-DYwk6_-u.js";import"./arrow-left-D0Dlhjqr.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ce=H("PiggyBank",[["path",{d:"M19 5c-1.5 0-2.8 1.4-3 2-3.5-1.5-11-.3-11 5 0 1.8 0 3 2 4.5V20h4v-2h3v2h4v-4c1-.5 1.7-1 2-2h2v-4h-2c0-1-.5-1.5-1-2V5z",key:"1ivx2i"}],["path",{d:"M2 9v1c0 1.1.9 2 2 2h1",key:"nm575m"}],["path",{d:"M16 11h.01",key:"xkw8gn"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const B=H("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]),De=()=>{var T,P;const[b,w]=t.useState("goals"),[de,me]=t.useState(""),[ue,xe]=t.useState(30),[he,pe]=t.useState("days"),[h,S]=t.useState(r[0]),[W,c]=t.useState(!1),[d,C]=t.useState(""),[m,k]=t.useState(""),[A,z]=t.useState(f[0].id),[a,O]=t.useState(null),[Y,p]=t.useState(!1),[o,j]=t.useState(""),{toast:u}=ee(),[x,D]=t.useState([{id:"goal1",name:"New Laptop",targetAmount:1500,currentAmount:450,token:"USDC",protocol:"Aave",apy:4.5},{id:"goal2",name:"Vacation Fund",targetAmount:3e3,currentAmount:800,token:"SEI",protocol:"CompoundV3",apy:7.2,deadline:"2025-08-15"}]),R=()=>{if(!d||!m||parseFloat(m)<=0){u({title:"Invalid Goal",description:"Please enter a valid goal name and amount",variant:"destructive"});return}const s=f.find(g=>g.id===A),n={id:`goal${Date.now()}`,name:d,targetAmount:parseFloat(m),currentAmount:0,token:h.symbol,protocol:(s==null?void 0:s.name)||"",apy:(s==null?void 0:s.apy)||0};D([...x,n]),c(!1),C(""),k(""),u({title:"Goal Created",description:`"${d}" savings goal has been created`})},X=()=>{if(!a||!o||parseFloat(o)<=0){u({title:"Invalid Deposit",description:"Please enter a valid amount",variant:"destructive"});return}const s=x.map(n=>{if(n.id===a.id){const g=n.currentAmount+parseFloat(o);return{...n,currentAmount:g}}return n});D(s),p(!1),j(""),u({title:"Deposit Successful",description:`${o} ${h.symbol} added to "${a.name}"`})},q=s=>{O(s),S(r.find(n=>n.symbol===s.token)||r[0]),p(!0)};return e.jsxs("div",{className:"space-y-6 max-w-3xl mx-auto",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Savings"}),e.jsx($,{value:b,onValueChange:s=>w(s),children:e.jsxs(se,{className:"grid grid-cols-3",children:[e.jsx(y,{value:"goals",children:"Goals"}),e.jsx(y,{value:"deposit",children:"Deposit"}),e.jsx(y,{value:"withdraw",children:"Withdraw"})]})})]}),e.jsxs($,{value:b,onValueChange:s=>w(s),children:[e.jsx(N,{value:"deposit",className:"mt-0",children:e.jsx("div",{className:"flex justify-center min-h-[300px]",children:e.jsx(M,{title:"Deposits Coming Soon",description:"We're working on a feature that will let you deposit and earn interest on your crypto assets. Stay tuned!"})})}),e.jsx(N,{value:"withdraw",className:"mt-0",children:e.jsx("div",{className:"flex justify-center min-h-[300px]",children:e.jsx(M,{title:"Withdrawals Coming Soon",description:"We're working on a feature that will let you withdraw your crypto assets from savings. Stay tuned!"})})}),e.jsx(N,{value:"goals",className:"mt-0",children:e.jsxs("div",{className:"grid grid-cols-1 gap-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("h3",{className:"text-lg font-medium",children:"Savings Goals"}),e.jsxs(i,{onClick:()=>c(!0),size:"sm",children:[e.jsx(U,{className:"h-4 w-4 mr-1"})," New Goal"]})]}),e.jsxs("div",{className:"grid sm:grid-cols-2 gap-4",children:[x.map(s=>e.jsxs(J,{className:"overflow-hidden",children:[e.jsx(K,{className:"pb-2",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center mr-3",children:e.jsx(B,{className:"h-5 w-5 text-primary"})}),e.jsx(Q,{className:"text-base",children:s.name})]}),e.jsxs("div",{className:"text-right text-sm",children:[e.jsxs("div",{className:"font-medium",children:[s.currentAmount," / ",s.targetAmount]}),e.jsx("div",{className:"text-xs text-muted-foreground",children:s.token})]})]})}),e.jsxs(Z,{className:"pb-2 space-y-2",children:[e.jsx(F,{value:s.currentAmount/s.targetAmount*100,className:"h-2"}),e.jsxs("div",{className:"flex justify-between text-xs text-muted-foreground",children:[e.jsxs("div",{children:[e.jsx("span",{className:"inline-block mr-2",children:s.protocol}),e.jsxs("span",{className:"text-primary",children:[s.apy,"% APY"]})]}),s.deadline&&e.jsxs("div",{children:[e.jsx(ae,{className:"inline h-3 w-3 mr-1"}),new Date(s.deadline).toLocaleDateString()]})]})]}),e.jsx(_,{className:"pt-2",children:e.jsxs(i,{variant:"outline",className:"w-full",size:"sm",onClick:()=>q(s),children:[e.jsx(ce,{className:"h-4 w-4 mr-1"})," Deposit"]})})]},s.id)),x.length===0&&e.jsxs("div",{className:"col-span-2 text-center py-10 border rounded-lg",children:[e.jsx(B,{className:"h-12 w-12 text-muted-foreground mx-auto mb-3"}),e.jsx("h3",{className:"font-medium mb-1",children:"No Savings Goals"}),e.jsx("p",{className:"text-sm text-muted-foreground mb-4",children:"Create a goal to start saving for something special"}),e.jsxs(i,{variant:"outline",onClick:()=>c(!0),children:[e.jsx(U,{className:"h-4 w-4 mr-1"})," Create Goal"]})]})]})]})})]}),e.jsx(V,{open:W,onOpenChange:c,children:e.jsxs(I,{className:"max-w-sm sm:max-w-md",children:[e.jsx(G,{children:e.jsx(L,{children:"Create Savings Goal"})}),e.jsxs("div",{className:"space-y-4 py-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"goal-name",children:"Goal Name"}),e.jsx(v,{id:"goal-name",placeholder:"e.g. New Laptop, Vacation, etc.",value:d,onChange:s=>C(s.target.value)})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"token",children:"Currency"}),e.jsx(le,{tokens:r,selectedToken:h,onTokenChange:S})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"target-amount",children:"Target Amount"}),e.jsx(v,{id:"target-amount",type:"number",placeholder:"0.00",min:"0.01",step:"0.01",value:m,onChange:s=>k(s.target.value)})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"protocol",children:"Protocol"}),e.jsxs(te,{value:A,onValueChange:z,children:[e.jsx(ne,{children:e.jsx(re,{placeholder:"Select protocol"})}),e.jsx(oe,{children:f.map(s=>e.jsxs(ie,{value:s.id,children:[s.name," (",s.apy,"% APY)"]},s.id))})]})]})]}),e.jsx(E,{children:e.jsx(i,{onClick:R,className:"w-full",children:"Create Goal"})})]})}),e.jsx(V,{open:Y,onOpenChange:p,children:e.jsxs(I,{className:"max-w-sm sm:max-w-md",children:[e.jsx(G,{children:e.jsxs(L,{children:["Deposit to ",a==null?void 0:a.name]})}),e.jsxs("div",{className:"space-y-4 py-4",children:[e.jsxs("div",{className:"p-3 bg-secondary/30 rounded-lg mb-4",children:[e.jsxs("div",{className:"flex justify-between mb-1",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Current:"}),e.jsxs("span",{children:[a==null?void 0:a.currentAmount," ",a==null?void 0:a.token]})]}),e.jsxs("div",{className:"flex justify-between mb-1",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Target:"}),e.jsxs("span",{children:[a==null?void 0:a.targetAmount," ",a==null?void 0:a.token]})]}),e.jsx(F,{value:a?a.currentAmount/a.targetAmount*100:0,className:"mt-2"})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(l,{htmlFor:"deposit-amount",children:"Amount to Deposit"}),e.jsxs("div",{className:"relative",children:[e.jsx(v,{id:"deposit-amount",type:"number",placeholder:"0.00",min:"0.01",step:"0.01",value:o,onChange:s=>j(s.target.value),className:"pr-16"}),e.jsx("button",{type:"button",className:"absolute right-1 top-1/2 -translate-y-1/2 px-2 py-1 text-xs rounded bg-secondary text-secondary-foreground",onClick:()=>{const s=r.find(n=>n.symbol===(a==null?void 0:a.token));s!=null&&s.balance&&j(s.balance.toString())},children:"MAX"})]}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:["Available: ",((P=(T=r.find(s=>s.symbol===(a==null?void 0:a.token)))==null?void 0:T.balance)==null?void 0:P.toFixed(2))||0," ",a==null?void 0:a.token]})]}),a&&e.jsxs("div",{className:"p-3 bg-secondary/30 rounded-lg space-y-1",children:[e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-muted-foreground",children:"Protocol:"}),e.jsx("span",{children:a.protocol})]}),e.jsxs("div",{className:"flex justify-between text-sm",children:[e.jsx("span",{className:"text-muted-foreground",children:"Interest Rate:"}),e.jsxs("span",{children:[a.apy,"% APY"]})]})]})]}),e.jsx(E,{children:e.jsx(i,{onClick:X,className:"w-full",children:"Deposit"})})]})})]})},r=[{symbol:"SEI",name:"Sei",balance:1245.78},{symbol:"ETH",name:"Ethereum",balance:.5},{symbol:"USDC",name:"USD Coin",balance:500.45},{symbol:"ATOM",name:"Cosmos",balance:25.32}],f=[{id:"aave",name:"Aave",apy:4.5},{id:"compound",name:"CompoundV3",apy:5.8},{id:"sei-stake",name:"Sei Staking",apy:12.4},{id:"cosmos-stake",name:"Cosmos Hub",apy:14.2}];export{De as default};
