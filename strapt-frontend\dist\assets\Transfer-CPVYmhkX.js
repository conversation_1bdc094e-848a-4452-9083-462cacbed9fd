const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-G17GlXLb.js","assets/index-s5rE7cZb.css","assets/index-CVLj7Jt7.js","assets/ccip-BBRczgY0.js","assets/secp256k1-em1ckmQy.js"])))=>i.map(i=>d[i]);
import{k as de,r as c,aa as ys,a as vs,ac as gs,j as e,F as r,av as V,Q as we,M as Q,R as Ne,ag as Ae,ae as bs,a6 as ws,a7 as _e,ah as Ns,a4 as W,a5 as me,ai as Ts,a8 as ie,aj as ks,a9 as Cs,t as Ie,bB as Rs,I as Ss,n as X,o as J,p as Z,at as fe,s as ee,a1 as Ps,bC as K,w as se,bD as Ee,bE as De,B as $,x as H,bF as As,ad as F,A as oe,v as _s,L as Is,G as Es,D as Ds,d as Ls,e as $s,f as Fs,i as Le}from"./index-G17GlXLb.js";import{I as ce}from"./input-CWM2bTJL.js";import{u as Us}from"./index-B97upM1f.js";import{L as Te}from"./label-aQpKQY_t.js";import{T as Os}from"./TokenSelect-DYUulU6S.js";import{S as Gs}from"./share-2-DrfYo1nq.js";import{A as $e}from"./arrow-right-gM81zKuh.js";import{S as Vs}from"./switch-DAmDmXFQ.js";import{S as Ms}from"./separator-BLTLWNIf.js";import{c as ke}from"./confetti.module-BxKCmZ95.js";import{m as O}from"./proxy-B8vTGf2f.js";import{Q as Fe}from"./qr-code-BnIF1LDH.js";import{Q as Qs}from"./QRCode-DVz9xBTO.js";import{T as Bs,a as Ys,b as Ce,c as Re}from"./tabs-CCtap3dh.js";import{A as qs}from"./arrow-left-D0Dlhjqr.js";import"./search-DYwk6_-u.js";import"./chevron-down-BIMAYvOi.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zs=de("Key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Se=de("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ue=de("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),Oe=c.createContext(void 0);function M(){const t=c.useContext(Oe);if(!t)throw new Error("useTransferContext must be used within a TransferProvider");return t}const Ks=({children:t})=>{const[o,i]=c.useState(""),[a,l]=c.useState(""),[n,s]=c.useState(""),[y,d]=c.useState(""),[h,f]=c.useState(!0),[x,v]=c.useState(!0),[j,T]=c.useState(""),[S,g]=c.useState("direct"),[b,D]=c.useState(""),{address:_}=ys(),{tokens:L,isLoading:m}=vs(),[u,U]=c.useState({symbol:"IDRX",name:"IDRX Token",balance:0,icon:"/IDRX BLUE COIN.svg"});c.useEffect(()=>{L.length>0&&U(L[0])},[L]);const[ss,te]=c.useState(""),[rs,Y]=c.useState(null),[ts,pe]=c.useState(!1),[ae,G]=c.useState(!1),[he,xe]=c.useState(!1),{isLoading:as,isConfirmed:ns,createDirectTransfer:is,createLinkTransfer:je,claimTransfer:ye,refundTransfer:os,generateClaimCode:cs,checkAllowance:ls,USDC_ADDRESS:ds,IDRX_ADDRESS:ms,isPasswordProtected:fs}=gs(),us=()=>"24 hours",ps=k=>k?k.length>16?`${k.slice(0,8)}...${k.slice(-8)}`:k:"",ve=()=>Math.floor(Date.now()/1e3)+86400,q=()=>u.symbol==="USDC"?"USDC":"IDRX",ge=()=>u.symbol==="USDC"?ds:ms,be=()=>!a||Number.isNaN(Number(a))||Number(a)<=0?(r.error("Invalid amount",{description:"Please enter a valid amount greater than 0"}),!1):u.balance&&Number(a)>u.balance?(r.error("Insufficient balance",{description:`You only have ${u.balance} ${u.symbol} available`}),!1):!0,hs={recipient:o,setRecipient:i,amount:a,setAmount:l,grossAmount:n,setGrossAmount:s,note:y,setNote:d,withTimeout:h,setWithTimeout:f,withPassword:x,setWithPassword:v,password:j,setPassword:T,selectedToken:u,setSelectedToken:U,transferType:S,setTransferType:g,transferLink:b,setTransferLink:D,formatTimeout:us,shortenTransferId:ps,tokens:L,isLoadingTokens:m,isLoading:as||m||he,isDirectTransferLoading:he,isConfirmed:ns,isApproving:ts,isApproved:ae,claimCode:ss,transferId:rs,setTransferId:Y,approveToken:async()=>{var k,A;try{if(pe(!0),G(!1),!_)return r.error("No wallet connected"),!1;if(S==="direct")return G(!0),r.success("Direct transfer doesn't require approval"),!0;try{if(await ls(q(),a,_))return console.log("Sufficient allowance already exists"),G(!0),r.success("Token already approved"),!0}catch(C){console.error("Error checking allowance:",C)}const P=u.symbol==="USDC"?(await V(async()=>{const{default:C}=await import("./index-G17GlXLb.js").then(R=>R.fz);return{default:C}},__vite__mapDeps([0,1]))).default.abi:(await V(async()=>{const{default:C}=await import("./index-G17GlXLb.js").then(R=>R.fA);return{default:C}},__vite__mapDeps([0,1]))).default.abi,{parseUnits:N}=await V(async()=>{const{parseUnits:C}=await import("./index-CVLj7Jt7.js");return{parseUnits:C}},__vite__mapDeps([2,0,1,3,4])),w=u.symbol==="USDC"?6:2,E=BigInt("*************"),p=ge(),I=(await V(async()=>{const{default:C}=await import("./index-G17GlXLb.js").then(R=>R.fB);return{default:C}},__vite__mapDeps([0,1]))).default.ProtectedTransferV2.address;try{console.log("Approving token for maximum allowance");const C=BigInt("0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff"),R=await we(Q,{abi:P,functionName:"approve",args:[I,C],address:p,account:_,chain:Q.chains[0]}),z=await Ne(Q,{hash:R});return z.status==="success"?(G(!0),r.success("Token approval successful"),!0):(r.error("Token approval failed"),console.log(z),!1)}catch(C){return console.error("Error approving token:",C),(k=C.message)!=null&&k.includes("rejected")||(A=C.message)!=null&&A.includes("denied")?r.error("Approval rejected",{description:"You canceled the approval transaction"}):r.error("Failed to approve token",{description:"An error occurred while approving the token. Please try again."}),!1}}catch(P){return console.error("Error approving token:",P),r.error("Failed to approve token"),!1}finally{pe(!1)}},createProtectedTransfer:async()=>{var k,A,P;try{if(!_)return r.error("No wallet connected"),!1;if(!be())return!1;if(!ae&&S!=="direct")return r.error("Please approve token transfer first"),!1;if(S==="direct")try{xe(!0);const I=u.symbol==="USDC"?(await V(async()=>{const{default:B}=await import("./index-G17GlXLb.js").then(ne=>ne.fz);return{default:B}},__vite__mapDeps([0,1]))).default.abi:(await V(async()=>{const{default:B}=await import("./index-G17GlXLb.js").then(ne=>ne.fA);return{default:B}},__vite__mapDeps([0,1]))).default.abi,{parseUnits:C}=await V(async()=>{const{parseUnits:B}=await import("./index-CVLj7Jt7.js");return{parseUnits:B}},__vite__mapDeps([2,0,1,3,4])),R=u.symbol==="USDC"?6:2,z=C(a,R),xs=ge(),js=await we(Q,{abi:I,functionName:"transfer",args:[o,z],address:xs,account:_,chain:Q.chains[0]});return(await Ne(Q,{hash:js})).status==="success"?(r.success("Direct transfer successful"),G(!1),s(a),!0):(r.error("Direct transfer failed"),!1)}catch(I){return console.error("Error executing direct transfer:",I),r.error(`Direct transfer failed: ${I instanceof Error?I.message:String(I)}`),!1}finally{xe(!1)}const N=h?ve():0,w=x&&j?j:null,p=await is(o||"0x0000000000000000000000000000000000000000",q(),a,N,x,w);if(p!=null&&p.transferId){te(p.claimCode||""),Y(p.transferId),s(a);const C=`${window.location.origin}/app/claims?id=${p.transferId}`;return D(C),r.success("Transfer created successfully",{description:`${a} ${u.symbol} sent to ${o?`${o.slice(0,6)}...${o.slice(-4)}`:"anyone with the link"}`}),G(!1),!0}return r.error("Transfer failed",{description:"Could not create transfer. Please try again."}),!1}catch(N){return console.error("Error creating transfer:",N),(k=N.message)!=null&&k.includes("rejected")||(A=N.message)!=null&&A.includes("denied")?r.error("Transaction cancelled",{description:"You cancelled the transaction"}):(P=N.message)!=null&&P.includes("insufficient funds")?r.error("Insufficient funds",{description:"You do not have enough funds to complete this transaction"}):r.error("Transfer failed",{description:"Could not create transfer. Please try again."}),!1}},createProtectedLinkTransfer:async()=>{var k,A,P;try{if(!_)return r.error("No wallet connected"),!1;if(!be())return!1;if(!ae)return r.error("Please approve token transfer first"),!1;const N=h?ve():0;let w;if(x){console.log("Creating password-protected link transfer");const E=j||cs();if(console.log("Using password:",E),w=await je(q(),a,N,!0,E),te((w==null?void 0:w.claimCode)||E||""),w!=null&&w.transferId){const I=`${window.location.origin}/app/claims?id=${w.transferId}`;return D(I),Y(w.transferId),s(a),r.success("Password-protected transfer link created",{description:`Transfer of ${a} ${u.symbol} is ready to share`}),G(!1),!0}}else if(console.log("Creating link transfer without password protection"),w=await je(q(),a,N),te(""),w!=null&&w.transferId){const p=`${window.location.origin}/app/claims?id=${w.transferId}`;return D(p),Y(w.transferId),s(a),r.success("Transfer link created",{description:`Transfer of ${a} ${u.symbol} is ready to share`}),G(!1),!0}return r.error("Transfer link failed",{description:"Could not create transfer link. Please try again."}),!1}catch(N){return console.error("Error creating link transfer:",N),(k=N.message)!=null&&k.includes("rejected")||(A=N.message)!=null&&A.includes("denied")?r.error("Transaction cancelled",{description:"You cancelled the transaction"}):(P=N.message)!=null&&P.includes("insufficient funds")?r.error("Insufficient funds",{description:"You do not have enough funds to complete this transaction"}):r.error("Transfer link failed",{description:"Could not create transfer link. Please try again."}),!1}},claimProtectedTransfer:async(k,A)=>{var P,N,w,E,p,I,C;try{if(console.log("Attempting to claim transfer with ID:",k,"and code:",A),!_)return r.error("No wallet connected"),!1;const R=A.trim();return R?(console.log("Using claim code:",R,"length:",R.length),await ye(k,R)):(console.error("Empty claim code provided"),r.error("Invalid claim code",{description:"Please enter a valid claim code"}),!1)}catch(R){return console.error("Error claiming transfer:",R),(P=R.message)!=null&&P.includes("rejected")||(N=R.message)!=null&&N.includes("denied")?r.error("Transaction cancelled",{description:"You cancelled the claim transaction"}):(w=R.message)!=null&&w.includes("insufficient funds")?r.error("Insufficient funds",{description:"You do not have enough funds to pay for transaction fees"}):(E=R.message)!=null&&E.includes("Invalid claim code")||(p=R.message)!=null&&p.includes("invalid password")?r.error("Invalid claim code",{description:"The claim code you entered is incorrect"}):(I=R.message)!=null&&I.includes("already claimed")||(C=R.message)!=null&&C.includes("not claimable")?r.error("Transfer not claimable",{description:"This transfer has already been claimed or is not available"}):r.error("Claim failed",{description:"Could not claim transfer. Please try again."}),!1}},claimProtectedLinkTransfer:async k=>{var A,P,N,w,E;try{if(console.log("Attempting to claim link transfer with ID:",k),!_)return r.error("No wallet connected"),!1;let p=!1;try{p=await fs(k),console.log("Transfer requires password:",p)}catch(I){console.error("Error checking if transfer requires password:",I)}return p?(console.log("Transfer is password-protected, notifying user"),r.error("Password Required",{description:"This transfer is password-protected. Please enter the password to claim it."}),!1):(console.log("Attempting to claim non-password protected transfer"),await ye(k,""))}catch(p){return console.error("Error claiming link transfer:",p),(A=p.message)!=null&&A.includes("rejected")||(P=p.message)!=null&&P.includes("denied")?r.error("Transaction cancelled",{description:"You cancelled the claim transaction"}):(N=p.message)!=null&&N.includes("insufficient funds")?r.error("Insufficient funds",{description:"You do not have enough funds to pay for transaction fees"}):(w=p.message)!=null&&w.includes("already claimed")||(E=p.message)!=null&&E.includes("not claimable")?r.error("Transfer not claimable",{description:"This transfer has already been claimed or is not available"}):r.error("Claim failed",{description:"Could not claim transfer. Please try again."}),!1}},refundProtectedTransfer:async k=>{var A,P,N,w,E;try{return await os(k)}catch(p){return console.error("Error refunding transfer:",p),(A=p.message)!=null&&A.includes("rejected")||(P=p.message)!=null&&P.includes("denied")?r.error("Transaction cancelled",{description:"You cancelled the refund transaction"}):(N=p.message)!=null&&N.includes("insufficient funds")?r.error("Insufficient funds",{description:"You do not have enough funds to pay for transaction fees"}):(w=p.message)!=null&&w.includes("not refundable")||(E=p.message)!=null&&E.includes("cannot refund")?r.error("Transfer not refundable",{description:"This transfer cannot be refunded or has already been claimed"}):r.error("Refund failed",{description:"Could not refund transfer. Please try again."}),!1}}};return e.jsx(Oe.Provider,{value:hs,children:t})};var ue="Radio",[Hs,Ge]=_e(ue),[Ws,Xs]=Hs(ue),Ve=c.forwardRef((t,o)=>{const{__scopeRadio:i,name:a,checked:l=!1,required:n,disabled:s,value:y="on",onCheck:d,form:h,...f}=t,[x,v]=c.useState(null),j=me(o,g=>v(g)),T=c.useRef(!1),S=x?h||!!x.closest("form"):!0;return e.jsxs(Ws,{scope:i,checked:l,disabled:s,children:[e.jsx(W.button,{type:"button",role:"radio","aria-checked":l,"data-state":Ye(l),"data-disabled":s?"":void 0,disabled:s,value:y,...f,ref:j,onClick:ie(t.onClick,g=>{l||d==null||d(),S&&(T.current=g.isPropagationStopped(),T.current||g.stopPropagation())})}),S&&e.jsx(Be,{control:x,bubbles:!T.current,name:a,value:y,checked:l,required:n,disabled:s,form:h,style:{transform:"translateX(-100%)"}})]})});Ve.displayName=ue;var Me="RadioIndicator",Qe=c.forwardRef((t,o)=>{const{__scopeRadio:i,forceMount:a,...l}=t,n=Xs(Me,i);return e.jsx(ks,{present:a||n.checked,children:e.jsx(W.span,{"data-state":Ye(n.checked),"data-disabled":n.disabled?"":void 0,...l,ref:o})})});Qe.displayName=Me;var Js="RadioBubbleInput",Be=c.forwardRef(({__scopeRadio:t,control:o,checked:i,bubbles:a=!0,...l},n)=>{const s=c.useRef(null),y=me(s,n),d=Us(i),h=Cs(o);return c.useEffect(()=>{const f=s.current;if(!f)return;const x=window.HTMLInputElement.prototype,j=Object.getOwnPropertyDescriptor(x,"checked").set;if(d!==i&&j){const T=new Event("click",{bubbles:a});j.call(f,i),f.dispatchEvent(T)}},[d,i,a]),e.jsx(W.input,{type:"radio","aria-hidden":!0,defaultChecked:i,...l,tabIndex:-1,ref:y,style:{...l.style,...h,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});Be.displayName=Js;function Ye(t){return t?"checked":"unchecked"}var Zs=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],re="RadioGroup",[er,Dr]=_e(re,[Ae,Ge]),qe=Ae(),ze=Ge(),[sr,rr]=er(re),Ke=c.forwardRef((t,o)=>{const{__scopeRadioGroup:i,name:a,defaultValue:l,value:n,required:s=!1,disabled:y=!1,orientation:d,dir:h,loop:f=!0,onValueChange:x,...v}=t,j=qe(i),T=bs(h),[S,g]=ws({prop:n,defaultProp:l??null,onChange:x,caller:re});return e.jsx(sr,{scope:i,name:a,required:s,disabled:y,value:S,onValueChange:g,children:e.jsx(Ns,{asChild:!0,...j,orientation:d,dir:T,loop:f,children:e.jsx(W.div,{role:"radiogroup","aria-required":s,"aria-orientation":d,"data-disabled":y?"":void 0,dir:T,...v,ref:o})})})});Ke.displayName=re;var He="RadioGroupItem",We=c.forwardRef((t,o)=>{const{__scopeRadioGroup:i,disabled:a,...l}=t,n=rr(He,i),s=n.disabled||a,y=qe(i),d=ze(i),h=c.useRef(null),f=me(o,h),x=n.value===l.value,v=c.useRef(!1);return c.useEffect(()=>{const j=S=>{Zs.includes(S.key)&&(v.current=!0)},T=()=>v.current=!1;return document.addEventListener("keydown",j),document.addEventListener("keyup",T),()=>{document.removeEventListener("keydown",j),document.removeEventListener("keyup",T)}},[]),e.jsx(Ts,{asChild:!0,...y,focusable:!s,active:x,children:e.jsx(Ve,{disabled:s,required:n.required,checked:x,...d,...l,name:n.name,ref:f,onCheck:()=>n.onValueChange(l.value),onKeyDown:ie(j=>{j.key==="Enter"&&j.preventDefault()}),onFocus:ie(l.onFocus,()=>{var j;v.current&&((j=h.current)==null||j.click())})})})});We.displayName=He;var tr="RadioGroupIndicator",Xe=c.forwardRef((t,o)=>{const{__scopeRadioGroup:i,...a}=t,l=ze(i);return e.jsx(Qe,{...l,...a,ref:o})});Xe.displayName=tr;var Je=Ke,Ze=We,ar=Xe;const es=c.forwardRef(({className:t,...o},i)=>e.jsx(Je,{className:Ie("grid gap-2",t),...o,ref:i}));es.displayName=Je.displayName;const le=c.forwardRef(({className:t,...o},i)=>e.jsx(Ze,{ref:i,className:Ie("aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),...o,children:e.jsx(ar,{className:"flex items-center justify-center",children:e.jsx(Rs,{className:"h-2.5 w-2.5 fill-current text-current"})})}));le.displayName=Ze.displayName;const Pe=({onNext:t,hideTransferMethod:o=!1})=>{var L;const{recipient:i,setRecipient:a,amount:l,setAmount:n,selectedToken:s,setSelectedToken:y,transferType:d,setTransferType:h}=M(),[f,x]=c.useState({}),[v,j]=c.useState({recipient:!1,amount:!1}),T=Ss(),S=m=>{if(!m)return"Recipient address is required";if(!m.startsWith("0x")||m.length!==42)return"Invalid Ethereum address format"},g=m=>{if(!m)return"Amount is required";const u=Number(m);if(Number.isNaN(u))return"Amount must be a valid number";if(u<=0)return"Amount must be greater than 0";if(s.balance&&u>s.balance)return`Insufficient balance (max: ${s.balance} ${s.symbol})`},b=()=>{const m={};if(d==="direct"){const U=S(i);U&&(m.recipient=U)}const u=g(l);return u&&(m.amount=u),x(m),Object.keys(m).length===0},D=m=>{if(j(u=>({...u,[m]:!0})),m==="recipient"&&d==="direct"){const u=S(i);x(U=>({...U,recipient:u}))}else if(m==="amount"){const u=g(l);x(U=>({...U,amount:u}))}},_=()=>{j({recipient:!0,amount:!0}),b()&&t()};return c.useEffect(()=>{const u=new URLSearchParams(T.search).get("to");u!=null&&u.startsWith("0x")&&(a(u),h("direct"))},[T.search,a,h]),c.useEffect(()=>{(v.recipient||v.amount)&&b()},[d,v.recipient,v.amount]),e.jsxs(X,{children:[e.jsx(J,{children:e.jsxs(Z,{className:"text-lg flex items-center",children:[e.jsx(fe,{className:"h-5 w-5 mr-2 text-primary"}),d==="direct"?"Direct Transfer Details":"Link/QR Transfer Details"]})}),e.jsxs(ee,{className:"space-y-4",children:[d==="direct"&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{htmlFor:"recipient",className:"text-sm font-medium",children:"Recipient"}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("div",{className:"relative flex-1",children:e.jsx(ce,{id:"recipient",placeholder:"wallet address 0x...",value:i,onChange:m=>a(m.target.value),onBlur:()=>D("recipient"),required:!0,className:v.recipient&&f.recipient?"border-red-500 focus-visible:ring-red-500":""})}),e.jsx(Ps,{buttonVariant:"outline",buttonSize:"icon",iconOnly:!0,onScanSuccess:m=>{m!=null&&m.startsWith("0x")&&m.length===42&&a(m)}})]}),v.recipient&&f.recipient&&e.jsxs("p",{className:"text-xs text-red-500 mt-1 flex items-center",children:[e.jsx(K,{className:"h-3 w-3 mr-1"}),f.recipient]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{htmlFor:"token",className:"text-sm font-medium",children:"Token"}),e.jsx(Os,{tokens:M().tokens,selectedToken:s,onTokenChange:y})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{htmlFor:"amount",className:"text-sm font-medium",children:"Amount"}),e.jsxs("div",{className:"relative",children:[e.jsx(ce,{id:"amount",type:"number",placeholder:"0.00",min:"0.01",step:"0.01",value:l,onChange:m=>n(m.target.value),onBlur:()=>D("amount"),required:!0,className:`pr-16 ${v.amount&&f.amount?"border-red-500 focus-visible:ring-red-500":""}`}),e.jsx("button",{type:"button",className:"absolute right-1 top-1/2 -translate-y-1/2 px-2 py-1 text-xs rounded bg-secondary text-secondary-foreground",onClick:()=>{const m=s.balance||0;n(m.toString())},children:"MAX"})]}),v.amount&&f.amount&&e.jsxs("p",{className:"text-xs text-red-500 mt-1 flex items-center",children:[e.jsx(K,{className:"h-3 w-3 mr-1"}),f.amount]}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:["Available: ",((L=s.balance)==null?void 0:L.toFixed(2))||0," ",s.symbol]})]}),!o&&e.jsxs("div",{className:"space-y-2",children:[e.jsx("label",{htmlFor:"transfer-method",className:"text-sm font-medium",children:"Transfer Method"}),e.jsxs(es,{id:"transfer-method",value:d,onValueChange:m=>h(m),className:"grid grid-cols-1 gap-4 pt-2",children:[e.jsxs("div",{className:"flex items-center space-x-2 border rounded-lg p-3 cursor-pointer hover:bg-secondary/20",children:[e.jsx(le,{value:"direct",id:"direct"}),e.jsxs(Te,{htmlFor:"direct",className:"flex items-center cursor-pointer",children:[e.jsx(Ue,{className:"h-4 w-4 mr-2 text-primary"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Direct Transfer"}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"Send tokens directly to recipient"})]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2 border rounded-lg p-3 cursor-pointer hover:bg-secondary/20",children:[e.jsx(le,{value:"claim",id:"claim"}),e.jsxs(Te,{htmlFor:"claim",className:"flex items-center cursor-pointer",children:[e.jsx(Gs,{className:"h-4 w-4 mr-2 text-primary"}),e.jsxs("div",{children:[e.jsx("div",{className:"font-medium",children:"Claim via Link/QR"}),e.jsx("div",{className:"text-xs text-muted-foreground",children:"Recipient claims via shared link or QR code"})]})]})]})]})]})]}),e.jsxs(se,{children:[f.general&&e.jsxs(Ee,{variant:"destructive",className:"mb-4",children:[e.jsx(K,{className:"h-4 w-4"}),e.jsx(De,{children:f.general})]}),e.jsxs($,{type:"button",onClick:_,className:"w-full",disabled:Object.keys(f).length>0&&(f.recipient&&v.recipient||f.amount&&v.amount||!!f.general),children:["Continue ",e.jsx($e,{className:"ml-2 h-4 w-4"})]})]})]})},nr=({onNext:t})=>{const{withPassword:o,setWithPassword:i,password:a,setPassword:l,transferType:n}=M();return e.jsxs(X,{children:[e.jsx(J,{children:e.jsxs(Z,{className:"text-lg flex items-center",children:[e.jsx(fe,{className:"h-5 w-5 mr-2 text-primary"}),"Protection Options"]})}),e.jsxs(ee,{className:"space-y-4",children:[n==="claim"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"space-y-0.5",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(Se,{className:"h-4 w-4 mr-2 text-muted-foreground"}),e.jsx("label",{htmlFor:"password",className:"text-sm font-medium",children:"Password Protection"})]}),e.jsx("p",{className:"text-xs text-muted-foreground",children:o?"Recipient must enter a password to claim funds":"Anyone with the link can claim funds without a password"})]}),e.jsx(Vs,{id:"password",checked:o,onCheckedChange:i})]}),o&&e.jsxs("div",{className:"space-y-2 pl-6",children:[e.jsx("label",{htmlFor:"password-input",className:"text-sm font-medium",children:"Password for Recipient"}),e.jsx(ce,{id:"password-input",type:"password",placeholder:"Enter a password",value:a,onChange:s=>l(s.target.value)})]})]}),n==="direct"&&e.jsxs("div",{className:"flex items-center",children:[e.jsx(Se,{className:"h-4 w-4 mr-2 text-muted-foreground"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Direct transfers send tokens immediately to the recipient's wallet address. No password protection is needed for direct transfers."})]})]}),e.jsx(se,{children:e.jsxs($,{type:"button",onClick:t,className:"w-full",children:["Continue ",e.jsx($e,{className:"ml-2 h-4 w-4"})]})})]})},ir=({onSubmit:t})=>{const{recipient:o,amount:i,note:a,withPassword:l,selectedToken:n,transferType:s,isLoading:y,isDirectTransferLoading:d,isApproving:h,isApproved:f,approveToken:x,createProtectedTransfer:v,createProtectedLinkTransfer:j}=M(),T=async()=>{await x()},S=async()=>{try{let g=!1;if(console.log("Confirming transfer with type:",s),console.log("Amount:",i,"Token:",n.symbol),console.log("Password enabled:",l),!i||Number.parseFloat(i)<=0){r.error("Please enter a valid amount");return}if(s==="direct"&&!o){r.error("Please enter a recipient address");return}if(s==="direct")try{const b=await v();console.log("Direct transfer result:",b),g=!!b}catch(b){console.error("Error creating direct transfer:",b),r.error(`Failed to create direct transfer: ${b instanceof Error?b.message:String(b)}`),g=!1}else if(s==="claim")try{const b=await j();console.log("Link transfer result:",b),g=!!b}catch(b){console.error("Error creating link transfer:",b),r.error(`Failed to create link transfer: ${b instanceof Error?b.message:String(b)}`),g=!1}g&&t()}catch(g){console.error("Error in handleConfirm:",g),r.error(`An unexpected error occurred: ${g instanceof Error?g.message:String(g)}`)}};return e.jsxs(X,{children:[e.jsx(J,{children:e.jsxs(Z,{className:"text-lg flex items-center",children:[e.jsx(fe,{className:"h-5 w-5 mr-2 text-primary"}),"Confirm Transfer"]})}),e.jsxs(ee,{className:"space-y-6",children:[e.jsxs("div",{className:"mb-2",children:[s==="direct"&&!d&&e.jsxs(Ee,{className:"mt-4 bg-blue-50 border-blue-200",children:[e.jsx(H,{className:"h-4 w-4 text-blue-500"}),e.jsx(As,{className:"text-blue-700",children:"Instant Direct Transfer"}),e.jsxs(De,{className:"text-blue-600",children:["You've selected an instant direct transfer. This will send ",n.symbol," tokens directly to the recipient's wallet. The transfer will be immediate and cannot be refunded."]})]}),s==="direct"&&d||h||s!=="direct"&&!f&&!h?e.jsxs("div",{className:"mt-4 border rounded-lg overflow-hidden",children:[s==="direct"&&d&&e.jsxs("div",{className:"p-4 bg-primary/5 border-b border-primary/10",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"mr-3",children:e.jsxs("div",{className:"relative",children:[e.jsx(F,{className:"h-5 w-5 animate-spin text-primary/30"}),e.jsx("div",{className:"absolute top-0 left-0 animate-spin-reverse [animation-delay:-0.2s]",children:e.jsx(F,{className:"h-5 w-5 text-primary"})})]})}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-sm",children:"Processing Transfer"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Please wait while your transaction is being processed"})]})]}),e.jsx("div",{className:"mt-3 w-full bg-primary/10 rounded-full h-1.5",children:e.jsx("div",{className:"bg-primary h-1.5 rounded-full animate-pulse w-full"})})]}),s!=="direct"&&!f&&!h&&e.jsx("div",{className:"p-4 bg-amber-50 dark:bg-amber-950/20",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(K,{className:"h-5 w-5 mr-3 text-amber-500"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-sm",children:"Token Approval Required"}),e.jsxs("p",{className:"text-xs text-muted-foreground",children:["Approve the contract to use your ",n.symbol," tokens before confirming the transfer"]})]})]})}),h&&e.jsxs("div",{className:"p-4 bg-primary/5",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"mr-3",children:e.jsxs("div",{className:"relative",children:[e.jsx(F,{className:"h-5 w-5 animate-spin text-primary/30"}),e.jsx("div",{className:"absolute top-0 left-0 animate-spin-reverse [animation-delay:-0.2s]",children:e.jsx(F,{className:"h-5 w-5 text-primary"})})]})}),e.jsxs("div",{children:[e.jsxs("h4",{className:"font-medium text-sm",children:["Approving ",n.symbol]}),e.jsx("p",{className:"text-xs text-muted-foreground",children:"Please confirm the approval transaction in your wallet"})]})]}),e.jsx("div",{className:"mt-3 w-full bg-primary/10 rounded-full h-1.5",children:e.jsx("div",{className:"bg-primary h-1.5 rounded-full animate-pulse w-full"})})]})]}):null,f&&!y&&e.jsx("div",{className:"mt-4 border rounded-lg overflow-hidden",children:e.jsx("div",{className:"p-4 bg-green-50 dark:bg-green-950/20",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx(H,{className:"h-5 w-5 mr-3 text-green-500"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-sm",children:s==="direct"?"Ready to Transfer":`${n.symbol} Approved`}),e.jsx("p",{className:"text-xs text-muted-foreground",children:s==="direct"?`You can now send your ${n.symbol} tokens directly to the recipient.`:"Your tokens are approved for transfer. You can now confirm your transaction."})]})]})})})]}),e.jsx(Ms,{}),e.jsxs("div",{className:"border border-border rounded-lg p-4",children:[e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Recipient:"}),e.jsx("span",{className:"font-medium",children:o?o.length>12?`${o.slice(0,6)}...${o.slice(-4)}`:o:s==="direct"?"Not specified":"Anyone with the link/QR code"})]}),e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Amount:"}),e.jsxs("span",{className:"font-medium",children:[i," ",n.symbol]})]}),e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Method:"}),e.jsx("span",{className:"font-medium",children:s==="direct"?"Instant Direct Transfer":"Claim via Link/QR"})]}),a&&e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Note:"}),e.jsx("span",{className:"font-medium",children:a})]}),s!=="direct"&&l&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Password Protected:"}),e.jsx("span",{className:"font-medium",children:"Yes"})]})]}),e.jsxs("div",{className:"border border-border rounded-lg p-4",children:[e.jsxs("div",{className:"flex justify-between mb-2",children:[e.jsx("span",{className:"text-sm text-muted-foreground",children:"Transfer Fee:"}),e.jsx("span",{className:"font-medium",children:"No fee"})]}),e.jsxs("div",{className:"flex justify-between font-medium",children:[e.jsx("span",{children:"Total:"}),e.jsxs("span",{children:[Number.parseFloat(i).toFixed(3)," ",n.symbol]})]})]})]}),e.jsx(se,{children:s==="direct"?e.jsx($,{type:"button",onClick:S,className:"w-full",disabled:y||d,children:y||d?e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsxs("div",{className:"relative mr-2",children:[e.jsx(F,{className:"h-4 w-4 animate-spin text-primary-foreground/30"}),e.jsx("div",{className:"absolute top-0 left-0 animate-spin-reverse [animation-delay:-0.2s]",children:e.jsx(F,{className:"h-4 w-4 text-primary-foreground"})})]}),e.jsx("span",{children:"Processing Transfer..."})]}):"Send Direct Transfer"}):f?e.jsx($,{type:"button",onClick:S,className:"w-full",disabled:y,children:y?e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsxs("div",{className:"relative mr-2",children:[e.jsx(F,{className:"h-4 w-4 animate-spin text-primary-foreground/30"}),e.jsx("div",{className:"absolute top-0 left-0 animate-spin-reverse [animation-delay:-0.2s]",children:e.jsx(F,{className:"h-4 w-4 text-primary-foreground"})})]}),e.jsx("span",{children:"Processing Transfer..."})]}):l?"Confirm Protected Transfer":"Confirm Transfer"}):e.jsx($,{type:"button",onClick:T,className:"w-full",disabled:h||y,variant:h?"outline":"default",children:h?e.jsxs("div",{className:"flex items-center justify-center",children:[e.jsxs("div",{className:"relative mr-2",children:[e.jsx(F,{className:"h-4 w-4 animate-spin text-primary/30"}),e.jsx("div",{className:"absolute top-0 left-0 animate-spin-reverse [animation-delay:-0.2s]",children:e.jsx(F,{className:"h-4 w-4 text-primary"})})]}),e.jsxs("span",{children:["Approving ",n.symbol,"..."]})]}):e.jsxs(e.Fragment,{children:[e.jsx(H,{className:"mr-2 h-4 w-4 opacity-70"}),"Approve ",n.symbol]})})})]})},or=({onReset:t,onShowQR:o})=>{const{recipient:i,amount:a,withPassword:l,selectedToken:n,transferType:s,transferLink:y,claimCode:d,transferId:h,shortenTransferId:f}=M();c.useEffect(()=>{x()},[]);const x=()=>{const S=Date.now()+3e3,g={startVelocity:30,spread:360,ticks:60,zIndex:0};function b(_,L){return Math.random()*(L-_)+_}const D=setInterval(()=>{const _=S-Date.now();if(_<=0)return clearInterval(D);const L=50*(_/3e3);ke({...g,particleCount:L,origin:{x:b(.1,.3),y:b(0,.2)}}),ke({...g,particleCount:L,origin:{x:b(.7,.9),y:b(0,.2)}})},250)},v=()=>{navigator.clipboard.writeText(y),r.success("Link Copied",{description:"Transfer link copied to clipboard. Share it with the recipient."})},j=()=>{d&&(navigator.clipboard.writeText(d),r.success("Claim Code Copied",{description:"Claim code copied to clipboard. Keep it secure and share only with the recipient."}))};return e.jsx(O.div,{initial:{opacity:0},animate:{opacity:1},className:"text-center",children:e.jsxs(X,{className:"overflow-hidden border-green-500/30 dark:border-green-500/20 shadow-md",children:[e.jsxs(J,{className:"pb-4",children:[e.jsx(O.div,{className:"mx-auto rounded-full bg-green-500/20 p-3 mb-2",initial:{scale:0},animate:{scale:[0,1.2,1]},transition:{duration:.5,times:[0,.7,1]},children:e.jsx(H,{className:"h-8 w-8 text-green-500"})}),e.jsx(O.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},children:e.jsx(Z,{className:"text-xl font-bold text-green-600 dark:text-green-500",children:"Transfer Successful!"})})]}),e.jsxs(ee,{className:"space-y-4",children:[e.jsxs(O.p,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},className:"text-muted-foreground",children:["Your ",s==="direct"?"direct transfer":"protected transfer"," of ",a," ",n.symbol,i?e.jsxs(e.Fragment,{children:[" to ",i.length>12?`${i.slice(0,6)}...${i.slice(-4)}`:i]}):s==="claim"?" via Link/QR":""," has been ",s==="direct"?"sent":"created","."]}),e.jsxs(O.div,{className:"text-sm text-muted-foreground bg-secondary/30 p-4 rounded-md",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.4},children:[e.jsx("p",{children:"No fees are charged for transfers."}),e.jsxs("p",{className:"mt-1",children:[e.jsx("span",{className:"font-medium",children:"Amount:"})," ",a," ",n.symbol]}),e.jsxs("p",{children:[e.jsx("span",{className:"font-medium",children:"Recipient will receive:"})," ",a," ",n.symbol]})]}),s==="claim"&&e.jsxs(O.div,{className:"border border-border rounded-lg p-4",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.5},children:[e.jsx("p",{className:"text-sm text-muted-foreground mb-2",children:"Share this link with the recipient:"}),e.jsx("div",{className:"bg-secondary p-2 rounded text-sm mb-2 overflow-hidden text-ellipsis",children:y}),e.jsxs("div",{className:"grid grid-cols-2 gap-2 mb-3",children:[e.jsxs($,{variant:"outline",size:"sm",onClick:v,children:[e.jsx(oe,{className:"h-4 w-4 mr-1"})," Copy Link"]}),e.jsxs($,{variant:"outline",size:"sm",onClick:o,children:[e.jsx(Fe,{className:"h-4 w-4 mr-1"})," Show QR"]})]}),l&&d&&e.jsxs("div",{className:"mt-3 border-t border-border pt-3",children:[e.jsxs("div",{className:"flex items-center justify-center mb-1",children:[e.jsx(zs,{className:"h-4 w-4 mr-1 text-amber-500"}),e.jsx("p",{className:"text-sm text-amber-500 font-medium",children:"Claim Code (Keep Secure!)"})]}),e.jsx("div",{className:"bg-amber-500/10 p-3 rounded text-center mb-2",children:e.jsx("span",{className:"text-xl font-mono tracking-widest",children:d})}),e.jsxs($,{variant:"outline",size:"sm",onClick:j,className:"w-full",children:[e.jsx(oe,{className:"h-4 w-4 mr-1"})," Copy Code"]}),e.jsx("p",{className:"text-xs text-muted-foreground mt-2",children:"Share this code securely with the recipient. They will need it to claim the funds."}),e.jsxs("p",{className:"text-xs text-amber-600 mt-1",children:[e.jsx("strong",{children:"Important:"})," The password is not included in the link for security. The recipient must enter it manually."]})]}),e.jsxs("div",{className:"mt-3 border-t border-border pt-3",children:[e.jsx("p",{className:"text-sm text-muted-foreground mb-1",children:"Claim Instructions:"}),e.jsxs("p",{className:"text-sm",children:["Recipient should visit: ",e.jsxs("span",{className:"font-medium",children:[window.location.origin,"/app/claims"]})]})]})]}),s==="claim"&&e.jsxs(O.div,{className:"bg-secondary/30 p-3 rounded-md text-sm",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.6},children:[e.jsxs("div",{className:"flex items-center text-amber-500 mb-1",children:[e.jsx(_s,{className:"h-4 w-4 mr-1"})," Refund Protection Enabled"]}),e.jsx("p",{children:"If not claimed within 24 hours, you'll be able to refund the funds back to your wallet."})]})]}),e.jsxs(se,{className:"flex flex-col space-y-2",children:[e.jsx(O.div,{className:"w-full",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.7},whileHover:{scale:1.02},whileTap:{scale:.98},children:e.jsx(Is,{to:"/app",className:"w-full",children:e.jsx($,{variant:"default",className:"w-full",children:"Back to Home"})})}),e.jsx(O.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.8},whileHover:{scale:1.02},whileTap:{scale:.98},children:e.jsx($,{variant:"outline",className:"w-full",onClick:t,children:"Create Another Transfer"})})]})]})})},cr=({showQR:t,onOpenChange:o})=>{const{transferLink:i,amount:a,selectedToken:l}=M(),{toast:n}=Le(),s=Es(),y=()=>{navigator.clipboard.writeText(i),n({title:"Link Copied",description:"Transfer link copied to clipboard"})};return e.jsx(Ds,{open:t,onOpenChange:o,children:e.jsxs(Ls,{className:s?"sm:max-w-[92%] w-[92%] mx-auto rounded-xl px-3 py-4":"max-w-xs mx-auto sm:max-w-md",children:[e.jsx($s,{children:e.jsx(Fs,{children:"Transfer QR Code"})}),e.jsxs("div",{className:"flex flex-col items-center justify-center space-y-4",children:[e.jsx(Qs,{value:i,size:s?180:200}),e.jsxs("p",{className:"text-sm text-center text-muted-foreground",children:["Share this QR code to claim ",a," ",l.symbol]}),e.jsxs($,{variant:"outline",size:"sm",onClick:y,className:"mt-2",children:[e.jsx(oe,{className:"h-4 w-4 mr-1"})," Copy Link"]})]})]})})},lr=()=>{const[t,o]=c.useState(1),[i,a]=c.useState(!1),{toast:l}=Le(),{transferType:n,setTransferType:s,recipient:y,amount:d,selectedToken:h,withPassword:f}=M();c.useEffect(()=>{s("direct")},[s]);const x=()=>{o(t+1),window.scrollTo(0,0)};c.useEffect(()=>{t===2&&n==="direct"&&(o(t+1),window.scrollTo(0,0))},[t,n]);const v=()=>{o(t===3&&n==="direct"?1:t-1)},j=()=>{o(1)},T=D=>{D.preventDefault(),t<3&&x()},S=()=>{n==="direct"?l({title:"Transfer Successful",description:`${d} ${h.symbol} has been sent to ${y.slice(0,6)}...${y.slice(-4)}`}):n==="claim"&&l({title:"Transfer Created",description:f?`Password-protected transfer of ${d} ${h.symbol} is ready to share`:`Transfer of ${d} ${h.symbol} is ready to share`}),x()},g=()=>{a(!0)},b=D=>{D==="send"?s("direct"):D==="claim"&&s("claim")};return e.jsxs("div",{className:"space-y-6 max-w-3xl mx-auto",children:[e.jsxs("div",{className:"flex items-center mb-4",children:[t>1&&t<4&&e.jsx($,{variant:"ghost",size:"sm",onClick:v,className:"mr-4 p-0 h-auto",children:e.jsx(qs,{className:"h-5 w-5"})}),e.jsxs("h1",{className:"text-xl font-semibold",children:[t===1&&"Transfer Tokens",t===2&&"Protection Options",t===3&&"Confirm Transfer",t===4&&"Transfer Created"]})]}),t===1?e.jsxs(Bs,{defaultValue:"send",value:n==="direct"?"send":"claim",onValueChange:b,className:"w-full",children:[e.jsxs(Ys,{className:"grid w-full grid-cols-2 mb-6",children:[e.jsxs(Ce,{value:"send",className:"flex items-center gap-2",children:[e.jsx(Ue,{className:"h-4 w-4"}),e.jsx("span",{children:"Direct Transfer"})]}),e.jsxs(Ce,{value:"claim",className:"flex items-center gap-2",children:[e.jsx(Fe,{className:"h-4 w-4"}),e.jsx("span",{children:"Link/QR Transfer"})]})]}),e.jsx(Re,{value:"send",className:"mt-0",children:e.jsx("form",{onSubmit:T,children:e.jsx(Pe,{onNext:x,hideTransferMethod:!0})})}),e.jsx(Re,{value:"claim",className:"mt-0",children:e.jsx("form",{onSubmit:T,children:e.jsx(Pe,{onNext:x,hideTransferMethod:!0})})})]}):e.jsxs("form",{onSubmit:T,children:[t===2&&n==="claim"&&e.jsx(nr,{onNext:x}),t===3&&e.jsx(ir,{onSubmit:S}),t===4&&e.jsx(or,{onReset:j,onShowQR:g})]}),e.jsx(cr,{showQR:i,onOpenChange:a})]})},Lr=()=>e.jsx(Ks,{children:e.jsx(lr,{})});export{Lr as default};
