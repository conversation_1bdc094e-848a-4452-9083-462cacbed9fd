"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.blake2s = exports.BLAKE2s = exports.compress = exports.G2s = exports.G1s = exports.B2S_IV = void 0;
/**
 * Blake2s hash function. Focuses on 8-bit to 32-bit platforms. blake2b for 64-bit, but in JS it is slower.
 * @module
 * @deprecated
 */
const _blake_ts_1 = require("./_blake.js");
const _md_ts_1 = require("./_md.js");
const blake2_ts_1 = require("./blake2.js");
/** @deprecated Use import from `noble/hashes/blake2` module */
exports.B2S_IV = _md_ts_1.SHA256_IV;
/** @deprecated Use import from `noble/hashes/blake2` module */
exports.G1s = _blake_ts_1.G1s;
/** @deprecated Use import from `noble/hashes/blake2` module */
exports.G2s = _blake_ts_1.G2s;
/** @deprecated Use import from `noble/hashes/blake2` module */
exports.compress = blake2_ts_1.compress;
/** @deprecated Use import from `noble/hashes/blake2` module */
exports.BLAKE2s = blake2_ts_1.BLAKE2s;
/** @deprecated Use import from `noble/hashes/blake2` module */
exports.blake2s = blake2_ts_1.blake2s;
//# sourceMappingURL=blake2s.js.map