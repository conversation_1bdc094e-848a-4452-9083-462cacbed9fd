import{k as R,r as o,u as O,l as _,j as e,n as $,o as M,p as Q,B as g,q as z,s as q,am as U,an as J,ao as K,D as W,d as B,e as H,f as P,g as G,h as X,ad as Y,i as V}from"./index-G17GlXLb.js";import{I as C}from"./input-CWM2bTJL.js";import{L as w}from"./label-aQpKQY_t.js";import{U as Z}from"./skeleton-CLgd6C74.js";import{S as ee}from"./search-DYwk6_-u.js";import{A as se}from"./arrow-up-right-D82LWdhe.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ae=R("StarOff",[["path",{d:"M8.34 8.34 2 9.27l5 4.87L5.82 21 12 17.77 18.18 21l-.59-3.43",key:"16m0ql"}],["path",{d:"M18.42 12.76 22 9.27l-6.91-1L12 2l-1.44 2.91",key:"1vt8nq"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const te=R("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]]),y=[{id:"1",name:"Sarah Miller",address:"0x1234...5678",username:"@sarah",avatar:""},{id:"2",name:"Alex Rodriguez",address:"0x9876...5432",username:"@alex_r",avatar:""},{id:"3",name:"Jamie Smith",address:"0x6543...2109",username:"@jamies",avatar:""},{id:"4",name:"Taylor Wong",address:"0x8765...4321",username:"@taylor",avatar:""}],D="strapt_contacts";function re(){const[m,i]=o.useState([]),[N,j]=o.useState(!0),{address:l}=O();o.useEffect(()=>{(()=>{if(!l){i(y),j(!1);return}try{const s=`${D}_${l}`,a=localStorage.getItem(s);if(a){const t=JSON.parse(a);i(t)}else i(y)}catch(s){console.error("Error loading contacts:",s),i(y)}j(!1)})()},[l]);const c=o.useCallback(n=>{if(l)try{const s=`${D}_${l}`;localStorage.setItem(s,JSON.stringify(n))}catch(s){console.error("Error saving contacts:",s)}},[l]),h=o.useCallback(n=>{if(!l)return;const s={...n,id:`contact_${Date.now()}`};i(a=>{const t=[...a,s];return c(t),t})},[l,c]),x=o.useCallback((n,s)=>{l&&i(a=>{const t=a.map(u=>u.id===n?{...u,...s}:u);return c(t),t})},[l,c]),b=o.useCallback(n=>{l&&i(s=>{const a=s.filter(t=>t.id!==n);return c(a),a})},[l,c]),f=o.useCallback(n=>{l&&i(s=>{const a=s.map(t=>t.id===n?{...t,isFavorite:!t.isFavorite}:t);return c(a),a})},[l,c]),p=o.useCallback(n=>{if(!n)return m;const s=n.toLowerCase();return m.filter(a=>a.name.toLowerCase().includes(s)||a.username.toLowerCase().includes(s)||a.address.toLowerCase().includes(s))},[m]);return{contacts:m,isLoading:N,addContact:h,updateContact:x,removeContact:b,toggleFavorite:f,searchContacts:p}}const ue=()=>{const{contacts:m,isLoading:i,addContact:N,toggleFavorite:j,searchContacts:l}=re(),[c,h]=o.useState(!1),[x,b]=o.useState(""),[f,p]=o.useState(!1),{toast:n}=V(),s=_(),a=o.useRef(null),t=o.useRef(null),u=o.useRef(null),S=x?l(x):m,I=r=>{const d=m.find(v=>v.id===r);d&&s("/app/transfer",{state:{recipient:d.address,username:d.username}})},T=r=>{j(r),n({title:"Contact updated",description:"Contact favorite status has been updated",duration:3e3})},E=r=>{var A,F,L;r.preventDefault(),p(!0);const d=((A=a.current)==null?void 0:A.value)||"",v=((F=t.current)==null?void 0:F.value)||"",k=((L=u.current)==null?void 0:L.value)||"";if(!d||!v||!k){n({title:"Missing information",description:"Please fill in all fields",variant:"destructive",duration:3e3}),p(!1);return}N({name:d,username:v,address:k,avatar:"",isFavorite:!1,lastInteraction:new Date().toISOString()}),a.current&&(a.current.value=""),t.current&&(t.current.value=""),u.current&&(u.current.value=""),n({title:"Contact added",description:`${d} has been added to your contacts`,duration:3e3}),p(!1),h(!1)};return e.jsxs(e.Fragment,{children:[e.jsxs($,{children:[e.jsxs(M,{className:"pb-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx(Q,{className:"text-xl",children:"Quick Contacts"}),e.jsxs(g,{size:"sm",variant:"outline",onClick:()=>h(!0),children:[e.jsx(Z,{className:"h-4 w-4 mr-1"})," Add"]})]}),e.jsx(z,{children:"Quickly send funds to your frequent contacts"})]}),e.jsxs(q,{children:[e.jsxs("div",{className:"relative mb-4",children:[e.jsx(ee,{className:"absolute left-3 top-2.5 h-4 w-4 text-muted-foreground"}),e.jsx(C,{placeholder:"Search contacts...",className:"pl-10",value:x,onChange:r=>b(r.target.value)})]}),e.jsx("div",{className:"space-y-3",children:i?Array.from({length:3}).map((r,d)=>e.jsxs("div",{className:"flex items-center justify-between bg-secondary/30 rounded-lg p-3 animate-pulse",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"h-10 w-10 rounded-full bg-muted"}),e.jsxs("div",{children:[e.jsx("div",{className:"h-4 w-24 bg-muted rounded mb-2"}),e.jsx("div",{className:"h-3 w-16 bg-muted rounded"})]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx("div",{className:"h-8 w-8 rounded-md bg-muted"}),e.jsx("div",{className:"h-8 w-8 rounded-md bg-muted"})]})]},`skeleton-contact-${d}-${Date.now()}`)):S.length===0?e.jsx("p",{className:"text-center text-muted-foreground py-4",children:"No contacts found"}):S.map(r=>e.jsxs("div",{className:"flex items-center justify-between bg-secondary/30 rounded-lg p-3",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs(U,{children:[e.jsx(J,{src:r.avatar}),e.jsx(K,{children:r.name.split(" ").map(d=>d[0]).join("")})]}),e.jsxs("div",{children:[e.jsx("p",{className:"font-medium",children:r.name}),e.jsx("p",{className:"text-sm text-muted-foreground",children:r.username})]})]}),e.jsxs("div",{className:"flex gap-2",children:[e.jsx(g,{size:"sm",variant:"ghost",className:"h-8 w-8 p-0",title:r.isFavorite?"Remove from favorites":"Mark as favorite",onClick:()=>T(r.id),children:r.isFavorite?e.jsx(te,{className:"h-4 w-4 text-yellow-500"}):e.jsx(ae,{className:"h-4 w-4"})}),e.jsx(g,{size:"sm",className:"h-8 w-8 p-0",onClick:()=>I(r.id),title:"Send funds",children:e.jsx(se,{className:"h-4 w-4"})})]})]},r.id))})]})]}),e.jsx(W,{open:c,onOpenChange:h,children:e.jsxs(B,{className:"sm:max-w-md",children:[e.jsxs(H,{children:[e.jsx(P,{children:"Add New Contact"}),e.jsx(G,{children:"Add someone to your quick contacts for faster transfers"})]}),e.jsxs("form",{onSubmit:E,children:[e.jsxs("div",{className:"grid gap-4 py-4",children:[e.jsxs("div",{className:"grid gap-2",children:[e.jsx(w,{htmlFor:"name",children:"Name"}),e.jsx(C,{id:"name",placeholder:"Contact name",ref:a})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(w,{htmlFor:"username",children:"Username or ENS"}),e.jsx(C,{id:"username",placeholder:"@username or name.eth",ref:t})]}),e.jsxs("div",{className:"grid gap-2",children:[e.jsx(w,{htmlFor:"address",children:"Wallet Address"}),e.jsx(C,{id:"address",placeholder:"0x...",ref:u})]})]}),e.jsxs(X,{children:[e.jsx(g,{type:"button",variant:"outline",onClick:()=>h(!1),disabled:f,children:"Cancel"}),e.jsx(g,{type:"submit",disabled:f,children:f?e.jsxs(e.Fragment,{children:[e.jsx(Y,{className:"mr-2 h-4 w-4 animate-spin"}),"Adding..."]}):"Add Contact"})]})]})]})})]})};export{ue as default};
