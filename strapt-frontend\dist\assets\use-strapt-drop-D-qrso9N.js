import{r as A,u as De,a as Ae,J as W,K as k,M as o,N as F,O as T,F as t,P as E,Q as j,R as $,U as ye,V as be,W as Ie}from"./index-G17GlXLb.js";import m from"./StraptDrop-DBtBqCxY.js";const f=W.StraptDrop.address,oe=W.StraptDrop.supportedTokens.USDC,X=W.StraptDrop.supportedTokens.IDRX;function ke(){const[re,g]=A.useState(!1),[te,B]=A.useState(!1),[ae,S]=A.useState(!1),[ne,H]=A.useState(!1),[se,_]=A.useState(!1),[ie,O]=A.useState(!1),[ce,le]=A.useState(null),{address:c,isConnected:N}=De(),{tokens:de}=Ae(),ue=A.useCallback(r=>{switch(r){case"USDC":return oe;case"IDRX":return X;default:throw new Error(`Unsupported token type: ${r}`)}},[]),fe=A.useCallback(r=>{switch(r){case"USDC":return 6;case"IDRX":return 2;default:return 18}},[]),ge=async(r,e,l,d,w,u)=>{var I,i,h,P,C,x,J,K,Q,Y,z;try{if(g(!0),!N||!c)throw console.error("No wallet connected"),t.error("Please connect your wallet"),new Error("No wallet connected");const D=ue(r),we=fe(r),p=ye(e,we),G=BigInt(Math.floor(Date.now()/1e3)+w*3600),U=r==="IDRX"?be.abi:Ie.abi;console.log("Checking allowance...");const Z=await T(o,{address:D,abi:U,functionName:"allowance",args:[c,f]});if(console.log("Current allowance:",Z.toString()),console.log("Required amount:",p.toString()),Z<p){B(!0),t.info("Approving token transfer...");try{const n=k(o);if(!n||!n.address)throw new Error("No wallet connected");const{request:L}=await E(o,{address:D,abi:U,functionName:"approve",args:[f,p],account:n.address});console.log("Sending approval transaction...");try{const s=await j(o,L);console.log("Approval transaction sent with hash:",s),console.log("Waiting for approval transaction to be confirmed...");const v=await $(o,{hash:s});console.log("Approval transaction confirmed:",v),await new Promise(y=>setTimeout(y,2e3)),console.log("Verifying allowance after approval...");const R=await T(o,{address:D,abi:U,functionName:"allowance",args:[c,f]});if(console.log("New allowance after approval:",R.toString()),console.log("Required amount:",p.toString()),R<p)return console.error("Allowance verification failed. Approved amount not reflected yet."),t.error("Approval verification failed",{description:"The approval transaction was confirmed, but the allowance is not yet reflected. Please try again in a few seconds."}),B(!1),g(!1),null;t.success(`Approved ${r} for transfer`)}catch(s){if((I=s.message)!=null&&I.includes("rejected")||(i=s.message)!=null&&i.includes("denied")||(h=s.message)!=null&&h.includes("cancelled")||(P=s.message)!=null&&P.includes("user rejected"))return console.log("User rejected approval transaction:",s),B(!1),g(!1),null;throw s}}catch(n){throw console.error("Error approving token:",n),t.error("Failed to approve token"),n}finally{B(!1)}}S(!0),t.info("Creating STRAPT Drop...");try{const n=k(o);if(!n||!n.address)throw new Error("No wallet connected");const L=Math.floor(Date.now()/1e3)+48*60*60;let s,v,R;try{s=(await E(o,{address:f,abi:m.abi,functionName:"createDrop",args:[D,p,BigInt(l),d,G,u],account:n.address})).request}catch(a){if(console.error("Simulation error:",a),(C=a.message)!=null&&C.includes("insufficient allowance")){console.error("Insufficient allowance detected in simulation.");let b=BigInt(0),M=0;const ee=3;for(;M<ee;){if(b=await T(o,{address:D,abi:U,functionName:"allowance",args:[c,f]}),console.log(`Retry ${M+1}/${ee} - Current allowance:`,b.toString()),console.log("Required amount:",p.toString()),b>=p){console.log("Allowance is now sufficient after retry");try{s=(await E(o,{address:f,abi:m.abi,functionName:"createDrop",args:[D,p,BigInt(l),d,G,u],account:n.address})).request,console.log("Simulation succeeded after retry");break}catch(q){console.error("Simulation still failing after retry:",q)}}await new Promise(q=>setTimeout(q,3e3)),M++}if(b<p||!s)return t.error("Insufficient allowance",{description:"The approval transaction was confirmed, but the allowance is still insufficient. Please try again in a few moments."}),S(!1),g(!1),null}if((x=a.message)!=null&&x.includes("0xfb8f41b2"))console.log("Found known error signature 0xfb8f41b2. Attempting to bypass simulation."),s={address:f,abi:m.abi,functionName:"createDrop",args:[D,p,BigInt(l),d,BigInt(L),u],account:n.address};else throw a}console.log("Sending create drop transaction...");try{v=await j(o,s),console.log("Create drop transaction sent with hash:",v),console.log("Waiting for create drop transaction to be confirmed..."),R=await $(o,{hash:v}),console.log("Create drop transaction confirmed:",R)}catch(a){return(J=a.message)!=null&&J.includes("rejected")||(K=a.message)!=null&&K.includes("denied")||(Q=a.message)!=null&&Q.includes("cancelled")||(Y=a.message)!=null&&Y.includes("user rejected")?(console.log("User rejected transaction:",a),S(!1),g(!1),null):((z=a.message)!=null&&z.includes("insufficient allowance")?(console.error("Insufficient allowance error in transaction:",a),t.error("Insufficient token allowance",{description:"The approval transaction was successful, but the blockchain needs more time to process it. Please wait a moment and try again."})):(console.error("Error in drop creation transaction:",a),t.error("Failed to create STRAPT Drop",{description:"The token approval was successful, but the drop creation failed. Your tokens are still in your wallet."})),S(!1),g(!1),null)}let y=null;const he=R.logs||[];for(const a of he)try{const b=F({abi:m.abi,data:a.data,topics:a.topics});if(b.eventName==="DropCreated"){y=b.args.dropId;break}}catch{}return y||(y=`0x${Array.from({length:64},()=>Math.floor(Math.random()*16).toString(16)).join("")}`,console.warn("Could not find drop ID in logs, using generated ID:",y)),le(y),y}catch(n){throw console.error("Error creating drop:",n),n}finally{S(!1)}}catch(D){throw console.error("Error creating drop:",D),D}finally{g(!1)}},pe=async r=>{try{if(g(!0),H(!0),!N||!c)throw console.error("No wallet connected"),t.error("Please connect your wallet"),new Error("No wallet connected");t.info("Claiming tokens from STRAPT Drop...");try{const e=k(o);if(!e||!e.address)throw new Error("No wallet connected");const{request:l}=await E(o,{address:f,abi:m.abi,functionName:"claimDrop",args:[r],account:e.address});console.log("Sending claim transaction...");const d=await j(o,l);console.log("Claim transaction sent with hash:",d),console.log("Waiting for claim transaction to be confirmed...");const w=await $(o,{hash:d});console.log("Claim transaction confirmed:",w);let u=BigInt(0);for(const I of w.logs)try{const i=F({abi:m.abi,data:I.data,topics:I.topics});if(i.eventName==="DropClaimed"){u=i.args.amount;break}}catch{}return u===BigInt(0)&&(u=BigInt(1e6),console.warn("Could not find claimed amount in logs, using default value:",u.toString())),t.success("Successfully claimed tokens from STRAPT Drop!"),u}catch(e){throw console.error("Error claiming drop:",e),t.error("Failed to claim STRAPT Drop"),e}finally{H(!1)}}catch(e){throw console.error("Error claiming drop:",e),t.error("Failed to claim STRAPT Drop"),e}finally{g(!1)}},me=async r=>{try{if(g(!0),_(!0),!N||!c)throw console.error("No wallet connected"),t.error("Please connect your wallet"),new Error("No wallet connected");t.info("Refunding expired STRAPT Drop...");try{const e=k(o);if(!e||!e.address)throw new Error("No wallet connected");const{request:l}=await E(o,{address:f,abi:m.abi,functionName:"refundExpiredDrop",args:[r],account:e.address});console.log("Sending refund transaction...");const d=await j(o,l);console.log("Refund transaction sent with hash:",d),console.log("Waiting for refund transaction to be confirmed...");const w=await $(o,{hash:d});return console.log("Refund transaction confirmed:",w),t.success("Successfully refunded expired STRAPT Drop"),w}catch(e){throw console.error("Error refunding drop:",e),t.error("Failed to refund STRAPT Drop"),e}}catch(e){throw console.error("Error refunding drop:",e),t.error("Failed to refund STRAPT Drop"),e}finally{g(!1),_(!1)}},V=async r=>{try{const e=await T(o,{address:f,abi:m.abi,functionName:"getDropInfo",args:[r]});return{creator:e[0],tokenAddress:e[1],totalAmount:e[2],remainingAmount:e[3],claimedCount:e[4],totalRecipients:e[5],isRandom:e[6],expiryTime:e[7],message:e[8],isActive:e[9],amountPerRecipient:e[6]?0n:e[2]/e[5]}}catch(e){throw console.error("Error getting drop info:",e),e}};return{createDrop:ge,claimDrop:pe,refundExpiredDrop:me,getDropInfo:V,hasAddressClaimed:async(r,e)=>{try{return await T(o,{address:f,abi:m.abi,functionName:"hasAddressClaimed",args:[r,e]})}catch(l){throw console.error("Error checking if address claimed:",l),l}},getUserCreatedDrops:async()=>{try{if(O(!0),!N||!c)throw console.error("No wallet connected"),new Error("No wallet connected");const r=k(o);if(!r||!r.address)throw new Error("No wallet connected");console.log("Fetching drops created by:",c);const d=await fetch(`https://sepolia-blockscout.lisk.com/api/v2/addresses/${f}/logs?topic0=0x7d84a6263ae0d98d3329bd7b46bb4e8d6f98cd35a7adb45c274c8b7fd5ebd5e0`);if(!d.ok)throw new Error("Failed to fetch events from Blockscout API");const w=await d.json();console.log("Events data:",w);const u=w.items.filter(i=>{try{const h=F({abi:m.abi,data:i.data,topics:i.topics});return h.eventName==="DropCreated"?h.args.creator.toLowerCase()===c.toLowerCase():!1}catch{return!1}});return console.log("User events:",u),(await Promise.all(u.map(async i=>{try{const C=F({abi:m.abi,data:i.data,topics:i.topics}).args.dropId,x=await V(C);return{id:C,info:x}}catch(h){return console.error("Error processing drop event:",h),null}}))).filter(i=>i!==null)}catch(r){return console.error("Error getting user created drops:",r),console.log("Falling back to mock data"),[{id:`0x${Array.from({length:64},()=>Math.floor(Math.random()*16).toString(16)).join("")}`,info:{creator:c,tokenAddress:X,totalAmount:BigInt(1e6),remainingAmount:BigInt(5e5),claimedCount:BigInt(5),totalRecipients:BigInt(10),amountPerRecipient:BigInt(1e5),isRandom:!1,expiryTime:BigInt(Math.floor(Date.now()/1e3)+86400),message:"Test drop 1",isActive:!0}},{id:`0x${Array.from({length:64},()=>Math.floor(Math.random()*16).toString(16)).join("")}`,info:{creator:c,tokenAddress:oe,totalAmount:BigInt(5e6),remainingAmount:BigInt(2e6),claimedCount:BigInt(3),totalRecipients:BigInt(5),amountPerRecipient:BigInt(0),isRandom:!0,expiryTime:BigInt(Math.floor(Date.now()/1e3)+43200),message:"Random distribution test",isActive:!0}},{id:`0x${Array.from({length:64},()=>Math.floor(Math.random()*16).toString(16)).join("")}`,info:{creator:c,tokenAddress:X,totalAmount:BigInt(2e6),remainingAmount:BigInt(0),claimedCount:BigInt(10),totalRecipients:BigInt(10),amountPerRecipient:BigInt(2e5),isRandom:!1,expiryTime:BigInt(Math.floor(Date.now()/1e3)-86400),message:"Expired drop",isActive:!1}}]}finally{O(!1)}},currentDropId:ce,isLoading:re,isApproving:te,isCreating:ae,isClaiming:ne,isRefunding:se,isLoadingUserDrops:ie,tokens:de}}export{ke as u};
