import{k as K,r as a,u as Q,a as ee,Y as se,b as te,Z as ae,S as o,j as e,_ as re,B as d,C as T,c as P,D as $,d as D,e as ne,f as ie,g as le,h as oe,i as ce}from"./index-G17GlXLb.js";import{T as me,a as de,b as R,c as L}from"./tabs-CCtap3dh.js";import{C as A,E as F,S as ue}from"./StreamForm-CKUxPQB5.js";import{C as xe}from"./circle-check-big-BugZDCTu.js";import"./badge-DogxunLX.js";import"./arrow-down-left-DZ4sL2ld.js";import"./arrow-up-right-D82LWdhe.js";import"./select-uVdWjx2o.js";import"./index-B97upM1f.js";import"./chevron-down-BIMAYvOi.js";import"./info-BYTODGZ9.js";import"./input-CWM2bTJL.js";import"./label-aQpKQY_t.js";import"./TokenSelect-DYUulU6S.js";import"./search-DYwk6_-u.js";import"./arrow-right-gM81zKuh.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pe=K("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),Re=()=>{const[M,c]=a.useState(!1),[j,u]=a.useState(!1),[x,E]=a.useState(null),[m,I]=a.useState(null),[z,S]=a.useState(!1),{toast:g}=ce(),{address:l}=Q(),{tokens:B,isLoading:W}=ee(),{refreshAllData:i}=se(),{createStream:H,pauseStream:O,resumeStream:U,cancelStream:Y,releaseMilestone:q,withdrawFromStream:y,isStreamFullyClaimed:v}=te(),{streams:h,isLoading:p,refresh:V}=ae(),[C,X]=a.useState([]),[N,Z]=a.useState([]);a.useCallback(s=>`${s.slice(0,6)}...${s.slice(-4)}`,[]);const w=a.useCallback(s=>{const r=Number(s.amount),t=s.endTime-s.startTime;if(t<=0)return"0";const n=r/t;return n>=1?`${n.toFixed(2)} ${s.tokenSymbol}/second`:n*60>=1?`${(n*60).toFixed(2)} ${s.tokenSymbol}/minute`:n*3600>=1?`${(n*3600).toFixed(2)} ${s.tokenSymbol}/hour`:`${(n*86400).toFixed(4)} ${s.tokenSymbol}/day`},[]),b=a.useCallback(s=>{switch(s){case o.Active:return"active";case o.Paused:return"paused";case o.Completed:return"completed";case o.Canceled:return"canceled";default:return"active"}},[]);a.useEffect(()=>{if(!h||!l)return;const s=[],r=[];for(const t of h){const n={id:t.id,recipient:t.recipient,sender:t.sender,total:Number(t.amount),streamed:Number(t.streamed),rate:w(t),status:b(t.status),token:t.tokenSymbol,startTime:t.startTime,endTime:t.endTime,isRecipient:(l==null?void 0:l.toLowerCase())===t.recipient.toLowerCase(),isSender:(l==null?void 0:l.toLowerCase())===t.sender.toLowerCase(),milestones:t.milestones.map((f,J)=>({id:`ms-${t.id}-${J}`,percentage:f.percentage,description:f.description,released:f.released}))},G=v(t);t.status===o.Completed||t.status===o.Canceled||G?r.push(n):s.push(n)}X(s),Z(r)},[h,l,w,v,b]);const _=async s=>{try{S(!0),await H(s.recipient,s.tokenType,s.amount,s.durationInSeconds,s.milestonePercentages,s.milestoneDescriptions),g({title:"Stream Created",description:`Successfully started streaming ${s.amount} ${s.tokenType} to ${s.recipient}`}),c(!1),i()}catch(r){console.error("Error creating stream:",r),g({title:"Error Creating Stream",description:r instanceof Error?r.message:"Unknown error",variant:"destructive"})}finally{S(!1)}},k=a.useCallback(async s=>(i(),Promise.resolve()),[i]);return e.jsxs("div",{className:"space-y-6 max-w-3xl mx-auto",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("h2",{className:"text-xl font-semibold",children:"Payment Streams"}),e.jsx(re,{content:e.jsxs("div",{children:[e.jsx("p",{className:"font-medium mb-1",children:"About Payment Streams"}),e.jsx("p",{className:"mb-1",children:"Payment streams allow you to send tokens gradually over time to recipients."}),e.jsxs("ul",{className:"list-disc pl-4 text-xs space-y-1",children:[e.jsx("li",{children:"Tokens are streamed continuously in real-time"}),e.jsx("li",{children:"Recipients can claim tokens at any time"}),e.jsx("li",{children:"Senders can pause, resume, or cancel streams"}),e.jsx("li",{children:"Add milestones to release funds at specific points"})]})]})})]}),e.jsxs("div",{className:"flex w-full sm:w-auto gap-2",children:[e.jsxs(d,{variant:"outline",size:"sm",onClick:V,disabled:p,className:"flex-1 sm:flex-none",children:[e.jsx(pe,{className:`h-4 w-4 mr-1 ${p?"animate-spin":""}`}),e.jsx("span",{children:"Refresh"})]}),e.jsxs(d,{variant:"default",size:"sm",onClick:()=>c(!0),className:"flex-1 sm:flex-none",children:[e.jsx(A,{className:"h-4 w-4 mr-1"}),e.jsx("span",{children:"Create Stream"})]})]})]}),e.jsx("div",{className:"mb-6 bg-secondary/30 p-4 rounded-lg",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(T,{className:"h-5 w-5 text-primary mt-0.5"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-sm",children:"About Payment Streams"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Payment streams allow you to send tokens gradually over time. Recipients can claim tokens as they are streamed. You can pause, resume, or cancel streams at any time."})]})]})}),e.jsxs(me,{defaultValue:"active",className:"space-y-4",children:[e.jsxs(de,{className:"w-full border-b",children:[e.jsx(R,{value:"active",className:"flex-1",children:"Active Streams"}),e.jsx(R,{value:"completed",className:"flex-1",children:"Completed Streams"})]}),e.jsx(L,{value:"active",className:"space-y-4",children:p?e.jsx("div",{className:"flex justify-center items-center h-32",children:e.jsx(P,{size:"lg"})}):C.length===0?e.jsxs("div",{className:"text-center py-12 border border-dashed rounded-lg",children:[e.jsx("div",{className:"flex justify-center mb-3",children:e.jsx(T,{className:"h-10 w-10 text-muted-foreground/50"})}),e.jsx("h3",{className:"text-lg font-medium mb-1",children:"No active streams"}),e.jsx("p",{className:"text-muted-foreground",children:"Create a new payment stream to get started"}),e.jsxs(d,{variant:"outline",size:"sm",className:"mt-4 mx-auto px-4",onClick:()=>c(!0),children:[e.jsx(A,{className:"mr-2 h-4 w-4"}),"Create Stream"]})]}):e.jsx("div",{className:"grid gap-4 md:grid-cols-2",children:C.map(s=>e.jsx(F,{stream:s,onPause:async()=>(await O(s.id),i(),Promise.resolve()),onResume:async()=>(await U(s.id),i(),Promise.resolve()),onCancel:async()=>(await Y(s.id),i(),Promise.resolve()),onReleaseMilestone:(r,t)=>{E(r),I(t),u(!0)},onWithdraw:async()=>(await y(s.id),i(),Promise.resolve()),onStreamComplete:k},s.id))})}),e.jsx(L,{value:"completed",className:"space-y-4",children:p?e.jsx("div",{className:"flex justify-center items-center h-32",children:e.jsx(P,{size:"lg"})}):N.length===0?e.jsxs("div",{className:"text-center py-12 border border-dashed rounded-lg",children:[e.jsx("div",{className:"flex justify-center mb-3",children:e.jsx(xe,{className:"h-10 w-10 text-white"})}),e.jsx("h3",{className:"text-lg font-medium mb-1",children:"No completed streams"}),e.jsx("p",{className:"text-muted-foreground",children:"Completed and canceled streams will appear here"})]}):e.jsx("div",{className:"grid gap-4 md:grid-cols-2",children:N.map(s=>e.jsx(F,{stream:s,onWithdraw:async()=>(await y(s.id),i(),Promise.resolve()),onStreamComplete:k},s.id))})})]}),e.jsx($,{open:M,onOpenChange:c,children:e.jsx(D,{className:"sm:max-w-[95%] w-[95%] p-3 mx-auto rounded-xl md:max-w-lg md:p-4",children:e.jsx(ue,{onCancel:()=>c(!1),onSubmit:_,isCreatingStream:z,tokens:B,isLoadingTokens:W})})}),j&&x&&m&&e.jsx($,{open:j,onOpenChange:u,children:e.jsxs(D,{children:[e.jsxs(ne,{children:[e.jsx(ie,{children:"Release Milestone"}),e.jsxs(le,{children:["Are you sure you want to release this milestone? This will make ",m.percentage,"% of the total stream amount available for the recipient to claim."]})]}),e.jsx("div",{className:"py-2",children:e.jsxs("div",{className:"bg-secondary/30 p-3 rounded-md",children:[e.jsx("p",{className:"text-sm font-medium",children:"Milestone Details"}),e.jsx("p",{className:"text-sm mt-1",children:m.description}),e.jsxs("p",{className:"text-sm mt-2 font-medium",children:["Amount: ",(x.total*m.percentage/100).toFixed(2)," ",x.token]})]})}),e.jsxs(oe,{children:[e.jsx(d,{variant:"outline",onClick:()=>u(!1),children:"Cancel"}),e.jsx(d,{onClick:()=>{q(x.id,Number.parseInt(m.id.split("-")[2],10)).then(()=>{u(!1),i()})},children:"Release Milestone"})]})]})})]})};export{Re as default};
