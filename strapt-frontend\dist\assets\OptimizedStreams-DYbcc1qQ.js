import{r as n,u as Z,a as _,b as ee,S as o,j as e,L as se,B as c,C as T,c as $,D as L,d as R,e as te,f as ae,g as re,h as ne,i as ie}from"./index-G17GlXLb.js";import{T as oe,a as le,b as D,c as E}from"./tabs-CCtap3dh.js";import{C as A,E as F,S as ce}from"./StreamForm-CKUxPQB5.js";import{A as me}from"./arrow-left-D0Dlhjqr.js";import{C as de}from"./circle-check-big-BugZDCTu.js";import"./badge-DogxunLX.js";import"./arrow-down-left-DZ4sL2ld.js";import"./arrow-up-right-D82LWdhe.js";import"./select-uVdWjx2o.js";import"./index-B97upM1f.js";import"./chevron-down-BIMAYvOi.js";import"./info-BYTODGZ9.js";import"./input-CWM2bTJL.js";import"./label-aQpKQY_t.js";import"./TokenSelect-DYUulU6S.js";import"./search-DYwk6_-u.js";import"./arrow-right-gM81zKuh.js";const Le=()=>{const[h,l]=n.useState(!1),[S,m]=n.useState(!1),[g,P]=n.useState(null),[d,M]=n.useState(null),[z,j]=n.useState(!1),{toast:u}=ie(),{address:i}=Z(),{tokens:B,isLoading:O}=_(),{createStream:W,pauseStream:I,resumeStream:H,cancelStream:U,releaseMilestone:V,withdrawFromStream:f,useUserStreams:X,isStreamFullyClaimed:C}=ee(),{streams:x,isLoading:N,refetch:y,updateStreamStatus:v}=X(i),[b,Y]=n.useState([]),[w,q]=n.useState([]);n.useCallback(s=>`${s.slice(0,6)}...${s.slice(-4)}`,[]);const k=n.useCallback(s=>{const a=Number(s.amount),t=s.endTime-s.startTime;if(t<=0)return"0";const r=a/t;return r>=1?`${r.toFixed(2)} ${s.tokenSymbol}/second`:r*60>=1?`${(r*60).toFixed(2)} ${s.tokenSymbol}/minute`:r*3600>=1?`${(r*3600).toFixed(2)} ${s.tokenSymbol}/hour`:`${(r*86400).toFixed(4)} ${s.tokenSymbol}/day`},[]);n.useEffect(()=>{if(!x)return;const s=[],a=[];for(const t of x){const r={id:t.id,recipient:t.recipient,sender:t.sender,total:Number(t.amount),streamed:Number(t.streamed),rate:k(t),status:G(t.status),token:t.tokenSymbol,startTime:t.startTime,endTime:t.endTime,isRecipient:(i==null?void 0:i.toLowerCase())===t.recipient.toLowerCase(),isSender:(i==null?void 0:i.toLowerCase())===t.sender.toLowerCase(),milestones:t.milestones.map((p,Q)=>({id:`ms-${t.id}-${Q}`,percentage:p.percentage,description:p.description,released:p.released}))},K=C(t);t.status===o.Completed||t.status===o.Canceled||K?a.push(r):s.push(r)}Y(s),q(a)},[x,i,k,C]);const G=s=>{switch(s){case o.Active:return"active";case o.Paused:return"paused";case o.Completed:return"completed";case o.Canceled:return"canceled";default:return"active"}},J=async s=>{try{j(!0),await W(s.recipient,s.tokenType,s.amount,s.durationInSeconds,s.milestonePercentages,s.milestoneDescriptions),u({title:"Stream Created",description:`Successfully started streaming ${s.amount} ${s.tokenType} to ${s.recipient}`}),l(!1),y()}catch(a){console.error("Error creating stream:",a),u({title:"Error Creating Stream",description:a instanceof Error?a.message:"An unknown error occurred",variant:"destructive"})}finally{j(!1)}};return e.jsxs("div",{className:"container mx-auto py-6",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx(se,{to:"/app",children:e.jsx(c,{variant:"ghost",size:"icon",className:"h-8 w-8",children:e.jsx(me,{className:"h-4 w-4"})})}),e.jsx("h1",{className:"text-2xl font-bold",children:"Payment Streams"})]}),e.jsxs(c,{onClick:()=>l(!0),children:[e.jsx(A,{className:"mr-2 h-4 w-4"}),"Create Stream"]})]}),e.jsx("div",{className:"mb-6 bg-secondary/30 p-4 rounded-lg",children:e.jsxs("div",{className:"flex items-start gap-3",children:[e.jsx(T,{className:"h-5 w-5 text-primary mt-0.5"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-sm",children:"About Payment Streams"}),e.jsx("p",{className:"text-sm text-muted-foreground",children:"Payment streams allow you to send tokens gradually over time. Recipients can claim tokens as they are streamed. You can pause, resume, or cancel streams at any time."})]})]})}),e.jsxs(oe,{defaultValue:"active",className:"space-y-4",children:[e.jsxs(le,{className:"w-full border-b",children:[e.jsx(D,{value:"active",className:"flex-1",children:"Active Streams"}),e.jsx(D,{value:"completed",className:"flex-1",children:"Completed Streams"})]}),e.jsx(E,{value:"active",className:"space-y-4",children:N?e.jsx("div",{className:"flex justify-center items-center h-32",children:e.jsx($,{size:"lg"})}):b.length===0?e.jsxs("div",{className:"text-center py-12 border border-dashed rounded-lg",children:[e.jsx("div",{className:"flex justify-center mb-3",children:e.jsx(T,{className:"h-10 w-10 text-muted-foreground/50"})}),e.jsx("h3",{className:"text-lg font-medium mb-1",children:"No active streams"}),e.jsx("p",{className:"text-muted-foreground",children:"Create a new payment stream to get started"}),e.jsxs(c,{variant:"outline",className:"mt-4",onClick:()=>l(!0),children:[e.jsx(A,{className:"mr-2 h-4 w-4"}),"Create Stream"]})]}):e.jsx("div",{className:"grid gap-4 md:grid-cols-2",children:b.map(s=>e.jsx(F,{stream:s,onPause:()=>I(s.id),onResume:()=>H(s.id),onCancel:()=>U(s.id),onReleaseMilestone:(a,t)=>{P(a),M(t),m(!0)},onWithdraw:()=>f(s.id),onStreamComplete:v},s.id))})}),e.jsx(E,{value:"completed",className:"space-y-4",children:N?e.jsx("div",{className:"flex justify-center items-center h-32",children:e.jsx($,{size:"lg"})}):w.length===0?e.jsxs("div",{className:"text-center py-12 border border-dashed rounded-lg",children:[e.jsx("div",{className:"flex justify-center mb-3",children:e.jsx(de,{className:"h-10 w-10 text-muted-foreground/50"})}),e.jsx("h3",{className:"text-lg font-medium mb-1",children:"No completed streams"}),e.jsx("p",{className:"text-muted-foreground",children:"Completed and canceled streams will appear here"})]}):e.jsx("div",{className:"grid gap-4 md:grid-cols-2",children:w.map(s=>e.jsx(F,{stream:s,onWithdraw:()=>f(s.id),onStreamComplete:v},s.id))})})]}),h&&e.jsx(L,{open:h,onOpenChange:l,children:e.jsx(R,{className:"max-w-2xl",children:e.jsx(ce,{onCancel:()=>l(!1),onSubmit:J,isCreatingStream:z,tokens:B,isLoadingTokens:O})})}),S&&g&&d&&e.jsx(L,{open:S,onOpenChange:m,children:e.jsxs(R,{children:[e.jsxs(te,{children:[e.jsx(ae,{children:"Release Milestone"}),e.jsx(re,{children:"Are you sure you want to release this milestone? This action cannot be undone."})]}),e.jsxs("div",{className:"py-4",children:[e.jsxs("p",{className:"text-sm text-muted-foreground",children:["Milestone: ",d.description]}),e.jsxs("p",{className:"text-sm text-muted-foreground",children:["Percentage: ",d.percentage,"%"]})]}),e.jsxs(ne,{children:[e.jsx(c,{variant:"outline",onClick:()=>m(!1),children:"Cancel"}),e.jsx(c,{onClick:async()=>{try{await V(g.id,d.id),u({title:"Milestone Released",description:"The milestone has been successfully released"}),m(!1),y()}catch(s){console.error("Error releasing milestone:",s),u({title:"Error Releasing Milestone",description:s instanceof Error?s.message:"An unknown error occurred",variant:"destructive"})}},children:"Release"})]})]})})]})};export{Le as default};
