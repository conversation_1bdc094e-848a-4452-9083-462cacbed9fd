import{r as s,G as R,H as u,j as l,t as x}from"./index-G17GlXLb.js";const j=s.memo(({value:i,size:n=200,className:h="",bgColor:r="#FFF",fgColor:a="#000",renderAsImage:c=!1})=>{const o=s.useRef(null),f=s.useRef(null),d=R(),e=s.useMemo(()=>d?Math.min(n,window.innerWidth-100):n,[d,n]);return s.useEffect(()=>{c?u.toDataURL(i,{width:e,margin:2,color:{dark:a,light:r}},(t,m)=>{if(t){console.error(t);return}f.current&&m&&(f.current.src=m)}):o.current&&u.toCanvas(o.current,i,{width:e,margin:2,color:{dark:a,light:r}},t=>{t&&console.error(t)})},[i,e,r,a,c]),l.jsx("div",{className:x("flex items-center justify-center",h),children:c?l.jsx("img",{ref:f,alt:"QR Code",width:e,height:e,loading:"lazy"}):l.jsx("canvas",{ref:o})})});j.displayName="QRCode";export{j as Q};
