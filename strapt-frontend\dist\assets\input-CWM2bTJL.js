import{r as t,j as i,t as n}from"./index-G17GlXLb.js";const l=t.forwardRef(({className:e,type:r,...o},s)=>i.jsx("input",{type:r,className:n("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...o}));l.displayName="Input";export{l as I};
