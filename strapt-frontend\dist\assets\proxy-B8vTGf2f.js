import{r as P,j as ue}from"./index-G17GlXLb.js";const bs=P.createContext({});function Ji(t){const e=P.useRef(null);return e.current===null&&(e.current=t()),e.current}const Ce=typeof window<"u",Qi=Ce?P.useLayoutEffect:P.useEffect,Re=P.createContext(null),As=P.createContext({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});function tr(t=!0){const e=P.useContext(Re);if(e===null)return[!0,null];const{isPresent:n,onExitComplete:s,register:i}=e,o=P.useId();P.useEffect(()=>{if(t)return i(o)},[t]);const r=P.useCallback(()=>t&&s&&s(o),[o,s,t]);return!n&&s?[!1,r]:[!0]}function Ee(t,e){t.indexOf(e)===-1&&t.push(e)}function Le(t,e){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const z=(t,e,n)=>n>e?e:n<t?t:n;let ke=()=>{};const X={},ws=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),Vs=t=>/^0[^.\s]+$/u.test(t);function Fe(t){let e;return()=>(e===void 0&&(e=t()),e)}const K=t=>t,er=(t,e)=>n=>e(t(n)),Rt=(...t)=>t.reduce(er),bt=(t,e,n)=>{const s=e-t;return s===0?1:(n-t)/s};class Be{constructor(){this.subscriptions=[]}add(e){return Ee(this.subscriptions,e),()=>Le(this.subscriptions,e)}notify(e,n,s){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](e,n,s);else for(let o=0;o<i;o++){const r=this.subscriptions[o];r&&r(e,n,s)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const $=t=>t*1e3,_=t=>t/1e3;function Ms(t,e){return e?t*(1e3/e):0}const Ds=(t,e,n)=>(((1-3*n+3*e)*t+(3*n-6*e))*t+3*e)*t,nr=1e-7,sr=12;function ir(t,e,n,s,i){let o,r,a=0;do r=e+(n-e)/2,o=Ds(r,s,i)-t,o>0?n=r:e=r;while(Math.abs(o)>nr&&++a<sr);return r}function Et(t,e,n,s){if(t===e&&n===s)return K;const i=o=>ir(o,0,1,t,n);return o=>o===0||o===1?o:Ds(i(o),e,s)}const Cs=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,Rs=t=>e=>1-t(1-e),Es=Et(.33,1.53,.69,.99),Oe=Rs(Es),Ls=Cs(Oe),ks=t=>(t*=2)<1?.5*Oe(t):.5*(2-Math.pow(2,-10*(t-1))),Ie=t=>1-Math.sin(Math.acos(t)),Fs=Rs(Ie),Bs=Cs(Ie),rr=Et(.42,0,1,1),or=Et(0,0,.58,1),Os=Et(.42,0,.58,1),ar=t=>Array.isArray(t)&&typeof t[0]!="number",Is=t=>Array.isArray(t)&&typeof t[0]=="number",lr={linear:K,easeIn:rr,easeInOut:Os,easeOut:or,circIn:Ie,circInOut:Bs,circOut:Fs,backIn:Oe,backInOut:Ls,backOut:Es,anticipate:ks},ur=t=>typeof t=="string",cn=t=>{if(Is(t)){ke(t.length===4);const[e,n,s,i]=t;return Et(e,n,s,i)}else if(ur(t))return lr[t];return t},Ft=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],hn={value:null};function cr(t,e){let n=new Set,s=new Set,i=!1,o=!1;const r=new WeakSet;let a={delta:0,timestamp:0,isProcessing:!1},l=0;function c(h){r.has(h)&&(u.schedule(h),t()),l++,h(a)}const u={schedule:(h,f=!1,d=!1)=>{const p=d&&i?n:s;return f&&r.add(h),p.has(h)||p.add(h),h},cancel:h=>{s.delete(h),r.delete(h)},process:h=>{if(a=h,i){o=!0;return}i=!0,[n,s]=[s,n],n.forEach(c),e&&hn.value&&hn.value.frameloop[e].push(l),l=0,n.clear(),i=!1,o&&(o=!1,u.process(h))}};return u}const hr=40;function js(t,e){let n=!1,s=!0;const i={delta:0,timestamp:0,isProcessing:!1},o=()=>n=!0,r=Ft.reduce((y,A)=>(y[A]=cr(o,e?A:void 0),y),{}),{setup:a,read:l,resolveKeyframes:c,preUpdate:u,update:h,preRender:f,render:d,postRender:m}=r,p=()=>{const y=X.useManualTiming?i.timestamp:performance.now();n=!1,X.useManualTiming||(i.delta=s?1e3/60:Math.max(Math.min(y-i.timestamp,hr),1)),i.timestamp=y,i.isProcessing=!0,a.process(i),l.process(i),c.process(i),u.process(i),h.process(i),f.process(i),d.process(i),m.process(i),i.isProcessing=!1,n&&e&&(s=!1,t(p))},v=()=>{n=!0,s=!0,i.isProcessing||t(p)};return{schedule:Ft.reduce((y,A)=>{const x=r[A];return y[A]=(w,R=!1,b=!1)=>(n||v(),x.schedule(w,R,b)),y},{}),cancel:y=>{for(let A=0;A<Ft.length;A++)r[Ft[A]].cancel(y)},state:i,steps:r}}const{schedule:M,cancel:q,state:E,steps:Zt}=js(typeof requestAnimationFrame<"u"?requestAnimationFrame:K,!0);let It;function fr(){It=void 0}const I={now:()=>(It===void 0&&I.set(E.isProcessing||X.useManualTiming?E.timestamp:performance.now()),It),set:t=>{It=t,queueMicrotask(fr)}},Ns=t=>e=>typeof e=="string"&&e.startsWith(t),je=Ns("--"),dr=Ns("var(--"),Ne=t=>dr(t)?mr.test(t.split("/*")[0].trim()):!1,mr=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,dt={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},At={...dt,transform:t=>z(0,1,t)},Bt={...dt,default:1},vt=t=>Math.round(t*1e5)/1e5,Ue=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu;function pr(t){return t==null}const gr=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,Ke=(t,e)=>n=>!!(typeof n=="string"&&gr.test(n)&&n.startsWith(t)||e&&!pr(n)&&Object.prototype.hasOwnProperty.call(n,e)),Us=(t,e,n)=>s=>{if(typeof s!="string")return s;const[i,o,r,a]=s.match(Ue);return{[t]:parseFloat(i),[e]:parseFloat(o),[n]:parseFloat(r),alpha:a!==void 0?parseFloat(a):1}},yr=t=>z(0,255,t),Jt={...dt,transform:t=>Math.round(yr(t))},et={test:Ke("rgb","red"),parse:Us("red","green","blue"),transform:({red:t,green:e,blue:n,alpha:s=1})=>"rgba("+Jt.transform(t)+", "+Jt.transform(e)+", "+Jt.transform(n)+", "+vt(At.transform(s))+")"};function vr(t){let e="",n="",s="",i="";return t.length>5?(e=t.substring(1,3),n=t.substring(3,5),s=t.substring(5,7),i=t.substring(7,9)):(e=t.substring(1,2),n=t.substring(2,3),s=t.substring(3,4),i=t.substring(4,5),e+=e,n+=n,s+=s,i+=i),{red:parseInt(e,16),green:parseInt(n,16),blue:parseInt(s,16),alpha:i?parseInt(i,16)/255:1}}const ce={test:Ke("#"),parse:vr,transform:et.transform},Lt=t=>({test:e=>typeof e=="string"&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),Y=Lt("deg"),G=Lt("%"),T=Lt("px"),Tr=Lt("vh"),xr=Lt("vw"),fn={...G,parse:t=>G.parse(t)/100,transform:t=>G.transform(t*100)},ot={test:Ke("hsl","hue"),parse:Us("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:n,alpha:s=1})=>"hsla("+Math.round(t)+", "+G.transform(vt(e))+", "+G.transform(vt(n))+", "+vt(At.transform(s))+")"},L={test:t=>et.test(t)||ce.test(t)||ot.test(t),parse:t=>et.test(t)?et.parse(t):ot.test(t)?ot.parse(t):ce.parse(t),transform:t=>typeof t=="string"?t:t.hasOwnProperty("red")?et.transform(t):ot.transform(t)},Sr=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;function Pr(t){var e,n;return isNaN(t)&&typeof t=="string"&&(((e=t.match(Ue))==null?void 0:e.length)||0)+(((n=t.match(Sr))==null?void 0:n.length)||0)>0}const Ks="number",Ws="color",br="var",Ar="var(",dn="${}",wr=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function wt(t){const e=t.toString(),n=[],s={color:[],number:[],var:[]},i=[];let o=0;const a=e.replace(wr,l=>(L.test(l)?(s.color.push(o),i.push(Ws),n.push(L.parse(l))):l.startsWith(Ar)?(s.var.push(o),i.push(br),n.push(l)):(s.number.push(o),i.push(Ks),n.push(parseFloat(l))),++o,dn)).split(dn);return{values:n,split:a,indexes:s,types:i}}function $s(t){return wt(t).values}function _s(t){const{split:e,types:n}=wt(t),s=e.length;return i=>{let o="";for(let r=0;r<s;r++)if(o+=e[r],i[r]!==void 0){const a=n[r];a===Ks?o+=vt(i[r]):a===Ws?o+=L.transform(i[r]):o+=i[r]}return o}}const Vr=t=>typeof t=="number"?0:t;function Mr(t){const e=$s(t);return _s(t)(e.map(Vr))}const Z={test:Pr,parse:$s,createTransformer:_s,getAnimatableNone:Mr};function Qt(t,e,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?t+(e-t)*6*n:n<1/2?e:n<2/3?t+(e-t)*(2/3-n)*6:t}function Dr({hue:t,saturation:e,lightness:n,alpha:s}){t/=360,e/=100,n/=100;let i=0,o=0,r=0;if(!e)i=o=r=n;else{const a=n<.5?n*(1+e):n+e-n*e,l=2*n-a;i=Qt(l,a,t+1/3),o=Qt(l,a,t),r=Qt(l,a,t-1/3)}return{red:Math.round(i*255),green:Math.round(o*255),blue:Math.round(r*255),alpha:s}}function Kt(t,e){return n=>n>0?e:t}const V=(t,e,n)=>t+(e-t)*n,te=(t,e,n)=>{const s=t*t,i=n*(e*e-s)+s;return i<0?0:Math.sqrt(i)},Cr=[ce,et,ot],Rr=t=>Cr.find(e=>e.test(t));function mn(t){const e=Rr(t);if(!e)return!1;let n=e.parse(t);return e===ot&&(n=Dr(n)),n}const pn=(t,e)=>{const n=mn(t),s=mn(e);if(!n||!s)return Kt(t,e);const i={...n};return o=>(i.red=te(n.red,s.red,o),i.green=te(n.green,s.green,o),i.blue=te(n.blue,s.blue,o),i.alpha=V(n.alpha,s.alpha,o),et.transform(i))},he=new Set(["none","hidden"]);function Er(t,e){return he.has(t)?n=>n<=0?t:e:n=>n>=1?e:t}function Lr(t,e){return n=>V(t,e,n)}function We(t){return typeof t=="number"?Lr:typeof t=="string"?Ne(t)?Kt:L.test(t)?pn:Br:Array.isArray(t)?Gs:typeof t=="object"?L.test(t)?pn:kr:Kt}function Gs(t,e){const n=[...t],s=n.length,i=t.map((o,r)=>We(o)(o,e[r]));return o=>{for(let r=0;r<s;r++)n[r]=i[r](o);return n}}function kr(t,e){const n={...t,...e},s={};for(const i in n)t[i]!==void 0&&e[i]!==void 0&&(s[i]=We(t[i])(t[i],e[i]));return i=>{for(const o in s)n[o]=s[o](i);return n}}function Fr(t,e){const n=[],s={color:0,var:0,number:0};for(let i=0;i<e.values.length;i++){const o=e.types[i],r=t.indexes[o][s[o]],a=t.values[r]??0;n[i]=a,s[o]++}return n}const Br=(t,e)=>{const n=Z.createTransformer(e),s=wt(t),i=wt(e);return s.indexes.var.length===i.indexes.var.length&&s.indexes.color.length===i.indexes.color.length&&s.indexes.number.length>=i.indexes.number.length?he.has(t)&&!i.values.length||he.has(e)&&!s.values.length?Er(t,e):Rt(Gs(Fr(s,i),i.values),n):Kt(t,e)};function Hs(t,e,n){return typeof t=="number"&&typeof e=="number"&&typeof n=="number"?V(t,e,n):We(t)(t,e)}const Or=t=>{const e=({timestamp:n})=>t(n);return{start:(n=!0)=>M.update(e,n),stop:()=>q(e),now:()=>E.isProcessing?E.timestamp:I.now()}},zs=(t,e,n=10)=>{let s="";const i=Math.max(Math.round(e/n),2);for(let o=0;o<i;o++)s+=t(o/(i-1))+", ";return`linear(${s.substring(0,s.length-2)})`},Wt=2e4;function $e(t){let e=0;const n=50;let s=t.next(e);for(;!s.done&&e<Wt;)e+=n,s=t.next(e);return e>=Wt?1/0:e}function Ir(t,e=100,n){const s=n({...t,keyframes:[0,e]}),i=Math.min($e(s),Wt);return{type:"keyframes",ease:o=>s.next(i*o).value/e,duration:_(i)}}const jr=5;function Xs(t,e,n){const s=Math.max(e-jr,0);return Ms(n-t(s),e-s)}const D={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1},gn=.001;function Nr({duration:t=D.duration,bounce:e=D.bounce,velocity:n=D.velocity,mass:s=D.mass}){let i,o,r=1-e;r=z(D.minDamping,D.maxDamping,r),t=z(D.minDuration,D.maxDuration,_(t)),r<1?(i=c=>{const u=c*r,h=u*t,f=u-n,d=fe(c,r),m=Math.exp(-h);return gn-f/d*m},o=c=>{const h=c*r*t,f=h*n+n,d=Math.pow(r,2)*Math.pow(c,2)*t,m=Math.exp(-h),p=fe(Math.pow(c,2),r);return(-i(c)+gn>0?-1:1)*((f-d)*m)/p}):(i=c=>{const u=Math.exp(-c*t),h=(c-n)*t+1;return-.001+u*h},o=c=>{const u=Math.exp(-c*t),h=(n-c)*(t*t);return u*h});const a=5/t,l=Kr(i,o,a);if(t=$(t),isNaN(l))return{stiffness:D.stiffness,damping:D.damping,duration:t};{const c=Math.pow(l,2)*s;return{stiffness:c,damping:r*2*Math.sqrt(s*c),duration:t}}}const Ur=12;function Kr(t,e,n){let s=n;for(let i=1;i<Ur;i++)s=s-t(s)/e(s);return s}function fe(t,e){return t*Math.sqrt(1-e*e)}const Wr=["duration","bounce"],$r=["stiffness","damping","mass"];function yn(t,e){return e.some(n=>t[n]!==void 0)}function _r(t){let e={velocity:D.velocity,stiffness:D.stiffness,damping:D.damping,mass:D.mass,isResolvedFromDuration:!1,...t};if(!yn(t,$r)&&yn(t,Wr))if(t.visualDuration){const n=t.visualDuration,s=2*Math.PI/(n*1.2),i=s*s,o=2*z(.05,1,1-(t.bounce||0))*Math.sqrt(i);e={...e,mass:D.mass,stiffness:i,damping:o}}else{const n=Nr(t);e={...e,...n,mass:D.mass},e.isResolvedFromDuration=!0}return e}function $t(t=D.visualDuration,e=D.bounce){const n=typeof t!="object"?{visualDuration:t,keyframes:[0,1],bounce:e}:t;let{restSpeed:s,restDelta:i}=n;const o=n.keyframes[0],r=n.keyframes[n.keyframes.length-1],a={done:!1,value:o},{stiffness:l,damping:c,mass:u,duration:h,velocity:f,isResolvedFromDuration:d}=_r({...n,velocity:-_(n.velocity||0)}),m=f||0,p=c/(2*Math.sqrt(l*u)),v=r-o,g=_(Math.sqrt(l/u)),S=Math.abs(v)<5;s||(s=S?D.restSpeed.granular:D.restSpeed.default),i||(i=S?D.restDelta.granular:D.restDelta.default);let y;if(p<1){const x=fe(g,p);y=w=>{const R=Math.exp(-p*g*w);return r-R*((m+p*g*v)/x*Math.sin(x*w)+v*Math.cos(x*w))}}else if(p===1)y=x=>r-Math.exp(-g*x)*(v+(m+g*v)*x);else{const x=g*Math.sqrt(p*p-1);y=w=>{const R=Math.exp(-p*g*w),b=Math.min(x*w,300);return r-R*((m+p*g*v)*Math.sinh(b)+x*v*Math.cosh(b))/x}}const A={calculatedDuration:d&&h||null,next:x=>{const w=y(x);if(d)a.done=x>=h;else{let R=x===0?m:0;p<1&&(R=x===0?$(m):Xs(y,x,w));const b=Math.abs(R)<=s,O=Math.abs(r-w)<=i;a.done=b&&O}return a.value=a.done?r:w,a},toString:()=>{const x=Math.min($e(A),Wt),w=zs(R=>A.next(x*R).value,x,30);return x+"ms "+w},toTransition:()=>{}};return A}$t.applyToOptions=t=>{const e=Ir(t,100,$t);return t.ease=e.ease,t.duration=$(e.duration),t.type="keyframes",t};function de({keyframes:t,velocity:e=0,power:n=.8,timeConstant:s=325,bounceDamping:i=10,bounceStiffness:o=500,modifyTarget:r,min:a,max:l,restDelta:c=.5,restSpeed:u}){const h=t[0],f={done:!1,value:h},d=b=>a!==void 0&&b<a||l!==void 0&&b>l,m=b=>a===void 0?l:l===void 0||Math.abs(a-b)<Math.abs(l-b)?a:l;let p=n*e;const v=h+p,g=r===void 0?v:r(v);g!==v&&(p=g-h);const S=b=>-p*Math.exp(-b/s),y=b=>g+S(b),A=b=>{const O=S(b),j=y(b);f.done=Math.abs(O)<=c,f.value=f.done?g:j};let x,w;const R=b=>{d(f.value)&&(x=b,w=$t({keyframes:[f.value,m(f.value)],velocity:Xs(y,b,f.value),damping:i,stiffness:o,restDelta:c,restSpeed:u}))};return R(0),{calculatedDuration:null,next:b=>{let O=!1;return!w&&x===void 0&&(O=!0,A(b),R(b)),x!==void 0&&b>=x?w.next(b-x):(!O&&A(b),f)}}}function Gr(t,e,n){const s=[],i=n||X.mix||Hs,o=t.length-1;for(let r=0;r<o;r++){let a=i(t[r],t[r+1]);if(e){const l=Array.isArray(e)?e[r]||K:e;a=Rt(l,a)}s.push(a)}return s}function Hr(t,e,{clamp:n=!0,ease:s,mixer:i}={}){const o=t.length;if(ke(o===e.length),o===1)return()=>e[0];if(o===2&&e[0]===e[1])return()=>e[1];const r=t[0]===t[1];t[0]>t[o-1]&&(t=[...t].reverse(),e=[...e].reverse());const a=Gr(e,s,i),l=a.length,c=u=>{if(r&&u<t[0])return e[0];let h=0;if(l>1)for(;h<t.length-2&&!(u<t[h+1]);h++);const f=bt(t[h],t[h+1],u);return a[h](f)};return n?u=>c(z(t[0],t[o-1],u)):c}function zr(t,e){const n=t[t.length-1];for(let s=1;s<=e;s++){const i=bt(0,e,s);t.push(V(n,1,i))}}function Xr(t){const e=[0];return zr(e,t.length-1),e}function Yr(t,e){return t.map(n=>n*e)}function qr(t,e){return t.map(()=>e||Os).splice(0,t.length-1)}function Tt({duration:t=300,keyframes:e,times:n,ease:s="easeInOut"}){const i=ar(s)?s.map(cn):cn(s),o={done:!1,value:e[0]},r=Yr(n&&n.length===e.length?n:Xr(e),t),a=Hr(r,e,{ease:Array.isArray(i)?i:qr(e,i)});return{calculatedDuration:t,next:l=>(o.value=a(l),o.done=l>=t,o)}}const Zr=t=>t!==null;function _e(t,{repeat:e,repeatType:n="loop"},s,i=1){const o=t.filter(Zr),a=i<0||e&&n!=="loop"&&e%2===1?0:o.length-1;return!a||s===void 0?o[a]:s}const Jr={decay:de,inertia:de,tween:Tt,keyframes:Tt,spring:$t};function Ys(t){typeof t.type=="string"&&(t.type=Jr[t.type])}class Ge{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(e=>{this.resolve=e})}notifyFinished(){this.resolve()}then(e,n){return this.finished.then(e,n)}}const Qr=t=>t/100;class He extends Ge{constructor(e){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=(n=!0)=>{var s,i;if(n){const{motionValue:o}=this.options;o&&o.updatedAt!==I.now()&&this.tick(I.now())}this.isStopped=!0,this.state!=="idle"&&(this.teardown(),(i=(s=this.options).onStop)==null||i.call(s))},this.options=e,this.initAnimation(),this.play(),e.autoplay===!1&&this.pause()}initAnimation(){const{options:e}=this;Ys(e);const{type:n=Tt,repeat:s=0,repeatDelay:i=0,repeatType:o,velocity:r=0}=e;let{keyframes:a}=e;const l=n||Tt;l!==Tt&&typeof a[0]!="number"&&(this.mixKeyframes=Rt(Qr,Hs(a[0],a[1])),a=[0,100]);const c=l({...e,keyframes:a});o==="mirror"&&(this.mirroredGenerator=l({...e,keyframes:[...a].reverse(),velocity:-r})),c.calculatedDuration===null&&(c.calculatedDuration=$e(c));const{calculatedDuration:u}=c;this.calculatedDuration=u,this.resolvedDuration=u+i,this.totalDuration=this.resolvedDuration*(s+1)-i,this.generator=c}updateTime(e){const n=Math.round(e-this.startTime)*this.playbackSpeed;this.holdTime!==null?this.currentTime=this.holdTime:this.currentTime=n}tick(e,n=!1){const{generator:s,totalDuration:i,mixKeyframes:o,mirroredGenerator:r,resolvedDuration:a,calculatedDuration:l}=this;if(this.startTime===null)return s.next(0);const{delay:c=0,keyframes:u,repeat:h,repeatType:f,repeatDelay:d,type:m,onUpdate:p,finalKeyframe:v}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-i/this.speed,this.startTime)),n?this.currentTime=e:this.updateTime(e);const g=this.currentTime-c*(this.playbackSpeed>=0?1:-1),S=this.playbackSpeed>=0?g<0:g>i;this.currentTime=Math.max(g,0),this.state==="finished"&&this.holdTime===null&&(this.currentTime=i);let y=this.currentTime,A=s;if(h){const b=Math.min(this.currentTime,i)/a;let O=Math.floor(b),j=b%1;!j&&b>=1&&(j=1),j===1&&O--,O=Math.min(O,h+1),!!(O%2)&&(f==="reverse"?(j=1-j,d&&(j-=d/a)):f==="mirror"&&(A=r)),y=z(0,1,j)*a}const x=S?{done:!1,value:u[0]}:A.next(y);o&&(x.value=o(x.value));let{done:w}=x;!S&&l!==null&&(w=this.playbackSpeed>=0?this.currentTime>=i:this.currentTime<=0);const R=this.holdTime===null&&(this.state==="finished"||this.state==="running"&&w);return R&&m!==de&&(x.value=_e(u,this.options,v,this.speed)),p&&p(x.value),R&&this.finish(),x}then(e,n){return this.finished.then(e,n)}get duration(){return _(this.calculatedDuration)}get time(){return _(this.currentTime)}set time(e){var n;e=$(e),this.currentTime=e,this.startTime===null||this.holdTime!==null||this.playbackSpeed===0?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.playbackSpeed),(n=this.driver)==null||n.start(!1)}get speed(){return this.playbackSpeed}set speed(e){this.updateTime(I.now());const n=this.playbackSpeed!==e;this.playbackSpeed=e,n&&(this.time=_(this.currentTime))}play(){var i,o;if(this.isStopped)return;const{driver:e=Or,startTime:n}=this.options;this.driver||(this.driver=e(r=>this.tick(r))),(o=(i=this.options).onPlay)==null||o.call(i);const s=this.driver.now();this.state==="finished"?(this.updateFinished(),this.startTime=s):this.holdTime!==null?this.startTime=s-this.holdTime:this.startTime||(this.startTime=n??s),this.state==="finished"&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(I.now()),this.holdTime=this.currentTime}complete(){this.state!=="running"&&this.play(),this.state="finished",this.holdTime=null}finish(){var e,n;this.notifyFinished(),this.teardown(),this.state="finished",(n=(e=this.options).onComplete)==null||n.call(e)}cancel(){var e,n;this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),(n=(e=this.options).onCancel)==null||n.call(e)}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}attachTimeline(e){var n;return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),(n=this.driver)==null||n.stop(),e.observe(this)}}function to(t){for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}const nt=t=>t*180/Math.PI,me=t=>{const e=nt(Math.atan2(t[1],t[0]));return pe(e)},eo={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:me,rotateZ:me,skewX:t=>nt(Math.atan(t[1])),skewY:t=>nt(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},pe=t=>(t=t%360,t<0&&(t+=360),t),vn=me,Tn=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),xn=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),no={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:Tn,scaleY:xn,scale:t=>(Tn(t)+xn(t))/2,rotateX:t=>pe(nt(Math.atan2(t[6],t[5]))),rotateY:t=>pe(nt(Math.atan2(-t[2],t[0]))),rotateZ:vn,rotate:vn,skewX:t=>nt(Math.atan(t[4])),skewY:t=>nt(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function Sn(t){return t.includes("scale")?1:0}function ge(t,e){if(!t||t==="none")return Sn(e);const n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);let s,i;if(n)s=no,i=n;else{const a=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);s=eo,i=a}if(!i)return Sn(e);const o=s[e],r=i[1].split(",").map(io);return typeof o=="function"?o(r):r[o]}const so=(t,e)=>{const{transform:n="none"}=getComputedStyle(t);return ge(n,e)};function io(t){return parseFloat(t.trim())}const mt=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],pt=new Set(mt),Pn=t=>t===dt||t===T,ro=new Set(["x","y","z"]),oo=mt.filter(t=>!ro.has(t));function ao(t){const e=[];return oo.forEach(n=>{const s=t.getValue(n);s!==void 0&&(e.push([n,s.get()]),s.set(n.startsWith("scale")?1:0))}),e}const st={width:({x:t},{paddingLeft:e="0",paddingRight:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),height:({y:t},{paddingTop:e="0",paddingBottom:n="0"})=>t.max-t.min-parseFloat(e)-parseFloat(n),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>ge(e,"x"),y:(t,{transform:e})=>ge(e,"y")};st.translateX=st.x;st.translateY=st.y;const it=new Set;let ye=!1,ve=!1,Te=!1;function qs(){if(ve){const t=Array.from(it).filter(s=>s.needsMeasurement),e=new Set(t.map(s=>s.element)),n=new Map;e.forEach(s=>{const i=ao(s);i.length&&(n.set(s,i),s.render())}),t.forEach(s=>s.measureInitialState()),e.forEach(s=>{s.render();const i=n.get(s);i&&i.forEach(([o,r])=>{var a;(a=s.getValue(o))==null||a.set(r)})}),t.forEach(s=>s.measureEndState()),t.forEach(s=>{s.suspendedScrollY!==void 0&&window.scrollTo(0,s.suspendedScrollY)})}ve=!1,ye=!1,it.forEach(t=>t.complete(Te)),it.clear()}function Zs(){it.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(ve=!0)})}function lo(){Te=!0,Zs(),qs(),Te=!1}class ze{constructor(e,n,s,i,o,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...e],this.onComplete=n,this.name=s,this.motionValue=i,this.element=o,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(it.add(this),ye||(ye=!0,M.read(Zs),M.resolveKeyframes(qs))):(this.readKeyframes(),this.complete())}readKeyframes(){const{unresolvedKeyframes:e,name:n,element:s,motionValue:i}=this;if(e[0]===null){const o=i==null?void 0:i.get(),r=e[e.length-1];if(o!==void 0)e[0]=o;else if(s&&n){const a=s.readValue(n,r);a!=null&&(e[0]=a)}e[0]===void 0&&(e[0]=r),i&&o===void 0&&i.set(e[0])}to(e)}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(e=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,e),it.delete(this)}cancel(){this.state==="scheduled"&&(it.delete(this),this.state="pending")}resume(){this.state==="pending"&&this.scheduleResolve()}}const uo=t=>t.startsWith("--");function co(t,e,n){uo(e)?t.style.setProperty(e,n):t.style[e]=n}const ho=Fe(()=>window.ScrollTimeline!==void 0),fo={};function mo(t,e){const n=Fe(t);return()=>fo[e]??n()}const Js=mo(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch{return!1}return!0},"linearEasing"),yt=([t,e,n,s])=>`cubic-bezier(${t}, ${e}, ${n}, ${s})`,bn={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:yt([0,.65,.55,1]),circOut:yt([.55,0,1,.45]),backIn:yt([.31,.01,.66,-.59]),backOut:yt([.33,1.53,.69,.99])};function Qs(t,e){if(t)return typeof t=="function"?Js()?zs(t,e):"ease-out":Is(t)?yt(t):Array.isArray(t)?t.map(n=>Qs(n,e)||bn.easeOut):bn[t]}function po(t,e,n,{delay:s=0,duration:i=300,repeat:o=0,repeatType:r="loop",ease:a="easeOut",times:l}={},c=void 0){const u={[e]:n};l&&(u.offset=l);const h=Qs(a,i);Array.isArray(h)&&(u.easing=h);const f={delay:s,duration:i,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:o+1,direction:r==="reverse"?"alternate":"normal"};return c&&(f.pseudoElement=c),t.animate(u,f)}function ti(t){return typeof t=="function"&&"applyToOptions"in t}function go({type:t,...e}){return ti(t)&&Js()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}class yo extends Ge{constructor(e){if(super(),this.finishedTime=null,this.isStopped=!1,!e)return;const{element:n,name:s,keyframes:i,pseudoElement:o,allowFlatten:r=!1,finalKeyframe:a,onComplete:l}=e;this.isPseudoElement=!!o,this.allowFlatten=r,this.options=e,ke(typeof e.type!="string");const c=go(e);this.animation=po(n,s,i,c,o),c.autoplay===!1&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!o){const u=_e(i,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(u):co(n,s,u),this.animation.cancel()}l==null||l(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),this.state==="finished"&&this.updateFinished())}pause(){this.animation.pause()}complete(){var e,n;(n=(e=this.animation).finish)==null||n.call(e)}cancel(){try{this.animation.cancel()}catch{}}stop(){if(this.isStopped)return;this.isStopped=!0;const{state:e}=this;e==="idle"||e==="finished"||(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){var e,n;this.isPseudoElement||(n=(e=this.animation).commitStyles)==null||n.call(e)}get duration(){var n,s;const e=((s=(n=this.animation.effect)==null?void 0:n.getComputedTiming)==null?void 0:s.call(n).duration)||0;return _(Number(e))}get time(){return _(Number(this.animation.currentTime)||0)}set time(e){this.finishedTime=null,this.animation.currentTime=$(e)}get speed(){return this.animation.playbackRate}set speed(e){e<0&&(this.finishedTime=null),this.animation.playbackRate=e}get state(){return this.finishedTime!==null?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(e){this.animation.startTime=e}attachTimeline({timeline:e,observe:n}){var s;return this.allowFlatten&&((s=this.animation.effect)==null||s.updateTiming({easing:"linear"})),this.animation.onfinish=null,e&&ho()?(this.animation.timeline=e,K):n(this)}}const ei={anticipate:ks,backInOut:Ls,circInOut:Bs};function vo(t){return t in ei}function To(t){typeof t.ease=="string"&&vo(t.ease)&&(t.ease=ei[t.ease])}const An=10;class xo extends yo{constructor(e){To(e),Ys(e),super(e),e.startTime&&(this.startTime=e.startTime),this.options=e}updateMotionValue(e){const{motionValue:n,onUpdate:s,onComplete:i,element:o,...r}=this.options;if(!n)return;if(e!==void 0){n.set(e);return}const a=new He({...r,autoplay:!1}),l=$(this.finishedTime??this.time);n.setWithVelocity(a.sample(l-An).value,a.sample(l).value,An),a.stop()}}const wn=(t,e)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(Z.test(t)||t==="0")&&!t.startsWith("url("));function So(t){const e=t[0];if(t.length===1)return!0;for(let n=0;n<t.length;n++)if(t[n]!==e)return!0}function Po(t,e,n,s){const i=t[0];if(i===null)return!1;if(e==="display"||e==="visibility")return!0;const o=t[t.length-1],r=wn(i,e),a=wn(o,e);return!r||!a?!1:So(t)||(n==="spring"||ti(n))&&s}const bo=new Set(["opacity","clipPath","filter","transform"]),Ao=Fe(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));function wo(t){const{motionValue:e,name:n,repeatDelay:s,repeatType:i,damping:o,type:r}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;const{onUpdate:a,transformTemplate:l}=e.owner.getProps();return Ao()&&n&&bo.has(n)&&(n!=="transform"||!l)&&!a&&!s&&i!=="mirror"&&o!==0&&r!=="inertia"}const Vo=40;class Mo extends Ge{constructor({autoplay:e=!0,delay:n=0,type:s="keyframes",repeat:i=0,repeatDelay:o=0,repeatType:r="loop",keyframes:a,name:l,motionValue:c,element:u,...h}){var m;super(),this.stop=()=>{var p,v;this._animation&&(this._animation.stop(),(p=this.stopTimeline)==null||p.call(this)),(v=this.keyframeResolver)==null||v.cancel()},this.createdAt=I.now();const f={autoplay:e,delay:n,type:s,repeat:i,repeatDelay:o,repeatType:r,name:l,motionValue:c,element:u,...h},d=(u==null?void 0:u.KeyframeResolver)||ze;this.keyframeResolver=new d(a,(p,v,g)=>this.onKeyframesResolved(p,v,f,!g),l,c,u),(m=this.keyframeResolver)==null||m.scheduleResolve()}onKeyframesResolved(e,n,s,i){this.keyframeResolver=void 0;const{name:o,type:r,velocity:a,delay:l,isHandoff:c,onUpdate:u}=s;this.resolvedAt=I.now(),Po(e,o,r,a)||((X.instantAnimations||!l)&&(u==null||u(_e(e,s,n))),e[0]=e[e.length-1],s.duration=0,s.repeat=0);const f={startTime:i?this.resolvedAt?this.resolvedAt-this.createdAt>Vo?this.resolvedAt:this.createdAt:this.createdAt:void 0,finalKeyframe:n,...s,keyframes:e},d=!c&&wo(f)?new xo({...f,element:f.motionValue.owner.current}):new He(f);d.finished.then(()=>this.notifyFinished()).catch(K),this.pendingTimeline&&(this.stopTimeline=d.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=d}get finished(){return this._animation?this.animation.finished:this._finished}then(e,n){return this.finished.finally(e).then(()=>{})}get animation(){var e;return this._animation||((e=this.keyframeResolver)==null||e.resume(),lo()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(e){this.animation.time=e}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(e){this.animation.speed=e}get startTime(){return this.animation.startTime}attachTimeline(e){return this._animation?this.stopTimeline=this.animation.attachTimeline(e):this.pendingTimeline=e,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){var e;this._animation&&this.animation.cancel(),(e=this.keyframeResolver)==null||e.cancel()}}const Do=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;function Co(t){const e=Do.exec(t);if(!e)return[,];const[,n,s,i]=e;return[`--${n??s}`,i]}function ni(t,e,n=1){const[s,i]=Co(t);if(!s)return;const o=window.getComputedStyle(e).getPropertyValue(s);if(o){const r=o.trim();return ws(r)?parseFloat(r):r}return Ne(i)?ni(i,e,n+1):i}function Xe(t,e){return(t==null?void 0:t[e])??(t==null?void 0:t.default)??t}const si=new Set(["width","height","top","left","right","bottom",...mt]),Ro={test:t=>t==="auto",parse:t=>t},ii=t=>e=>e.test(t),ri=[dt,T,G,Y,xr,Tr,Ro],Vn=t=>ri.find(ii(t));function Eo(t){return typeof t=="number"?t===0:t!==null?t==="none"||t==="0"||Vs(t):!0}const Lo=new Set(["brightness","contrast","saturate","opacity"]);function ko(t){const[e,n]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;const[s]=n.match(Ue)||[];if(!s)return t;const i=n.replace(s,"");let o=Lo.has(e)?1:0;return s!==n&&(o*=100),e+"("+o+i+")"}const Fo=/\b([a-z-]*)\(.*?\)/gu,xe={...Z,getAnimatableNone:t=>{const e=t.match(Fo);return e?e.map(ko).join(" "):t}},Mn={...dt,transform:Math.round},Bo={rotate:Y,rotateX:Y,rotateY:Y,rotateZ:Y,scale:Bt,scaleX:Bt,scaleY:Bt,scaleZ:Bt,skew:Y,skewX:Y,skewY:Y,distance:T,translateX:T,translateY:T,translateZ:T,x:T,y:T,z:T,perspective:T,transformPerspective:T,opacity:At,originX:fn,originY:fn,originZ:T},Ye={borderWidth:T,borderTopWidth:T,borderRightWidth:T,borderBottomWidth:T,borderLeftWidth:T,borderRadius:T,radius:T,borderTopLeftRadius:T,borderTopRightRadius:T,borderBottomRightRadius:T,borderBottomLeftRadius:T,width:T,maxWidth:T,height:T,maxHeight:T,top:T,right:T,bottom:T,left:T,padding:T,paddingTop:T,paddingRight:T,paddingBottom:T,paddingLeft:T,margin:T,marginTop:T,marginRight:T,marginBottom:T,marginLeft:T,backgroundPositionX:T,backgroundPositionY:T,...Bo,zIndex:Mn,fillOpacity:At,strokeOpacity:At,numOctaves:Mn},Oo={...Ye,color:L,backgroundColor:L,outlineColor:L,fill:L,stroke:L,borderColor:L,borderTopColor:L,borderRightColor:L,borderBottomColor:L,borderLeftColor:L,filter:xe,WebkitFilter:xe},oi=t=>Oo[t];function ai(t,e){let n=oi(t);return n!==xe&&(n=Z),n.getAnimatableNone?n.getAnimatableNone(e):void 0}const Io=new Set(["auto","none","0"]);function jo(t,e,n){let s=0,i;for(;s<t.length&&!i;){const o=t[s];typeof o=="string"&&!Io.has(o)&&wt(o).values.length&&(i=t[s]),s++}if(i&&n)for(const o of e)t[o]=ai(n,i)}class No extends ze{constructor(e,n,s,i,o){super(e,n,s,i,o,!0)}readKeyframes(){const{unresolvedKeyframes:e,element:n,name:s}=this;if(!n||!n.current)return;super.readKeyframes();for(let l=0;l<e.length;l++){let c=e[l];if(typeof c=="string"&&(c=c.trim(),Ne(c))){const u=ni(c,n.current);u!==void 0&&(e[l]=u),l===e.length-1&&(this.finalKeyframe=c)}}if(this.resolveNoneKeyframes(),!si.has(s)||e.length!==2)return;const[i,o]=e,r=Vn(i),a=Vn(o);if(r!==a)if(Pn(r)&&Pn(a))for(let l=0;l<e.length;l++){const c=e[l];typeof c=="string"&&(e[l]=parseFloat(c))}else st[s]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){const{unresolvedKeyframes:e,name:n}=this,s=[];for(let i=0;i<e.length;i++)(e[i]===null||Eo(e[i]))&&s.push(i);s.length&&jo(e,s,n)}measureInitialState(){const{element:e,unresolvedKeyframes:n,name:s}=this;if(!e||!e.current)return;s==="height"&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=st[s](e.measureViewportBox(),window.getComputedStyle(e.current)),n[0]=this.measuredOrigin;const i=n[n.length-1];i!==void 0&&e.getValue(s,i).jump(i,!1)}measureEndState(){var a;const{element:e,name:n,unresolvedKeyframes:s}=this;if(!e||!e.current)return;const i=e.getValue(n);i&&i.jump(this.measuredOrigin,!1);const o=s.length-1,r=s[o];s[o]=st[n](e.measureViewportBox(),window.getComputedStyle(e.current)),r!==null&&this.finalKeyframe===void 0&&(this.finalKeyframe=r),(a=this.removedTransforms)!=null&&a.length&&this.removedTransforms.forEach(([l,c])=>{e.getValue(l).set(c)}),this.resolveNoneKeyframes()}}function Uo(t,e,n){if(t instanceof EventTarget)return[t];if(typeof t=="string"){let s=document;const i=(n==null?void 0:n[t])??s.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}const Dn=30,Ko=t=>!isNaN(parseFloat(t));class Wo{constructor(e,n={}){this.version="__VERSION__",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(s,i=!0)=>{var r,a;const o=I.now();if(this.updatedAt!==o&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(s),this.current!==this.prev&&((r=this.events.change)==null||r.notify(this.current),this.dependents))for(const l of this.dependents)l.dirty();i&&((a=this.events.renderRequest)==null||a.notify(this.current))},this.hasAnimated=!1,this.setCurrent(e),this.owner=n.owner}setCurrent(e){this.current=e,this.updatedAt=I.now(),this.canTrackVelocity===null&&e!==void 0&&(this.canTrackVelocity=Ko(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,n){this.events[e]||(this.events[e]=new Be);const s=this.events[e].add(n);return e==="change"?()=>{s(),M.read(()=>{this.events.change.getSize()||this.stop()})}:s}clearListeners(){for(const e in this.events)this.events[e].clear()}attach(e,n){this.passiveEffect=e,this.stopPassiveEffect=n}set(e,n=!0){!n||!this.passiveEffect?this.updateAndNotify(e,n):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,n,s){this.set(n),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-s}jump(e,n=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,n&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){var e;(e=this.events.change)==null||e.notify(this.current)}addDependent(e){this.dependents||(this.dependents=new Set),this.dependents.add(e)}removeDependent(e){this.dependents&&this.dependents.delete(e)}get(){return this.current}getPrevious(){return this.prev}getVelocity(){const e=I.now();if(!this.canTrackVelocity||this.prevFrameValue===void 0||e-this.updatedAt>Dn)return 0;const n=Math.min(this.updatedAt-this.prevUpdatedAt,Dn);return Ms(parseFloat(this.current)-parseFloat(this.prevFrameValue),n)}start(e){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=e(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){var e,n;(e=this.dependents)==null||e.clear(),(n=this.events.destroy)==null||n.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function ht(t,e){return new Wo(t,e)}const li=(t,e)=>e&&typeof t=="number"?e.transform(t):t,{schedule:qe}=js(queueMicrotask,!1),W={x:!1,y:!1};function ui(){return W.x||W.y}function $o(t){return t==="x"||t==="y"?W[t]?null:(W[t]=!0,()=>{W[t]=!1}):W.x||W.y?null:(W.x=W.y=!0,()=>{W.x=W.y=!1})}function ci(t,e){const n=Uo(t),s=new AbortController,i={passive:!0,...e,signal:s.signal};return[n,i,()=>s.abort()]}function Cn(t){return!(t.pointerType==="touch"||ui())}function _o(t,e,n={}){const[s,i,o]=ci(t,n),r=a=>{if(!Cn(a))return;const{target:l}=a,c=e(l,a);if(typeof c!="function"||!l)return;const u=h=>{Cn(h)&&(c(h),l.removeEventListener("pointerleave",u))};l.addEventListener("pointerleave",u,i)};return s.forEach(a=>{a.addEventListener("pointerenter",r,i)}),o}const hi=(t,e)=>e?t===e?!0:hi(t,e.parentElement):!1,Ze=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1,Go=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]);function Ho(t){return Go.has(t.tagName)||t.tabIndex!==-1}const jt=new WeakSet;function Rn(t){return e=>{e.key==="Enter"&&t(e)}}function ee(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}const zo=(t,e)=>{const n=t.currentTarget;if(!n)return;const s=Rn(()=>{if(jt.has(n))return;ee(n,"down");const i=Rn(()=>{ee(n,"up")}),o=()=>ee(n,"cancel");n.addEventListener("keyup",i,e),n.addEventListener("blur",o,e)});n.addEventListener("keydown",s,e),n.addEventListener("blur",()=>n.removeEventListener("keydown",s),e)};function En(t){return Ze(t)&&!ui()}function Xo(t,e,n={}){const[s,i,o]=ci(t,n),r=a=>{const l=a.currentTarget;if(!En(a))return;jt.add(l);const c=e(l,a),u=(d,m)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",f),jt.has(l)&&jt.delete(l),En(d)&&typeof c=="function"&&c(d,{success:m})},h=d=>{u(d,l===window||l===document||n.useGlobalTarget||hi(l,d.target))},f=d=>{u(d,!1)};window.addEventListener("pointerup",h,i),window.addEventListener("pointercancel",f,i)};return s.forEach(a=>{(n.useGlobalTarget?window:a).addEventListener("pointerdown",r,i),a instanceof HTMLElement&&(a.addEventListener("focus",c=>zo(c,i)),!Ho(a)&&!a.hasAttribute("tabindex")&&(a.tabIndex=0))}),o}const Yo=[...ri,L,Z],qo=t=>Yo.find(ii(t)),fi=P.createContext({strict:!1}),Ln={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},ft={};for(const t in Ln)ft[t]={isEnabled:e=>Ln[t].some(n=>!!e[n])};function Zo(t){for(const e in t)ft[e]={...ft[e],...t[e]}}const Jo=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function _t(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||Jo.has(t)}let di=t=>!_t(t);function Qo(t){t&&(di=e=>e.startsWith("on")?!_t(e):t(e))}try{Qo(require("@emotion/is-prop-valid").default)}catch{}function ta(t,e,n){const s={};for(const i in t)i==="values"&&typeof t.values=="object"||(di(i)||n===!0&&_t(i)||!e&&!_t(i)||t.draggable&&i.startsWith("onDrag"))&&(s[i]=t[i]);return s}function ea(t){if(typeof Proxy>"u")return t;const e=new Map,n=(...s)=>t(...s);return new Proxy(n,{get:(s,i)=>i==="create"?t:(e.has(i)||e.set(i,t(i)),e.get(i))})}const Ht=P.createContext({});function zt(t){return t!==null&&typeof t=="object"&&typeof t.start=="function"}function Vt(t){return typeof t=="string"||Array.isArray(t)}const Je=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Qe=["initial",...Je];function Xt(t){return zt(t.animate)||Qe.some(e=>Vt(t[e]))}function mi(t){return!!(Xt(t)||t.variants)}function na(t,e){if(Xt(t)){const{initial:n,animate:s}=t;return{initial:n===!1||Vt(n)?n:void 0,animate:Vt(s)?s:void 0}}return t.inherit!==!1?e:{}}function sa(t){const{initial:e,animate:n}=na(t,P.useContext(Ht));return P.useMemo(()=>({initial:e,animate:n}),[kn(e),kn(n)])}function kn(t){return Array.isArray(t)?t.join(" "):t}const ia=Symbol.for("motionComponentSymbol");function at(t){return t&&typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function ra(t,e,n){return P.useCallback(s=>{s&&t.onMount&&t.onMount(s),e&&(s?e.mount(s):e.unmount()),n&&(typeof n=="function"?n(s):at(n)&&(n.current=s))},[e])}const tn=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),oa="framerAppearId",pi="data-"+tn(oa),gi=P.createContext({});function aa(t,e,n,s,i){var p,v;const{visualElement:o}=P.useContext(Ht),r=P.useContext(fi),a=P.useContext(Re),l=P.useContext(As).reducedMotion,c=P.useRef(null);s=s||r.renderer,!c.current&&s&&(c.current=s(t,{visualState:e,parent:o,props:n,presenceContext:a,blockInitialAnimation:a?a.initial===!1:!1,reducedMotionConfig:l}));const u=c.current,h=P.useContext(gi);u&&!u.projection&&i&&(u.type==="html"||u.type==="svg")&&la(c.current,n,i,h);const f=P.useRef(!1);P.useInsertionEffect(()=>{u&&f.current&&u.update(n,a)});const d=n[pi],m=P.useRef(!!d&&!((p=window.MotionHandoffIsComplete)!=null&&p.call(window,d))&&((v=window.MotionHasOptimisedAnimation)==null?void 0:v.call(window,d)));return Qi(()=>{u&&(f.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),qe.render(u.render),m.current&&u.animationState&&u.animationState.animateChanges())}),P.useEffect(()=>{u&&(!m.current&&u.animationState&&u.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{var g;(g=window.MotionHandoffMarkAsComplete)==null||g.call(window,d)}),m.current=!1))}),u}function la(t,e,n,s){const{layoutId:i,layout:o,drag:r,dragConstraints:a,layoutScroll:l,layoutRoot:c,layoutCrossfade:u}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:yi(t.parent)),t.projection.setOptions({layoutId:i,layout:o,alwaysMeasureLayout:!!r||a&&at(a),visualElement:t,animationType:typeof o=="string"?o:"both",initialPromotionConfig:s,crossfade:u,layoutScroll:l,layoutRoot:c})}function yi(t){if(t)return t.options.allowProjection!==!1?t.projection:yi(t.parent)}function ua({preloadedFeatures:t,createVisualElement:e,useRender:n,useVisualState:s,Component:i}){t&&Zo(t);function o(a,l){let c;const u={...P.useContext(As),...a,layoutId:ca(a)},{isStatic:h}=u,f=sa(a),d=s(a,h);if(!h&&Ce){ha();const m=fa(u);c=m.MeasureLayout,f.visualElement=aa(i,d,u,e,m.ProjectionNode)}return ue.jsxs(Ht.Provider,{value:f,children:[c&&f.visualElement?ue.jsx(c,{visualElement:f.visualElement,...u}):null,n(i,a,ra(d,f.visualElement,l),d,h,f.visualElement)]})}o.displayName=`motion.${typeof i=="string"?i:`create(${i.displayName??i.name??""})`}`;const r=P.forwardRef(o);return r[ia]=i,r}function ca({layoutId:t}){const e=P.useContext(bs).id;return e&&t!==void 0?e+"-"+t:t}function ha(t,e){P.useContext(fi).strict}function fa(t){const{drag:e,layout:n}=ft;if(!e&&!n)return{};const s={...e,...n};return{MeasureLayout:e!=null&&e.isEnabled(t)||n!=null&&n.isEnabled(t)?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}const Mt={};function da(t){for(const e in t)Mt[e]=t[e],je(e)&&(Mt[e].isCSSVariable=!0)}function vi(t,{layout:e,layoutId:n}){return pt.has(t)||t.startsWith("origin")||(e||n!==void 0)&&(!!Mt[t]||t==="opacity")}const k=t=>!!(t&&t.getVelocity),ma={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},pa=mt.length;function ga(t,e,n){let s="",i=!0;for(let o=0;o<pa;o++){const r=mt[o],a=t[r];if(a===void 0)continue;let l=!0;if(typeof a=="number"?l=a===(r.startsWith("scale")?1:0):l=parseFloat(a)===0,!l||n){const c=li(a,Ye[r]);if(!l){i=!1;const u=ma[r]||r;s+=`${u}(${c}) `}n&&(e[r]=c)}}return s=s.trim(),n?s=n(e,i?"":s):i&&(s="none"),s}function en(t,e,n){const{style:s,vars:i,transformOrigin:o}=t;let r=!1,a=!1;for(const l in e){const c=e[l];if(pt.has(l)){r=!0;continue}else if(je(l)){i[l]=c;continue}else{const u=li(c,Ye[l]);l.startsWith("origin")?(a=!0,o[l]=u):s[l]=u}}if(e.transform||(r||n?s.transform=ga(e,t.transform,n):s.transform&&(s.transform="none")),a){const{originX:l="50%",originY:c="50%",originZ:u=0}=o;s.transformOrigin=`${l} ${c} ${u}`}}const nn=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Ti(t,e,n){for(const s in e)!k(e[s])&&!vi(s,n)&&(t[s]=e[s])}function ya({transformTemplate:t},e){return P.useMemo(()=>{const n=nn();return en(n,e,t),Object.assign({},n.vars,n.style)},[e])}function va(t,e){const n=t.style||{},s={};return Ti(s,n,t),Object.assign(s,ya(t,e)),s}function Ta(t,e){const n={},s=va(t,e);return t.drag&&t.dragListener!==!1&&(n.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=s,n}const xa=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function sn(t){return typeof t!="string"||t.includes("-")?!1:!!(xa.indexOf(t)>-1||/[A-Z]/u.test(t))}const Sa={offset:"stroke-dashoffset",array:"stroke-dasharray"},Pa={offset:"strokeDashoffset",array:"strokeDasharray"};function ba(t,e,n=1,s=0,i=!0){t.pathLength=1;const o=i?Sa:Pa;t[o.offset]=T.transform(-s);const r=T.transform(e),a=T.transform(n);t[o.array]=`${r} ${a}`}function xi(t,{attrX:e,attrY:n,attrScale:s,pathLength:i,pathSpacing:o=1,pathOffset:r=0,...a},l,c,u){if(en(t,a,c),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};const{attrs:h,style:f}=t;h.transform&&(f.transform=h.transform,delete h.transform),(f.transform||h.transformOrigin)&&(f.transformOrigin=h.transformOrigin??"50% 50%",delete h.transformOrigin),f.transform&&(f.transformBox=(u==null?void 0:u.transformBox)??"fill-box",delete h.transformBox),e!==void 0&&(h.x=e),n!==void 0&&(h.y=n),s!==void 0&&(h.scale=s),i!==void 0&&ba(h,i,o,r,!1)}const Si=()=>({...nn(),attrs:{}}),Pi=t=>typeof t=="string"&&t.toLowerCase()==="svg";function Aa(t,e,n,s){const i=P.useMemo(()=>{const o=Si();return xi(o,e,Pi(s),t.transformTemplate,t.style),{...o.attrs,style:{...o.style}}},[e]);if(t.style){const o={};Ti(o,t.style,t),i.style={...o,...i.style}}return i}function wa(t=!1){return(n,s,i,{latestValues:o},r)=>{const l=(sn(n)?Aa:Ta)(s,o,r,n),c=ta(s,typeof n=="string",t),u=n!==P.Fragment?{...c,...l,ref:i}:{},{children:h}=s,f=P.useMemo(()=>k(h)?h.get():h,[h]);return P.createElement(n,{...u,children:f})}}function Fn(t){const e=[{},{}];return t==null||t.values.forEach((n,s)=>{e[0][s]=n.get(),e[1][s]=n.getVelocity()}),e}function rn(t,e,n,s){if(typeof e=="function"){const[i,o]=Fn(s);e=e(n!==void 0?n:t.custom,i,o)}if(typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"){const[i,o]=Fn(s);e=e(n!==void 0?n:t.custom,i,o)}return e}function Nt(t){return k(t)?t.get():t}function Va({scrapeMotionValuesFromProps:t,createRenderState:e},n,s,i){return{latestValues:Ma(n,s,i,t),renderState:e()}}const bi=t=>(e,n)=>{const s=P.useContext(Ht),i=P.useContext(Re),o=()=>Va(t,e,s,i);return n?o():Ji(o)};function Ma(t,e,n,s){const i={},o=s(t,{});for(const f in o)i[f]=Nt(o[f]);let{initial:r,animate:a}=t;const l=Xt(t),c=mi(t);e&&c&&!l&&t.inherit!==!1&&(r===void 0&&(r=e.initial),a===void 0&&(a=e.animate));let u=n?n.initial===!1:!1;u=u||r===!1;const h=u?a:r;if(h&&typeof h!="boolean"&&!zt(h)){const f=Array.isArray(h)?h:[h];for(let d=0;d<f.length;d++){const m=rn(t,f[d]);if(m){const{transitionEnd:p,transition:v,...g}=m;for(const S in g){let y=g[S];if(Array.isArray(y)){const A=u?y.length-1:0;y=y[A]}y!==null&&(i[S]=y)}for(const S in p)i[S]=p[S]}}}return i}function on(t,e,n){var o;const{style:s}=t,i={};for(const r in s)(k(s[r])||e.style&&k(e.style[r])||vi(r,t)||((o=n==null?void 0:n.getValue(r))==null?void 0:o.liveStyle)!==void 0)&&(i[r]=s[r]);return i}const Da={useVisualState:bi({scrapeMotionValuesFromProps:on,createRenderState:nn})};function Ai(t,e,n){const s=on(t,e,n);for(const i in t)if(k(t[i])||k(e[i])){const o=mt.indexOf(i)!==-1?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i;s[o]=t[i]}return s}const Ca={useVisualState:bi({scrapeMotionValuesFromProps:Ai,createRenderState:Si})};function Ra(t,e){return function(s,{forwardMotionProps:i}={forwardMotionProps:!1}){const r={...sn(s)?Ca:Da,preloadedFeatures:t,useRender:wa(i),createVisualElement:e,Component:s};return ua(r)}}function Dt(t,e,n){const s=t.getProps();return rn(s,e,n!==void 0?n:s.custom,t)}const Se=t=>Array.isArray(t);function Ea(t,e,n){t.hasValue(e)?t.getValue(e).set(n):t.addValue(e,ht(n))}function La(t){return Se(t)?t[t.length-1]||0:t}function ka(t,e){const n=Dt(t,e);let{transitionEnd:s={},transition:i={},...o}=n||{};o={...o,...s};for(const r in o){const a=La(o[r]);Ea(t,r,a)}}function Fa(t){return!!(k(t)&&t.add)}function Pe(t,e){const n=t.getValue("willChange");if(Fa(n))return n.add(e);if(!n&&X.WillChange){const s=new X.WillChange("auto");t.addValue("willChange",s),s.add(e)}}function wi(t){return t.props[pi]}const Ba=t=>t!==null;function Oa(t,{repeat:e,repeatType:n="loop"},s){const i=t.filter(Ba),o=e&&n!=="loop"&&e%2===1?0:i.length-1;return i[o]}const Ia={type:"spring",stiffness:500,damping:25,restSpeed:10},ja=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),Na={type:"keyframes",duration:.8},Ua={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Ka=(t,{keyframes:e})=>e.length>2?Na:pt.has(t)?t.startsWith("scale")?ja(e[1]):Ia:Ua;function Wa({when:t,delay:e,delayChildren:n,staggerChildren:s,staggerDirection:i,repeat:o,repeatType:r,repeatDelay:a,from:l,elapsed:c,...u}){return!!Object.keys(u).length}const an=(t,e,n,s={},i,o)=>r=>{const a=Xe(s,t)||{},l=a.delay||s.delay||0;let{elapsed:c=0}=s;c=c-$(l);const u={keyframes:Array.isArray(n)?n:[null,n],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-c,onUpdate:f=>{e.set(f),a.onUpdate&&a.onUpdate(f)},onComplete:()=>{r(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:o?void 0:i};Wa(a)||Object.assign(u,Ka(t,u)),u.duration&&(u.duration=$(u.duration)),u.repeatDelay&&(u.repeatDelay=$(u.repeatDelay)),u.from!==void 0&&(u.keyframes[0]=u.from);let h=!1;if((u.type===!1||u.duration===0&&!u.repeatDelay)&&(u.duration=0,u.delay===0&&(h=!0)),(X.instantAnimations||X.skipAnimations)&&(h=!0,u.duration=0,u.delay=0),u.allowFlatten=!a.type&&!a.ease,h&&!o&&e.get()!==void 0){const f=Oa(u.keyframes,a);if(f!==void 0){M.update(()=>{u.onUpdate(f),u.onComplete()});return}}return a.isSync?new He(u):new Mo(u)};function $a({protectedKeys:t,needsAnimating:e},n){const s=t.hasOwnProperty(n)&&e[n]!==!0;return e[n]=!1,s}function Vi(t,e,{delay:n=0,transitionOverride:s,type:i}={}){let{transition:o=t.getDefaultTransition(),transitionEnd:r,...a}=e;s&&(o=s);const l=[],c=i&&t.animationState&&t.animationState.getState()[i];for(const u in a){const h=t.getValue(u,t.latestValues[u]??null),f=a[u];if(f===void 0||c&&$a(c,u))continue;const d={delay:n,...Xe(o||{},u)},m=h.get();if(m!==void 0&&!h.isAnimating&&!Array.isArray(f)&&f===m&&!d.velocity)continue;let p=!1;if(window.MotionHandoffAnimation){const g=wi(t);if(g){const S=window.MotionHandoffAnimation(g,u,M);S!==null&&(d.startTime=S,p=!0)}}Pe(t,u),h.start(an(u,h,f,t.shouldReduceMotion&&si.has(u)?{type:!1}:d,t,p));const v=h.animation;v&&l.push(v)}return r&&Promise.all(l).then(()=>{M.update(()=>{r&&ka(t,r)})}),l}function be(t,e,n={}){var l;const s=Dt(t,e,n.type==="exit"?(l=t.presenceContext)==null?void 0:l.custom:void 0);let{transition:i=t.getDefaultTransition()||{}}=s||{};n.transitionOverride&&(i=n.transitionOverride);const o=s?()=>Promise.all(Vi(t,s,n)):()=>Promise.resolve(),r=t.variantChildren&&t.variantChildren.size?(c=0)=>{const{delayChildren:u=0,staggerChildren:h,staggerDirection:f}=i;return _a(t,e,u+c,h,f,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[c,u]=a==="beforeChildren"?[o,r]:[r,o];return c().then(()=>u())}else return Promise.all([o(),r(n.delay)])}function _a(t,e,n=0,s=0,i=1,o){const r=[],a=(t.variantChildren.size-1)*s,l=i===1?(c=0)=>c*s:(c=0)=>a-c*s;return Array.from(t.variantChildren).sort(Ga).forEach((c,u)=>{c.notify("AnimationStart",e),r.push(be(c,e,{...o,delay:n+l(u)}).then(()=>c.notify("AnimationComplete",e)))}),Promise.all(r)}function Ga(t,e){return t.sortNodePosition(e)}function Ha(t,e,n={}){t.notify("AnimationStart",e);let s;if(Array.isArray(e)){const i=e.map(o=>be(t,o,n));s=Promise.all(i)}else if(typeof e=="string")s=be(t,e,n);else{const i=typeof e=="function"?Dt(t,e,n.custom):e;s=Promise.all(Vi(t,i,n))}return s.then(()=>{t.notify("AnimationComplete",e)})}function Mi(t,e){if(!Array.isArray(e))return!1;const n=e.length;if(n!==t.length)return!1;for(let s=0;s<n;s++)if(e[s]!==t[s])return!1;return!0}const za=Qe.length;function Di(t){if(!t)return;if(!t.isControllingVariants){const n=t.parent?Di(t.parent)||{}:{};return t.props.initial!==void 0&&(n.initial=t.props.initial),n}const e={};for(let n=0;n<za;n++){const s=Qe[n],i=t.props[s];(Vt(i)||i===!1)&&(e[s]=i)}return e}const Xa=[...Je].reverse(),Ya=Je.length;function qa(t){return e=>Promise.all(e.map(({animation:n,options:s})=>Ha(t,n,s)))}function Za(t){let e=qa(t),n=Bn(),s=!0;const i=l=>(c,u)=>{var f;const h=Dt(t,u,l==="exit"?(f=t.presenceContext)==null?void 0:f.custom:void 0);if(h){const{transition:d,transitionEnd:m,...p}=h;c={...c,...p,...m}}return c};function o(l){e=l(t)}function r(l){const{props:c}=t,u=Di(t.parent)||{},h=[],f=new Set;let d={},m=1/0;for(let v=0;v<Ya;v++){const g=Xa[v],S=n[g],y=c[g]!==void 0?c[g]:u[g],A=Vt(y),x=g===l?S.isActive:null;x===!1&&(m=v);let w=y===u[g]&&y!==c[g]&&A;if(w&&s&&t.manuallyAnimateOnMount&&(w=!1),S.protectedKeys={...d},!S.isActive&&x===null||!y&&!S.prevProp||zt(y)||typeof y=="boolean")continue;const R=Ja(S.prevProp,y);let b=R||g===l&&S.isActive&&!w&&A||v>m&&A,O=!1;const j=Array.isArray(y)?y:[y];let rt=j.reduce(i(g),{});x===!1&&(rt={});const{prevResolvedValues:ln={}}=S,Zi={...ln,...rt},un=F=>{b=!0,f.has(F)&&(O=!0,f.delete(F)),S.needsAnimating[F]=!0;const H=t.getValue(F);H&&(H.liveStyle=!1)};for(const F in Zi){const H=rt[F],Yt=ln[F];if(d.hasOwnProperty(F))continue;let qt=!1;Se(H)&&Se(Yt)?qt=!Mi(H,Yt):qt=H!==Yt,qt?H!=null?un(F):f.add(F):H!==void 0&&f.has(F)?un(F):S.protectedKeys[F]=!0}S.prevProp=y,S.prevResolvedValues=rt,S.isActive&&(d={...d,...rt}),s&&t.blockInitialAnimation&&(b=!1),b&&(!(w&&R)||O)&&h.push(...j.map(F=>({animation:F,options:{type:g}})))}if(f.size){const v={};if(typeof c.initial!="boolean"){const g=Dt(t,Array.isArray(c.initial)?c.initial[0]:c.initial);g&&g.transition&&(v.transition=g.transition)}f.forEach(g=>{const S=t.getBaseTarget(g),y=t.getValue(g);y&&(y.liveStyle=!0),v[g]=S??null}),h.push({animation:v})}let p=!!h.length;return s&&(c.initial===!1||c.initial===c.animate)&&!t.manuallyAnimateOnMount&&(p=!1),s=!1,p?e(h):Promise.resolve()}function a(l,c){var h;if(n[l].isActive===c)return Promise.resolve();(h=t.variantChildren)==null||h.forEach(f=>{var d;return(d=f.animationState)==null?void 0:d.setActive(l,c)}),n[l].isActive=c;const u=r(l);for(const f in n)n[f].protectedKeys={};return u}return{animateChanges:r,setActive:a,setAnimateFunction:o,getState:()=>n,reset:()=>{n=Bn(),s=!0}}}function Ja(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!Mi(e,t):!1}function Q(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function Bn(){return{animate:Q(!0),whileInView:Q(),whileHover:Q(),whileTap:Q(),whileDrag:Q(),whileFocus:Q(),exit:Q()}}class J{constructor(e){this.isMounted=!1,this.node=e}update(){}}class Qa extends J{constructor(e){super(e),e.animationState||(e.animationState=Za(e))}updateAnimationControlsSubscription(){const{animate:e}=this.node.getProps();zt(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:e}=this.node.getProps(),{animate:n}=this.node.prevProps||{};e!==n&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),(e=this.unmountControls)==null||e.call(this)}}let tl=0;class el extends J{constructor(){super(...arguments),this.id=tl++}update(){if(!this.node.presenceContext)return;const{isPresent:e,onExitComplete:n}=this.node.presenceContext,{isPresent:s}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===s)return;const i=this.node.animationState.setActive("exit",!e);n&&!e&&i.then(()=>{n(this.id)})}mount(){const{register:e,onExitComplete:n}=this.node.presenceContext||{};n&&n(this.id),e&&(this.unmount=e(this.id))}unmount(){}}const nl={animation:{Feature:Qa},exit:{Feature:el}};function Ct(t,e,n,s={passive:!0}){return t.addEventListener(e,n,s),()=>t.removeEventListener(e,n)}function kt(t){return{point:{x:t.pageX,y:t.pageY}}}const sl=t=>e=>Ze(e)&&t(e,kt(e));function xt(t,e,n,s){return Ct(t,e,sl(n),s)}function Ci({top:t,left:e,right:n,bottom:s}){return{x:{min:e,max:n},y:{min:t,max:s}}}function il({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function rl(t,e){if(!e)return t;const n=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:n.y,left:n.x,bottom:s.y,right:s.x}}const Ri=1e-4,ol=1-Ri,al=1+Ri,Ei=.01,ll=0-Ei,ul=0+Ei;function B(t){return t.max-t.min}function cl(t,e,n){return Math.abs(t-e)<=n}function On(t,e,n,s=.5){t.origin=s,t.originPoint=V(e.min,e.max,t.origin),t.scale=B(n)/B(e),t.translate=V(n.min,n.max,t.origin)-t.originPoint,(t.scale>=ol&&t.scale<=al||isNaN(t.scale))&&(t.scale=1),(t.translate>=ll&&t.translate<=ul||isNaN(t.translate))&&(t.translate=0)}function St(t,e,n,s){On(t.x,e.x,n.x,s?s.originX:void 0),On(t.y,e.y,n.y,s?s.originY:void 0)}function In(t,e,n){t.min=n.min+e.min,t.max=t.min+B(e)}function hl(t,e,n){In(t.x,e.x,n.x),In(t.y,e.y,n.y)}function jn(t,e,n){t.min=e.min-n.min,t.max=t.min+B(e)}function Pt(t,e,n){jn(t.x,e.x,n.x),jn(t.y,e.y,n.y)}const Nn=()=>({translate:0,scale:1,origin:0,originPoint:0}),lt=()=>({x:Nn(),y:Nn()}),Un=()=>({min:0,max:0}),C=()=>({x:Un(),y:Un()});function U(t){return[t("x"),t("y")]}function ne(t){return t===void 0||t===1}function Ae({scale:t,scaleX:e,scaleY:n}){return!ne(t)||!ne(e)||!ne(n)}function tt(t){return Ae(t)||Li(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function Li(t){return Kn(t.x)||Kn(t.y)}function Kn(t){return t&&t!=="0%"}function Gt(t,e,n){const s=t-n,i=e*s;return n+i}function Wn(t,e,n,s,i){return i!==void 0&&(t=Gt(t,i,s)),Gt(t,n,s)+e}function we(t,e=0,n=1,s,i){t.min=Wn(t.min,e,n,s,i),t.max=Wn(t.max,e,n,s,i)}function ki(t,{x:e,y:n}){we(t.x,e.translate,e.scale,e.originPoint),we(t.y,n.translate,n.scale,n.originPoint)}const $n=.999999999999,_n=1.0000000000001;function fl(t,e,n,s=!1){const i=n.length;if(!i)return;e.x=e.y=1;let o,r;for(let a=0;a<i;a++){o=n[a],r=o.projectionDelta;const{visualElement:l}=o.options;l&&l.props.style&&l.props.style.display==="contents"||(s&&o.options.layoutScroll&&o.scroll&&o!==o.root&&ct(t,{x:-o.scroll.offset.x,y:-o.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,ki(t,r)),s&&tt(o.latestValues)&&ct(t,o.latestValues))}e.x<_n&&e.x>$n&&(e.x=1),e.y<_n&&e.y>$n&&(e.y=1)}function ut(t,e){t.min=t.min+e,t.max=t.max+e}function Gn(t,e,n,s,i=.5){const o=V(t.min,t.max,i);we(t,e,n,o,s)}function ct(t,e){Gn(t.x,e.x,e.scaleX,e.scale,e.originX),Gn(t.y,e.y,e.scaleY,e.scale,e.originY)}function Fi(t,e){return Ci(rl(t.getBoundingClientRect(),e))}function dl(t,e,n){const s=Fi(t,n),{scroll:i}=e;return i&&(ut(s.x,i.offset.x),ut(s.y,i.offset.y)),s}const Bi=({current:t})=>t?t.ownerDocument.defaultView:null,Hn=(t,e)=>Math.abs(t-e);function ml(t,e){const n=Hn(t.x,e.x),s=Hn(t.y,e.y);return Math.sqrt(n**2+s**2)}class Oi{constructor(e,n,{transformPagePoint:s,contextWindow:i,dragSnapToOrigin:o=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const h=ie(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,d=ml(h.offset,{x:0,y:0})>=3;if(!f&&!d)return;const{point:m}=h,{timestamp:p}=E;this.history.push({...m,timestamp:p});const{onStart:v,onMove:g}=this.handlers;f||(v&&v(this.lastMoveEvent,h),this.startEvent=this.lastMoveEvent),g&&g(this.lastMoveEvent,h)},this.handlePointerMove=(h,f)=>{this.lastMoveEvent=h,this.lastMoveEventInfo=se(f,this.transformPagePoint),M.update(this.updatePoint,!0)},this.handlePointerUp=(h,f)=>{this.end();const{onEnd:d,onSessionEnd:m,resumeAnimation:p}=this.handlers;if(this.dragSnapToOrigin&&p&&p(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const v=ie(h.type==="pointercancel"?this.lastMoveEventInfo:se(f,this.transformPagePoint),this.history);this.startEvent&&d&&d(h,v),m&&m(h,v)},!Ze(e))return;this.dragSnapToOrigin=o,this.handlers=n,this.transformPagePoint=s,this.contextWindow=i||window;const r=kt(e),a=se(r,this.transformPagePoint),{point:l}=a,{timestamp:c}=E;this.history=[{...l,timestamp:c}];const{onSessionStart:u}=n;u&&u(e,ie(a,this.history)),this.removeListeners=Rt(xt(this.contextWindow,"pointermove",this.handlePointerMove),xt(this.contextWindow,"pointerup",this.handlePointerUp),xt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),q(this.updatePoint)}}function se(t,e){return e?{point:e(t.point)}:t}function zn(t,e){return{x:t.x-e.x,y:t.y-e.y}}function ie({point:t},e){return{point:t,delta:zn(t,Ii(e)),offset:zn(t,pl(e)),velocity:gl(e,.1)}}function pl(t){return t[0]}function Ii(t){return t[t.length-1]}function gl(t,e){if(t.length<2)return{x:0,y:0};let n=t.length-1,s=null;const i=Ii(t);for(;n>=0&&(s=t[n],!(i.timestamp-s.timestamp>$(e)));)n--;if(!s)return{x:0,y:0};const o=_(i.timestamp-s.timestamp);if(o===0)return{x:0,y:0};const r={x:(i.x-s.x)/o,y:(i.y-s.y)/o};return r.x===1/0&&(r.x=0),r.y===1/0&&(r.y=0),r}function yl(t,{min:e,max:n},s){return e!==void 0&&t<e?t=s?V(e,t,s.min):Math.max(t,e):n!==void 0&&t>n&&(t=s?V(n,t,s.max):Math.min(t,n)),t}function Xn(t,e,n){return{min:e!==void 0?t.min+e:void 0,max:n!==void 0?t.max+n-(t.max-t.min):void 0}}function vl(t,{top:e,left:n,bottom:s,right:i}){return{x:Xn(t.x,n,i),y:Xn(t.y,e,s)}}function Yn(t,e){let n=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([n,s]=[s,n]),{min:n,max:s}}function Tl(t,e){return{x:Yn(t.x,e.x),y:Yn(t.y,e.y)}}function xl(t,e){let n=.5;const s=B(t),i=B(e);return i>s?n=bt(e.min,e.max-s,t.min):s>i&&(n=bt(t.min,t.max-i,e.min)),z(0,1,n)}function Sl(t,e){const n={};return e.min!==void 0&&(n.min=e.min-t.min),e.max!==void 0&&(n.max=e.max-t.min),n}const Ve=.35;function Pl(t=Ve){return t===!1?t=0:t===!0&&(t=Ve),{x:qn(t,"left","right"),y:qn(t,"top","bottom")}}function qn(t,e,n){return{min:Zn(t,e),max:Zn(t,n)}}function Zn(t,e){return typeof t=="number"?t:t[e]||0}const bl=new WeakMap;class Al{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=C(),this.visualElement=e}start(e,{snapToCursor:n=!1}={}){const{presenceContext:s}=this.visualElement;if(s&&s.isPresent===!1)return;const i=u=>{const{dragSnapToOrigin:h}=this.getProps();h?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(kt(u).point)},o=(u,h)=>{const{drag:f,dragPropagation:d,onDragStart:m}=this.getProps();if(f&&!d&&(this.openDragLock&&this.openDragLock(),this.openDragLock=$o(f),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),U(v=>{let g=this.getAxisMotionValue(v).get()||0;if(G.test(g)){const{projection:S}=this.visualElement;if(S&&S.layout){const y=S.layout.layoutBox[v];y&&(g=B(y)*(parseFloat(g)/100))}}this.originPoint[v]=g}),m&&M.postRender(()=>m(u,h)),Pe(this.visualElement,"transform");const{animationState:p}=this.visualElement;p&&p.setActive("whileDrag",!0)},r=(u,h)=>{const{dragPropagation:f,dragDirectionLock:d,onDirectionLock:m,onDrag:p}=this.getProps();if(!f&&!this.openDragLock)return;const{offset:v}=h;if(d&&this.currentDirection===null){this.currentDirection=wl(v),this.currentDirection!==null&&m&&m(this.currentDirection);return}this.updateAxis("x",h.point,v),this.updateAxis("y",h.point,v),this.visualElement.render(),p&&p(u,h)},a=(u,h)=>this.stop(u,h),l=()=>U(u=>{var h;return this.getAnimationState(u)==="paused"&&((h=this.getAxisMotionValue(u).animation)==null?void 0:h.play())}),{dragSnapToOrigin:c}=this.getProps();this.panSession=new Oi(e,{onSessionStart:i,onStart:o,onMove:r,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:c,contextWindow:Bi(this.visualElement)})}stop(e,n){const s=this.isDragging;if(this.cancel(),!s)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:o}=this.getProps();o&&M.postRender(()=>o(e,n))}cancel(){this.isDragging=!1;const{projection:e,animationState:n}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:s}=this.getProps();!s&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(e,n,s){const{drag:i}=this.getProps();if(!s||!Ot(e,i,this.currentDirection))return;const o=this.getAxisMotionValue(e);let r=this.originPoint[e]+s[e];this.constraints&&this.constraints[e]&&(r=yl(r,this.constraints[e],this.elastic[e])),o.set(r)}resolveConstraints(){var o;const{dragConstraints:e,dragElastic:n}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(o=this.visualElement.projection)==null?void 0:o.layout,i=this.constraints;e&&at(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&s?this.constraints=vl(s.layoutBox,e):this.constraints=!1,this.elastic=Pl(n),i!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&U(r=>{this.constraints!==!1&&this.getAxisMotionValue(r)&&(this.constraints[r]=Sl(s.layoutBox[r],this.constraints[r]))})}resolveRefConstraints(){const{dragConstraints:e,onMeasureDragConstraints:n}=this.getProps();if(!e||!at(e))return!1;const s=e.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const o=dl(s,i.root,this.visualElement.getTransformPagePoint());let r=Tl(i.layout.layoutBox,o);if(n){const a=n(il(r));this.hasMutatedConstraints=!!a,a&&(r=Ci(a))}return r}startAnimation(e){const{drag:n,dragMomentum:s,dragElastic:i,dragTransition:o,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},c=U(u=>{if(!Ot(u,n,this.currentDirection))return;let h=l&&l[u]||{};r&&(h={min:0,max:0});const f=i?200:1e6,d=i?40:1e7,m={type:"inertia",velocity:s?e[u]:0,bounceStiffness:f,bounceDamping:d,timeConstant:750,restDelta:1,restSpeed:10,...o,...h};return this.startAxisValueAnimation(u,m)});return Promise.all(c).then(a)}startAxisValueAnimation(e,n){const s=this.getAxisMotionValue(e);return Pe(this.visualElement,e),s.start(an(e,s,0,n,this.visualElement,!1))}stopAnimation(){U(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){U(e=>{var n;return(n=this.getAxisMotionValue(e).animation)==null?void 0:n.pause()})}getAnimationState(e){var n;return(n=this.getAxisMotionValue(e).animation)==null?void 0:n.state}getAxisMotionValue(e){const n=`_drag${e.toUpperCase()}`,s=this.visualElement.getProps(),i=s[n];return i||this.visualElement.getValue(e,(s.initial?s.initial[e]:void 0)||0)}snapToCursor(e){U(n=>{const{drag:s}=this.getProps();if(!Ot(n,s,this.currentDirection))return;const{projection:i}=this.visualElement,o=this.getAxisMotionValue(n);if(i&&i.layout){const{min:r,max:a}=i.layout.layoutBox[n];o.set(e[n]-V(r,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:e,dragConstraints:n}=this.getProps(),{projection:s}=this.visualElement;if(!at(n)||!s||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};U(r=>{const a=this.getAxisMotionValue(r);if(a&&this.constraints!==!1){const l=a.get();i[r]=xl({min:l,max:l},this.constraints[r])}});const{transformTemplate:o}=this.visualElement.getProps();this.visualElement.current.style.transform=o?o({},""):"none",s.root&&s.root.updateScroll(),s.updateLayout(),this.resolveConstraints(),U(r=>{if(!Ot(r,e,null))return;const a=this.getAxisMotionValue(r),{min:l,max:c}=this.constraints[r];a.set(V(l,c,i[r]))})}addListeners(){if(!this.visualElement.current)return;bl.set(this.visualElement,this);const e=this.visualElement.current,n=xt(e,"pointerdown",l=>{const{drag:c,dragListener:u=!0}=this.getProps();c&&u&&this.start(l)}),s=()=>{const{dragConstraints:l}=this.getProps();at(l)&&l.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,o=i.addEventListener("measure",s);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),M.read(s);const r=Ct(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:c})=>{this.isDragging&&c&&(U(u=>{const h=this.getAxisMotionValue(u);h&&(this.originPoint[u]+=l[u].translate,h.set(h.get()+l[u].translate))}),this.visualElement.render())});return()=>{r(),n(),o(),a&&a()}}getProps(){const e=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:s=!1,dragPropagation:i=!1,dragConstraints:o=!1,dragElastic:r=Ve,dragMomentum:a=!0}=e;return{...e,drag:n,dragDirectionLock:s,dragPropagation:i,dragConstraints:o,dragElastic:r,dragMomentum:a}}}function Ot(t,e,n){return(e===!0||e===t)&&(n===null||n===t)}function wl(t,e=10){let n=null;return Math.abs(t.y)>e?n="y":Math.abs(t.x)>e&&(n="x"),n}class Vl extends J{constructor(e){super(e),this.removeGroupControls=K,this.removeListeners=K,this.controls=new Al(e)}mount(){const{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||K}unmount(){this.removeGroupControls(),this.removeListeners()}}const Jn=t=>(e,n)=>{t&&M.postRender(()=>t(e,n))};class Ml extends J{constructor(){super(...arguments),this.removePointerDownListener=K}onPointerDown(e){this.session=new Oi(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:Bi(this.node)})}createPanHandlers(){const{onPanSessionStart:e,onPanStart:n,onPan:s,onPanEnd:i}=this.node.getProps();return{onSessionStart:Jn(e),onStart:Jn(n),onMove:s,onEnd:(o,r)=>{delete this.session,i&&M.postRender(()=>i(o,r))}}}mount(){this.removePointerDownListener=xt(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}const Ut={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function Qn(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}const gt={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(T.test(t))t=parseFloat(t);else return t;const n=Qn(t,e.target.x),s=Qn(t,e.target.y);return`${n}% ${s}%`}},Dl={correct:(t,{treeScale:e,projectionDelta:n})=>{const s=t,i=Z.parse(t);if(i.length>5)return s;const o=Z.createTransformer(t),r=typeof i[0]!="number"?1:0,a=n.x.scale*e.x,l=n.y.scale*e.y;i[0+r]/=a,i[1+r]/=l;const c=V(a,l,.5);return typeof i[2+r]=="number"&&(i[2+r]/=c),typeof i[3+r]=="number"&&(i[3+r]/=c),o(i)}};class Cl extends P.Component{componentDidMount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s,layoutId:i}=this.props,{projection:o}=e;da(Rl),o&&(n.group&&n.group.add(o),s&&s.register&&i&&s.register(o),o.root.didUpdate(),o.addEventListener("animationComplete",()=>{this.safeToRemove()}),o.setOptions({...o.options,onExitComplete:()=>this.safeToRemove()})),Ut.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){const{layoutDependency:n,visualElement:s,drag:i,isPresent:o}=this.props,{projection:r}=s;return r&&(r.isPresent=o,i||e.layoutDependency!==n||n===void 0||e.isPresent!==o?r.willUpdate():this.safeToRemove(),e.isPresent!==o&&(o?r.promote():r.relegate()||M.postRender(()=>{const a=r.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),qe.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:e,layoutGroup:n,switchLayoutGroup:s}=this.props,{projection:i}=e;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),s&&s.deregister&&s.deregister(i))}safeToRemove(){const{safeToRemove:e}=this.props;e&&e()}render(){return null}}function ji(t){const[e,n]=tr(),s=P.useContext(bs);return ue.jsx(Cl,{...t,layoutGroup:s,switchLayoutGroup:P.useContext(gi),isPresent:e,safeToRemove:n})}const Rl={borderRadius:{...gt,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:gt,borderTopRightRadius:gt,borderBottomLeftRadius:gt,borderBottomRightRadius:gt,boxShadow:Dl};function El(t,e,n){const s=k(t)?t:ht(t);return s.start(an("",s,e,n)),s.animation}function Ll(t){return t instanceof SVGElement&&t.tagName!=="svg"}const kl=(t,e)=>t.depth-e.depth;class Fl{constructor(){this.children=[],this.isDirty=!1}add(e){Ee(this.children,e),this.isDirty=!0}remove(e){Le(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(kl),this.isDirty=!1,this.children.forEach(e)}}function Bl(t,e){const n=I.now(),s=({timestamp:i})=>{const o=i-n;o>=e&&(q(s),t(o-e))};return M.setup(s,!0),()=>q(s)}const Ni=["TopLeft","TopRight","BottomLeft","BottomRight"],Ol=Ni.length,ts=t=>typeof t=="string"?parseFloat(t):t,es=t=>typeof t=="number"||T.test(t);function Il(t,e,n,s,i,o){i?(t.opacity=V(0,n.opacity??1,jl(s)),t.opacityExit=V(e.opacity??1,0,Nl(s))):o&&(t.opacity=V(e.opacity??1,n.opacity??1,s));for(let r=0;r<Ol;r++){const a=`border${Ni[r]}Radius`;let l=ns(e,a),c=ns(n,a);if(l===void 0&&c===void 0)continue;l||(l=0),c||(c=0),l===0||c===0||es(l)===es(c)?(t[a]=Math.max(V(ts(l),ts(c),s),0),(G.test(c)||G.test(l))&&(t[a]+="%")):t[a]=c}(e.rotate||n.rotate)&&(t.rotate=V(e.rotate||0,n.rotate||0,s))}function ns(t,e){return t[e]!==void 0?t[e]:t.borderRadius}const jl=Ui(0,.5,Fs),Nl=Ui(.5,.95,K);function Ui(t,e,n){return s=>s<t?0:s>e?1:n(bt(t,e,s))}function ss(t,e){t.min=e.min,t.max=e.max}function N(t,e){ss(t.x,e.x),ss(t.y,e.y)}function is(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function rs(t,e,n,s,i){return t-=e,t=Gt(t,1/n,s),i!==void 0&&(t=Gt(t,1/i,s)),t}function Ul(t,e=0,n=1,s=.5,i,o=t,r=t){if(G.test(e)&&(e=parseFloat(e),e=V(r.min,r.max,e/100)-r.min),typeof e!="number")return;let a=V(o.min,o.max,s);t===o&&(a-=e),t.min=rs(t.min,e,n,a,i),t.max=rs(t.max,e,n,a,i)}function os(t,e,[n,s,i],o,r){Ul(t,e[n],e[s],e[i],e.scale,o,r)}const Kl=["x","scaleX","originX"],Wl=["y","scaleY","originY"];function as(t,e,n,s){os(t.x,e,Kl,n?n.x:void 0,s?s.x:void 0),os(t.y,e,Wl,n?n.y:void 0,s?s.y:void 0)}function ls(t){return t.translate===0&&t.scale===1}function Ki(t){return ls(t.x)&&ls(t.y)}function us(t,e){return t.min===e.min&&t.max===e.max}function $l(t,e){return us(t.x,e.x)&&us(t.y,e.y)}function cs(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function Wi(t,e){return cs(t.x,e.x)&&cs(t.y,e.y)}function hs(t){return B(t.x)/B(t.y)}function fs(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class _l{constructor(){this.members=[]}add(e){Ee(this.members,e),e.scheduleRender()}remove(e){if(Le(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(e){const n=this.members.findIndex(i=>e===i);if(n===0)return!1;let s;for(let i=n;i>=0;i--){const o=this.members[i];if(o.isPresent!==!1){s=o;break}}return s?(this.promote(s),!0):!1}promote(e,n){const s=this.lead;if(e!==s&&(this.prevLead=s,this.lead=e,e.show(),s)){s.instance&&s.scheduleRender(),e.scheduleRender(),e.resumeFrom=s,n&&(e.resumeFrom.preserveOpacity=!0),s.snapshot&&(e.snapshot=s.snapshot,e.snapshot.latestValues=s.animationValues||s.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);const{crossfade:i}=e.options;i===!1&&s.hide()}}exitAnimationComplete(){this.members.forEach(e=>{const{options:n,resumingFrom:s}=e;n.onExitComplete&&n.onExitComplete(),s&&s.options.onExitComplete&&s.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Gl(t,e,n){let s="";const i=t.x.translate/e.x,o=t.y.translate/e.y,r=(n==null?void 0:n.z)||0;if((i||o||r)&&(s=`translate3d(${i}px, ${o}px, ${r}px) `),(e.x!==1||e.y!==1)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),n){const{transformPerspective:c,rotate:u,rotateX:h,rotateY:f,skewX:d,skewY:m}=n;c&&(s=`perspective(${c}px) ${s}`),u&&(s+=`rotate(${u}deg) `),h&&(s+=`rotateX(${h}deg) `),f&&(s+=`rotateY(${f}deg) `),d&&(s+=`skewX(${d}deg) `),m&&(s+=`skewY(${m}deg) `)}const a=t.x.scale*e.x,l=t.y.scale*e.y;return(a!==1||l!==1)&&(s+=`scale(${a}, ${l})`),s||"none"}const re=["","X","Y","Z"],Hl={visibility:"hidden"},zl=1e3;let Xl=0;function oe(t,e,n,s){const{latestValues:i}=e;i[t]&&(n[t]=i[t],e.setStaticValue(t,0),s&&(s[t]=0))}function $i(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;const{visualElement:e}=t.options;if(!e)return;const n=wi(e);if(window.MotionHasOptimisedAnimation(n,"transform")){const{layout:i,layoutId:o}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",M,!(i||o))}const{parent:s}=t;s&&!s.hasCheckedOptimisedAppear&&$i(s)}function _i({attachResizeListener:t,defaultParent:e,measureScroll:n,checkIsScrollRoot:s,resetTransform:i}){return class{constructor(r={},a=e==null?void 0:e()){this.id=Xl++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,this.nodes.forEach(Zl),this.nodes.forEach(nu),this.nodes.forEach(su),this.nodes.forEach(Jl)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=r,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new Fl)}addEventListener(r,a){return this.eventHandlers.has(r)||this.eventHandlers.set(r,new Be),this.eventHandlers.get(r).add(a)}notifyListeners(r,...a){const l=this.eventHandlers.get(r);l&&l.notify(...a)}hasListeners(r){return this.eventHandlers.has(r)}mount(r){if(this.instance)return;this.isSVG=Ll(r),this.instance=r;const{layoutId:a,layout:l,visualElement:c}=this.options;if(c&&!c.current&&c.mount(r),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(l||a)&&(this.isLayoutDirty=!0),t){let u;const h=()=>this.root.updateBlockedByResize=!1;t(r,()=>{this.root.updateBlockedByResize=!0,u&&u(),u=Bl(h,250),Ut.hasAnimatedSinceResize&&(Ut.hasAnimatedSinceResize=!1,this.nodes.forEach(ms))})}a&&this.root.registerSharedNode(a,this),this.options.animate!==!1&&c&&(a||l)&&this.addEventListener("didUpdate",({delta:u,hasLayoutChanged:h,hasRelativeLayoutChanged:f,layout:d})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const m=this.options.transition||c.getDefaultTransition()||lu,{onLayoutAnimationStart:p,onLayoutAnimationComplete:v}=c.getProps(),g=!this.targetLayout||!Wi(this.targetLayout,d),S=!h&&f;if(this.options.layoutRoot||this.resumeFrom||S||h&&(g||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(u,S);const y={...Xe(m,"layout"),onPlay:p,onComplete:v};(c.shouldReduceMotion||this.options.layoutRoot)&&(y.delay=0,y.type=!1),this.startAnimation(y)}else h||ms(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=d})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const r=this.getStack();r&&r.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),q(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(iu),this.animationId++)}getTransformTemplate(){const{visualElement:r}=this.options;return r&&r.getProps().transformTemplate}willUpdate(r=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&$i(this),!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let u=0;u<this.path.length;u++){const h=this.path[u];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const c=this.getTransformTemplate();this.prevTransformTemplateValue=c?c(this.latestValues,""):void 0,this.updateSnapshot(),r&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(ds);return}this.isUpdating||this.nodes.forEach(tu),this.isUpdating=!1,this.nodes.forEach(eu),this.nodes.forEach(Yl),this.nodes.forEach(ql),this.clearAllSnapshots();const a=I.now();E.delta=z(0,1e3/60,a-E.timestamp),E.timestamp=a,E.isProcessing=!0,Zt.update.process(E),Zt.preRender.process(E),Zt.render.process(E),E.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,qe.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(Ql),this.sharedNodes.forEach(ru)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,M.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){M.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),this.snapshot&&!B(this.snapshot.measuredBox.x)&&!B(this.snapshot.measuredBox.y)&&(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const r=this.layout;this.layout=this.measure(!1),this.layoutCorrected=C(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,r?r.layoutBox:void 0)}updateScroll(r="measure"){let a=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===r&&(a=!1),a&&this.instance){const l=s(this.instance);this.scroll={animationId:this.root.animationId,phase:r,isRoot:l,offset:n(this.instance),wasRoot:this.scroll?this.scroll.isRoot:l}}}resetTransform(){if(!i)return;const r=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,a=this.projectionDelta&&!Ki(this.projectionDelta),l=this.getTransformTemplate(),c=l?l(this.latestValues,""):void 0,u=c!==this.prevTransformTemplateValue;r&&this.instance&&(a||tt(this.latestValues)||u)&&(i(this.instance,c),this.shouldResetTransform=!1,this.scheduleRender())}measure(r=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return r&&(l=this.removeTransform(l)),uu(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){var c;const{visualElement:r}=this.options;if(!r)return C();const a=r.measureViewportBox();if(!(((c=this.scroll)==null?void 0:c.wasRoot)||this.path.some(cu))){const{scroll:u}=this.root;u&&(ut(a.x,u.offset.x),ut(a.y,u.offset.y))}return a}removeElementScroll(r){var l;const a=C();if(N(a,r),(l=this.scroll)!=null&&l.wasRoot)return a;for(let c=0;c<this.path.length;c++){const u=this.path[c],{scroll:h,options:f}=u;u!==this.root&&h&&f.layoutScroll&&(h.wasRoot&&N(a,r),ut(a.x,h.offset.x),ut(a.y,h.offset.y))}return a}applyTransform(r,a=!1){const l=C();N(l,r);for(let c=0;c<this.path.length;c++){const u=this.path[c];!a&&u.options.layoutScroll&&u.scroll&&u!==u.root&&ct(l,{x:-u.scroll.offset.x,y:-u.scroll.offset.y}),tt(u.latestValues)&&ct(l,u.latestValues)}return tt(this.latestValues)&&ct(l,this.latestValues),l}removeTransform(r){const a=C();N(a,r);for(let l=0;l<this.path.length;l++){const c=this.path[l];if(!c.instance||!tt(c.latestValues))continue;Ae(c.latestValues)&&c.updateSnapshot();const u=C(),h=c.measurePageBox();N(u,h),as(a,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,u)}return tt(this.latestValues)&&as(a,this.latestValues),a}setTargetDelta(r){this.targetDelta=r,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(r){this.options={...this.options,...r,crossfade:r.crossfade!==void 0?r.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==E.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(r=!1){var f;const a=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=a.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=a.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=a.isSharedProjectionDirty);const l=!!this.resumingFrom||this!==a;if(!(r||l&&this.isSharedProjectionDirty||this.isProjectionDirty||(f=this.parent)!=null&&f.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;const{layout:u,layoutId:h}=this.options;if(!(!this.layout||!(u||h))){if(this.resolvedRelativeTargetAt=E.timestamp,!this.targetDelta&&!this.relativeTarget){const d=this.getClosestProjectingParent();d&&d.layout&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=C(),this.relativeTargetOrigin=C(),Pt(this.relativeTargetOrigin,this.layout.layoutBox,d.layout.layoutBox),N(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)&&(this.target||(this.target=C(),this.targetWithTransforms=C()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),hl(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):N(this.target,this.layout.layoutBox),ki(this.target,this.targetDelta)):N(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget)){this.attemptToResolveRelativeTarget=!1;const d=this.getClosestProjectingParent();d&&!!d.resumingFrom==!!this.resumingFrom&&!d.options.layoutScroll&&d.target&&this.animationProgress!==1?(this.relativeParent=d,this.forceRelativeParentToResolveTarget(),this.relativeTarget=C(),this.relativeTargetOrigin=C(),Pt(this.relativeTargetOrigin,this.target,d.target),N(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}}}getClosestProjectingParent(){if(!(!this.parent||Ae(this.parent.latestValues)||Li(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var m;const r=this.getLead(),a=!!this.resumingFrom||this!==r;let l=!0;if((this.isProjectionDirty||(m=this.parent)!=null&&m.isProjectionDirty)&&(l=!1),a&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(l=!1),this.resolvedRelativeTargetAt===E.timestamp&&(l=!1),l)return;const{layout:c,layoutId:u}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||u))return;N(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,f=this.treeScale.y;fl(this.layoutCorrected,this.treeScale,this.path,a),r.layout&&!r.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(r.target=r.layout.layoutBox,r.targetWithTransforms=C());const{target:d}=r;if(!d){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}!this.projectionDelta||!this.prevProjectionDelta?this.createProjectionDeltas():(is(this.prevProjectionDelta.x,this.projectionDelta.x),is(this.prevProjectionDelta.y,this.projectionDelta.y)),St(this.projectionDelta,this.layoutCorrected,d,this.latestValues),(this.treeScale.x!==h||this.treeScale.y!==f||!fs(this.projectionDelta.x,this.prevProjectionDelta.x)||!fs(this.projectionDelta.y,this.prevProjectionDelta.y))&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",d))}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(r=!0){var a;if((a=this.options.visualElement)==null||a.scheduleRender(),r){const l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=lt(),this.projectionDelta=lt(),this.projectionDeltaWithTransform=lt()}setAnimationOrigin(r,a=!1){const l=this.snapshot,c=l?l.latestValues:{},u={...this.latestValues},h=lt();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const f=C(),d=l?l.source:void 0,m=this.layout?this.layout.source:void 0,p=d!==m,v=this.getStack(),g=!v||v.members.length<=1,S=!!(p&&!g&&this.options.crossfade===!0&&!this.path.some(au));this.animationProgress=0;let y;this.mixTargetDelta=A=>{const x=A/1e3;ps(h.x,r.x,x),ps(h.y,r.y,x),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Pt(f,this.layout.layoutBox,this.relativeParent.layout.layoutBox),ou(this.relativeTarget,this.relativeTargetOrigin,f,x),y&&$l(this.relativeTarget,y)&&(this.isProjectionDirty=!1),y||(y=C()),N(y,this.relativeTarget)),p&&(this.animationValues=u,Il(u,c,this.latestValues,x,S,g)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=x},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(r){var a,l,c;this.notifyListeners("animationStart"),(a=this.currentAnimation)==null||a.stop(!1),(c=(l=this.resumingFrom)==null?void 0:l.currentAnimation)==null||c.stop(!1),this.pendingAnimation&&(q(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=M.update(()=>{Ut.hasAnimatedSinceResize=!0,this.motionValue||(this.motionValue=ht(0)),this.currentAnimation=El(this.motionValue,[0,1e3],{...r,isSync:!0,onUpdate:u=>{this.mixTargetDelta(u),r.onUpdate&&r.onUpdate(u)},onStop:()=>{},onComplete:()=>{r.onComplete&&r.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const r=this.getStack();r&&r.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(zl),this.currentAnimation.stop(!1)),this.completeAnimation()}applyTransformsToTarget(){const r=this.getLead();let{targetWithTransforms:a,target:l,layout:c,latestValues:u}=r;if(!(!a||!l||!c)){if(this!==r&&this.layout&&c&&Gi(this.options.animationType,this.layout.layoutBox,c.layoutBox)){l=this.target||C();const h=B(this.layout.layoutBox.x);l.x.min=r.target.x.min,l.x.max=l.x.min+h;const f=B(this.layout.layoutBox.y);l.y.min=r.target.y.min,l.y.max=l.y.min+f}N(a,l),ct(a,u),St(this.projectionDeltaWithTransform,this.layoutCorrected,a,u)}}registerSharedNode(r,a){this.sharedNodes.has(r)||this.sharedNodes.set(r,new _l),this.sharedNodes.get(r).add(a);const c=a.options.initialPromotionConfig;a.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(a):void 0})}isLead(){const r=this.getStack();return r?r.lead===this:!0}getLead(){var a;const{layoutId:r}=this.options;return r?((a=this.getStack())==null?void 0:a.lead)||this:this}getPrevLead(){var a;const{layoutId:r}=this.options;return r?(a=this.getStack())==null?void 0:a.prevLead:void 0}getStack(){const{layoutId:r}=this.options;if(r)return this.root.sharedNodes.get(r)}promote({needsReset:r,transition:a,preserveFollowOpacity:l}={}){const c=this.getStack();c&&c.promote(this,l),r&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const r=this.getStack();return r?r.relegate(this):!1}resetSkewAndRotation(){const{visualElement:r}=this.options;if(!r)return;let a=!1;const{latestValues:l}=r;if((l.z||l.rotate||l.rotateX||l.rotateY||l.rotateZ||l.skewX||l.skewY)&&(a=!0),!a)return;const c={};l.z&&oe("z",r,c,this.animationValues);for(let u=0;u<re.length;u++)oe(`rotate${re[u]}`,r,c,this.animationValues),oe(`skew${re[u]}`,r,c,this.animationValues);r.render();for(const u in c)r.setStaticValue(u,c[u]),this.animationValues&&(this.animationValues[u]=c[u]);r.scheduleRender()}getProjectionStyles(r){if(!this.instance||this.isSVG)return;if(!this.isVisible)return Hl;const a={visibility:""},l=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,a.opacity="",a.pointerEvents=Nt(r==null?void 0:r.pointerEvents)||"",a.transform=l?l(this.latestValues,""):"none",a;const c=this.getLead();if(!this.projectionDelta||!this.layout||!c.target){const d={};return this.options.layoutId&&(d.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,d.pointerEvents=Nt(r==null?void 0:r.pointerEvents)||""),this.hasProjected&&!tt(this.latestValues)&&(d.transform=l?l({},""):"none",this.hasProjected=!1),d}const u=c.animationValues||c.latestValues;this.applyTransformsToTarget(),a.transform=Gl(this.projectionDeltaWithTransform,this.treeScale,u),l&&(a.transform=l(u,a.transform));const{x:h,y:f}=this.projectionDelta;a.transformOrigin=`${h.origin*100}% ${f.origin*100}% 0`,c.animationValues?a.opacity=c===this?u.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:u.opacityExit:a.opacity=c===this?u.opacity!==void 0?u.opacity:"":u.opacityExit!==void 0?u.opacityExit:0;for(const d in Mt){if(u[d]===void 0)continue;const{correct:m,applyTo:p,isCSSVariable:v}=Mt[d],g=a.transform==="none"?u[d]:m(u[d],c);if(p){const S=p.length;for(let y=0;y<S;y++)a[p[y]]=g}else v?this.options.visualElement.renderState.vars[d]=g:a[d]=g}return this.options.layoutId&&(a.pointerEvents=c===this?Nt(r==null?void 0:r.pointerEvents)||"":"none"),a}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(r=>{var a;return(a=r.currentAnimation)==null?void 0:a.stop(!1)}),this.root.nodes.forEach(ds),this.root.sharedNodes.clear()}}}function Yl(t){t.updateLayout()}function ql(t){var n;const e=((n=t.resumeFrom)==null?void 0:n.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){const{layoutBox:s,measuredBox:i}=t.layout,{animationType:o}=t.options,r=e.source!==t.layout.source;o==="size"?U(h=>{const f=r?e.measuredBox[h]:e.layoutBox[h],d=B(f);f.min=s[h].min,f.max=f.min+d}):Gi(o,e.layoutBox,s)&&U(h=>{const f=r?e.measuredBox[h]:e.layoutBox[h],d=B(s[h]);f.max=f.min+d,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[h].max=t.relativeTarget[h].min+d)});const a=lt();St(a,s,e.layoutBox);const l=lt();r?St(l,t.applyTransform(i,!0),e.measuredBox):St(l,s,e.layoutBox);const c=!Ki(a);let u=!1;if(!t.resumeFrom){const h=t.getClosestProjectingParent();if(h&&!h.resumeFrom){const{snapshot:f,layout:d}=h;if(f&&d){const m=C();Pt(m,e.layoutBox,f.layoutBox);const p=C();Pt(p,s,d.layoutBox),Wi(m,p)||(u=!0),h.options.layoutRoot&&(t.relativeTarget=p,t.relativeTargetOrigin=m,t.relativeParent=h)}}}t.notifyListeners("didUpdate",{layout:s,snapshot:e,delta:l,layoutDelta:a,hasLayoutChanged:c,hasRelativeLayoutChanged:u})}else if(t.isLead()){const{onExitComplete:s}=t.options;s&&s()}t.options.transition=void 0}function Zl(t){t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function Jl(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function Ql(t){t.clearSnapshot()}function ds(t){t.clearMeasurements()}function tu(t){t.isLayoutDirty=!1}function eu(t){const{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function ms(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function nu(t){t.resolveTargetDelta()}function su(t){t.calcProjection()}function iu(t){t.resetSkewAndRotation()}function ru(t){t.removeLeadSnapshot()}function ps(t,e,n){t.translate=V(e.translate,0,n),t.scale=V(e.scale,1,n),t.origin=e.origin,t.originPoint=e.originPoint}function gs(t,e,n,s){t.min=V(e.min,n.min,s),t.max=V(e.max,n.max,s)}function ou(t,e,n,s){gs(t.x,e.x,n.x,s),gs(t.y,e.y,n.y,s)}function au(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}const lu={duration:.45,ease:[.4,0,.1,1]},ys=t=>typeof navigator<"u"&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),vs=ys("applewebkit/")&&!ys("chrome/")?Math.round:K;function Ts(t){t.min=vs(t.min),t.max=vs(t.max)}function uu(t){Ts(t.x),Ts(t.y)}function Gi(t,e,n){return t==="position"||t==="preserve-aspect"&&!cl(hs(e),hs(n),.2)}function cu(t){var e;return t!==t.root&&((e=t.scroll)==null?void 0:e.wasRoot)}const hu=_i({attachResizeListener:(t,e)=>Ct(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ae={current:void 0},Hi=_i({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!ae.current){const t=new hu({});t.mount(window),t.setOptions({layoutScroll:!0}),ae.current=t}return ae.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>window.getComputedStyle(t).position==="fixed"}),fu={pan:{Feature:Ml},drag:{Feature:Vl,ProjectionNode:Hi,MeasureLayout:ji}};function xs(t,e,n){const{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover",n==="Start");const i="onHover"+n,o=s[i];o&&M.postRender(()=>o(e,kt(e)))}class du extends J{mount(){const{current:e}=this.node;e&&(this.unmount=_o(e,(n,s)=>(xs(this.node,s,"Start"),i=>xs(this.node,i,"End"))))}unmount(){}}class mu extends J{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=Rt(Ct(this.node.current,"focus",()=>this.onFocus()),Ct(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function Ss(t,e,n){const{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap",n==="Start");const i="onTap"+(n==="End"?"":n),o=s[i];o&&M.postRender(()=>o(e,kt(e)))}class pu extends J{mount(){const{current:e}=this.node;e&&(this.unmount=Xo(e,(n,s)=>(Ss(this.node,s,"Start"),(i,{success:o})=>Ss(this.node,i,o?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}const Me=new WeakMap,le=new WeakMap,gu=t=>{const e=Me.get(t.target);e&&e(t)},yu=t=>{t.forEach(gu)};function vu({root:t,...e}){const n=t||document;le.has(n)||le.set(n,{});const s=le.get(n),i=JSON.stringify(e);return s[i]||(s[i]=new IntersectionObserver(yu,{root:t,...e})),s[i]}function Tu(t,e,n){const s=vu(e);return Me.set(t,n),s.observe(t),()=>{Me.delete(t),s.unobserve(t)}}const xu={some:0,all:1};class Su extends J{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:e={}}=this.node.getProps(),{root:n,margin:s,amount:i="some",once:o}=e,r={root:n?n.current:void 0,rootMargin:s,threshold:typeof i=="number"?i:xu[i]},a=l=>{const{isIntersecting:c}=l;if(this.isInView===c||(this.isInView=c,o&&!c&&this.hasEnteredView))return;c&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",c);const{onViewportEnter:u,onViewportLeave:h}=this.node.getProps(),f=c?u:h;f&&f(l)};return Tu(this.node.current,r,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:e,prevProps:n}=this.node;["amount","margin","root"].some(Pu(e,n))&&this.startObserver()}unmount(){}}function Pu({viewport:t={}},{viewport:e={}}={}){return n=>t[n]!==e[n]}const bu={inView:{Feature:Su},tap:{Feature:pu},focus:{Feature:mu},hover:{Feature:du}},Au={layout:{ProjectionNode:Hi,MeasureLayout:ji}},De={current:null},zi={current:!1};function wu(){if(zi.current=!0,!!Ce)if(window.matchMedia){const t=window.matchMedia("(prefers-reduced-motion)"),e=()=>De.current=t.matches;t.addListener(e),e()}else De.current=!1}const Vu=new WeakMap;function Mu(t,e,n){for(const s in e){const i=e[s],o=n[s];if(k(i))t.addValue(s,i);else if(k(o))t.addValue(s,ht(i,{owner:t}));else if(o!==i)if(t.hasValue(s)){const r=t.getValue(s);r.liveStyle===!0?r.jump(i):r.hasAnimated||r.set(i)}else{const r=t.getStaticValue(s);t.addValue(s,ht(r!==void 0?r:i,{owner:t}))}}for(const s in n)e[s]===void 0&&t.removeValue(s);return e}const Ps=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class Du{scrapeMotionValuesFromProps(e,n,s){return{}}constructor({parent:e,props:n,presenceContext:s,reducedMotionConfig:i,blockInitialAnimation:o,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ze,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{const f=I.now();this.renderScheduledAt<f&&(this.renderScheduledAt=f,M.render(this.render,!1,!0))};const{latestValues:l,renderState:c}=r;this.latestValues=l,this.baseTarget={...l},this.initialValues=n.initial?{...l}:{},this.renderState=c,this.parent=e,this.props=n,this.presenceContext=s,this.depth=e?e.depth+1:0,this.reducedMotionConfig=i,this.options=a,this.blockInitialAnimation=!!o,this.isControllingVariants=Xt(n),this.isVariantNode=mi(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);const{willChange:u,...h}=this.scrapeMotionValuesFromProps(n,{},this);for(const f in h){const d=h[f];l[f]!==void 0&&k(d)&&d.set(l[f],!1)}}mount(e){this.current=e,Vu.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,s)=>this.bindToMotionValue(s,n)),zi.current||wu(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:De.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){this.projection&&this.projection.unmount(),q(this.notifyUpdate),q(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const e in this.events)this.events[e].clear();for(const e in this.features){const n=this.features[e];n&&(n.unmount(),n.isMounted=!1)}this.current=null}bindToMotionValue(e,n){this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();const s=pt.has(e);s&&this.onBindTransform&&this.onBindTransform();const i=n.on("change",a=>{this.latestValues[e]=a,this.props.onUpdate&&M.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),o=n.on("renderRequest",this.scheduleRender);let r;window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,n)),this.valueSubscriptions.set(e,()=>{i(),o(),r&&r(),n.owner&&n.stop()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}updateFeatures(){let e="animation";for(e in ft){const n=ft[e];if(!n)continue;const{isEnabled:s,Feature:i}=n;if(!this.features[e]&&i&&s(this.props)&&(this.features[e]=new i(this)),this.features[e]){const o=this.features[e];o.isMounted?o.update():(o.mount(),o.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):C()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,n){this.latestValues[e]=n}update(e,n){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let s=0;s<Ps.length;s++){const i=Ps[s];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const o="on"+i,r=e[o];r&&(this.propEventSubscriptions[i]=this.on(i,r))}this.prevMotionValues=Mu(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(e),()=>n.variantChildren.delete(e)}addValue(e,n){const s=this.values.get(e);n!==s&&(s&&this.removeValue(e),this.bindToMotionValue(e,n),this.values.set(e,n),this.latestValues[e]=n.get())}removeValue(e){this.values.delete(e);const n=this.valueSubscriptions.get(e);n&&(n(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,n){if(this.props.values&&this.props.values[e])return this.props.values[e];let s=this.values.get(e);return s===void 0&&n!==void 0&&(s=ht(n===null?void 0:n,{owner:this}),this.addValue(e,s)),s}readValue(e,n){let s=this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:this.getBaseTargetFromProps(this.props,e)??this.readValueFromInstance(this.current,e,this.options);return s!=null&&(typeof s=="string"&&(ws(s)||Vs(s))?s=parseFloat(s):!qo(s)&&Z.test(n)&&(s=ai(e,n)),this.setBaseTarget(e,k(s)?s.get():s)),k(s)?s.get():s}setBaseTarget(e,n){this.baseTarget[e]=n}getBaseTarget(e){var o;const{initial:n}=this.props;let s;if(typeof n=="string"||typeof n=="object"){const r=rn(this.props,n,(o=this.presenceContext)==null?void 0:o.custom);r&&(s=r[e])}if(n&&s!==void 0)return s;const i=this.getBaseTargetFromProps(this.props,e);return i!==void 0&&!k(i)?i:this.initialValues[e]!==void 0&&s===void 0?void 0:this.baseTarget[e]}on(e,n){return this.events[e]||(this.events[e]=new Be),this.events[e].add(n)}notify(e,...n){this.events[e]&&this.events[e].notify(...n)}}class Xi extends Du{constructor(){super(...arguments),this.KeyframeResolver=No}sortInstanceNodePosition(e,n){return e.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(e,n){return e.style?e.style[n]:void 0}removeValueFromRenderState(e,{vars:n,style:s}){delete n[e],delete s[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:e}=this.props;k(e)&&(this.childSubscription=e.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}}function Yi(t,{style:e,vars:n},s,i){Object.assign(t.style,e,i&&i.getProjectionStyles(s));for(const o in n)t.style.setProperty(o,n[o])}function Cu(t){return window.getComputedStyle(t)}class Ru extends Xi{constructor(){super(...arguments),this.type="html",this.renderInstance=Yi}readValueFromInstance(e,n){if(pt.has(n))return so(e,n);{const s=Cu(e),i=(je(n)?s.getPropertyValue(n):s[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(e,{transformPagePoint:n}){return Fi(e,n)}build(e,n,s){en(e,n,s.transformTemplate)}scrapeMotionValuesFromProps(e,n,s){return on(e,n,s)}}const qi=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function Eu(t,e,n,s){Yi(t,e,void 0,s);for(const i in e.attrs)t.setAttribute(qi.has(i)?i:tn(i),e.attrs[i])}class Lu extends Xi{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=C}getBaseTargetFromProps(e,n){return e[n]}readValueFromInstance(e,n){if(pt.has(n)){const s=oi(n);return s&&s.default||0}return n=qi.has(n)?n:tn(n),e.getAttribute(n)}scrapeMotionValuesFromProps(e,n,s){return Ai(e,n,s)}build(e,n,s){xi(e,n,this.isSVGTag,s.transformTemplate,s.style)}renderInstance(e,n,s,i){Eu(e,n,s,i)}mount(e){this.isSVGTag=Pi(e.tagName),super.mount(e)}}const ku=(t,e)=>sn(t)?new Lu(e):new Ru(e,{allowProjection:t!==P.Fragment}),Fu=Ra({...nl,...bu,...fu,...Au},ku),Nu=ea(Fu);export{bs as L,As as M,Re as P,tr as a,Qi as b,Nu as m,Ji as u};
