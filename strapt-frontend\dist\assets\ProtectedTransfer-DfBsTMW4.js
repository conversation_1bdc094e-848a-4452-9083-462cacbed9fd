const e="DEPRECATED: This contract has been replaced by ProtectedTransferV2. This file is kept for reference only.",t="0x225f179c0d57c3DF357f802BB40d5a4BeaFb4F0C",n=[{inputs:[{internalType:"address",name:"initialOwner",type:"address"},{internalType:"address",name:"initialFeeCollector",type:"address"},{internalType:"uint16",name:"initialFeeInBasisPoints",type:"uint16"}],stateMutability:"nonpayable",type:"constructor"},{inputs:[],name:"FeeExceedsMaximum",type:"error"},{inputs:[],name:"InvalidAmount",type:"error"},{inputs:[],name:"InvalidClaimCode",type:"error"},{inputs:[],name:"InvalidExpiryTime",type:"error"},{inputs:[],name:"InvalidFeeCollector",type:"error"},{inputs:[],name:"InvalidTokenAddress",type:"error"},{inputs:[],name:"NotIntendedRecipient",type:"error"},{inputs:[],name:"NotLinkTransfer",type:"error"},{inputs:[],name:"PasswordProtected",type:"error"},{inputs:[],name:"PasswordRequired",type:"error"},{inputs:[],name:"NotOwner",type:"error"},{inputs:[],name:"NotSender",type:"error"},{inputs:[],name:"TokenNotSupported",type:"error"},{inputs:[],name:"TransferAlreadyExists",type:"error"},{inputs:[],name:"TransferDoesNotExist",type:"error"},{inputs:[],name:"TransferExpired",type:"error"},{inputs:[],name:"TransferNotClaimable",type:"error"},{inputs:[],name:"TransferNotRefundable",type:"error"},{anonymous:!1,inputs:[{indexed:!0,internalType:"bytes32",name:"transferId",type:"bytes32"},{indexed:!0,internalType:"address",name:"claimer",type:"address"},{indexed:!1,internalType:"uint256",name:"amount",type:"uint256"}],name:"TransferClaimed",type:"event"},{anonymous:!1,inputs:[{indexed:!0,internalType:"bytes32",name:"transferId",type:"bytes32"},{indexed:!0,internalType:"address",name:"sender",type:"address"},{indexed:!0,internalType:"address",name:"recipient",type:"address"},{indexed:!1,internalType:"address",name:"tokenAddress",type:"address"},{indexed:!1,internalType:"uint256",name:"amount",type:"uint256"},{indexed:!1,internalType:"uint256",name:"expiry",type:"uint256"}],name:"TransferCreated",type:"event"},{anonymous:!1,inputs:[{indexed:!0,internalType:"bytes32",name:"transferId",type:"bytes32"},{indexed:!0,internalType:"address",name:"sender",type:"address"},{indexed:!1,internalType:"uint256",name:"amount",type:"uint256"}],name:"TransferRefunded",type:"event"},{inputs:[{internalType:"bytes32",name:"transferId",type:"bytes32"},{internalType:"string",name:"claimCode",type:"string"}],name:"claimTransfer",outputs:[],stateMutability:"nonpayable",type:"function"},{inputs:[{internalType:"bytes32",name:"transferId",type:"bytes32"}],name:"claimLinkTransfer",outputs:[],stateMutability:"nonpayable",type:"function"},{inputs:[{internalType:"address",name:"recipient",type:"address"},{internalType:"address",name:"tokenAddress",type:"address"},{internalType:"uint256",name:"amount",type:"uint256"},{internalType:"uint256",name:"expiry",type:"uint256"},{internalType:"bytes32",name:"claimCodeHash",type:"bytes32"}],name:"createTransfer",outputs:[{internalType:"bytes32",name:"",type:"bytes32"}],stateMutability:"nonpayable",type:"function"},{inputs:[{internalType:"address",name:"tokenAddress",type:"address"},{internalType:"uint256",name:"amount",type:"uint256"},{internalType:"uint256",name:"expiry",type:"uint256"}],name:"createLinkTransfer",outputs:[{internalType:"bytes32",name:"",type:"bytes32"}],stateMutability:"nonpayable",type:"function"},{inputs:[{internalType:"address",name:"tokenAddress",type:"address"},{internalType:"uint256",name:"amount",type:"uint256"},{internalType:"uint256",name:"expiry",type:"uint256"},{internalType:"bytes32",name:"claimCodeHash",type:"bytes32"}],name:"createProtectedLinkTransfer",outputs:[{internalType:"bytes32",name:"",type:"bytes32"}],stateMutability:"nonpayable",type:"function"},{inputs:[],name:"feeCollector",outputs:[{internalType:"address",name:"",type:"address"}],stateMutability:"view",type:"function"},{inputs:[],name:"feeInBasisPoints",outputs:[{internalType:"uint16",name:"",type:"uint16"}],stateMutability:"view",type:"function"},{inputs:[{internalType:"bytes32",name:"transferId",type:"bytes32"}],name:"getTransfer",outputs:[{internalType:"address",name:"sender",type:"address"},{internalType:"address",name:"recipient",type:"address"},{internalType:"address",name:"tokenAddress",type:"address"},{internalType:"uint256",name:"amount",type:"uint256"},{internalType:"uint256",name:"expiry",type:"uint256"},{internalType:"enum ProtectedTransfer.TransferStatus",name:"status",type:"uint8"},{internalType:"uint256",name:"createdAt",type:"uint256"},{internalType:"bool",name:"isLinkTransfer",type:"bool"}],stateMutability:"view",type:"function"},{inputs:[{internalType:"bytes32",name:"transferId",type:"bytes32"}],name:"isTransferClaimable",outputs:[{internalType:"bool",name:"",type:"bool"}],stateMutability:"view",type:"function"},{inputs:[{internalType:"bytes32",name:"transferId",type:"bytes32"}],name:"isPasswordProtected",outputs:[{internalType:"bool",name:"",type:"bool"}],stateMutability:"view",type:"function"},{inputs:[],name:"owner",outputs:[{internalType:"address",name:"",type:"address"}],stateMutability:"view",type:"function"},{inputs:[{internalType:"bytes32",name:"transferId",type:"bytes32"}],name:"refundTransfer",outputs:[],stateMutability:"nonpayable",type:"function"},{inputs:[{internalType:"address",name:"newFeeCollector",type:"address"}],name:"setFeeCollector",outputs:[],stateMutability:"nonpayable",type:"function"},{inputs:[{internalType:"uint16",name:"newFeeInBasisPoints",type:"uint16"}],name:"setFee",outputs:[],stateMutability:"nonpayable",type:"function"},{inputs:[{internalType:"address",name:"tokenAddress",type:"address"},{internalType:"bool",name:"isSupported",type:"bool"}],name:"setTokenSupport",outputs:[],stateMutability:"nonpayable",type:"function"},{inputs:[{internalType:"address",name:"",type:"address"}],name:"supportedTokens",outputs:[{internalType:"bool",name:"",type:"bool"}],stateMutability:"view",type:"function"},{inputs:[{internalType:"bytes32",name:"",type:"bytes32"}],name:"transfers",outputs:[{internalType:"address",name:"sender",type:"address"},{internalType:"address",name:"recipient",type:"address"},{internalType:"address",name:"tokenAddress",type:"address"},{internalType:"uint256",name:"amount",type:"uint256"},{internalType:"uint256",name:"expiry",type:"uint256"},{internalType:"bytes32",name:"claimCodeHash",type:"bytes32"},{internalType:"enum ProtectedTransfer.TransferStatus",name:"status",type:"uint8"},{internalType:"uint256",name:"createdAt",type:"uint256"},{internalType:"bool",name:"isLinkTransfer",type:"bool"}],stateMutability:"view",type:"function"}],a={_comment:e,address:t,abi:n};export{e as _comment,n as abi,t as address,a as default};
