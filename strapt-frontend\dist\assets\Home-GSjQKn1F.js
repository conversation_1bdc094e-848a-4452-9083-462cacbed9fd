import{k as Q,j as e,t as L,L as ie,D as X,d as z,e as O,f as V,B as u,A as W,aI as re,v as q,i as _,r as d,n as y,o as C,p as R,bx as ne,q as le,$ as ce,s as N,ad as Y,x as $,bw as de,am as oe,ao as me,w as xe,u as he,by as ue,bz as pe,a as je,l as fe,ap as H,bA as ye,C as Ne,F as ve,a1 as ge}from"./index-G17GlXLb.js";import{A as J}from"./arrow-down-left-DZ4sL2ld.js";import{A as E}from"./arrow-up-right-D82LWdhe.js";import{I as we}from"./input-CWM2bTJL.js";import{c as M}from"./confetti.module-BxKCmZ95.js";import{A as F}from"./index-94PVxzUY.js";import{m as l}from"./proxy-B8vTGf2f.js";import{A as be}from"./arrow-right-gM81zKuh.js";import{Q as Ce}from"./QRCode-DVz9xBTO.js";import{S as c,U as Re}from"./skeleton-CLgd6C74.js";import{Q as G}from"./qr-code-BnIF1LDH.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ke=Q("ArrowDownRight",[["path",{d:"m7 7 10 10",key:"1fmybs"}],["path",{d:"M17 7v10H7",key:"6fjiku"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const De=Q("ArrowDown",[["path",{d:"M12 5v14",key:"s699le"}],["path",{d:"m19 12-7 7-7-7",key:"1idqje"}]]);/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const K=Q("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]),P=({icon:r,label:s,to:a,color:t="bg-primary",onClick:i})=>i?e.jsxs("button",{onClick:i,className:L("flex flex-col items-center p-3 rounded-2xl card-hover w-full",t),children:[e.jsx("div",{className:"rounded-full bg-white/20 p-3 mb-2",children:e.jsx(r,{className:"h-6 w-6 text-white"})}),e.jsx("span",{className:"text-xs font-medium text-white",children:s})]}):e.jsxs(ie,{to:a||"#",className:L("flex flex-col items-center p-3 rounded-2xl card-hover",t),children:[e.jsx("div",{className:"rounded-full bg-white/20 p-3 mb-2",children:e.jsx(r,{className:"h-6 w-6 text-white"})}),e.jsx("span",{className:"text-xs font-medium text-white",children:s})]}),Se=({open:r,onClose:s,transaction:a})=>{const{toast:t}=_();if(!a)return null;const i=()=>{switch(a.type){case"sent":return e.jsx(E,{className:"h-6 w-6 text-destructive"});case"received":return e.jsx(J,{className:"h-6 w-6 text-primary"});case"pending":return e.jsx(q,{className:"h-6 w-6 text-amber-500"})}},m=()=>{switch(a.type){case"sent":return"Sent";case"received":return"Received";case"pending":return"Pending"}},x=()=>{switch(a.type){case"sent":return"text-destructive";case"received":return"text-primary";case"pending":return"text-amber-500"}},n=()=>{navigator.clipboard.writeText(a.hash),t({title:"Hash Copied",description:"Transaction hash copied to clipboard"})},o=()=>{window.open(`https://sei.explorers.guru/transaction/${a.hash}`,"_blank")};return e.jsx(X,{open:r,onOpenChange:p=>!p&&s(),children:e.jsxs(z,{className:"sm:max-w-md w-[92%] mx-auto",children:[e.jsx(O,{children:e.jsx(V,{children:"Transaction Details"})}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"rounded-full bg-secondary p-3 mr-3",children:i()}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-xl font-semibold",children:a.title}),e.jsx("p",{className:`${x()} font-medium`,children:m()})]})]}),e.jsx("span",{className:`text-xl font-bold ${x()}`,children:a.amount})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-muted-foreground",children:"Date"}),e.jsx("span",{children:a.date})]}),a.recipient&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-muted-foreground",children:"Recipient"}),e.jsx("span",{children:a.recipient})]}),e.jsxs("div",{className:"space-y-1",children:[e.jsx("span",{className:"text-muted-foreground",children:"Transaction Hash"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("code",{className:"bg-secondary p-2 rounded text-xs block w-full overflow-hidden text-ellipsis",children:a.hash}),e.jsx(u,{variant:"ghost",size:"icon",onClick:n,children:e.jsx(W,{className:"h-4 w-4"})})]})]})]}),e.jsxs(u,{variant:"outline",className:"w-full",onClick:o,children:["View in Explorer",e.jsx(re,{className:"ml-2 h-4 w-4"})]})]})]})})},b=({type:r,title:s,amount:a,date:t,recipient:i,hash:m="sei1tx92h43lta8kdl5z4jmwxz7"})=>{const[x,n]=d.useState(!1),o=()=>{switch(r){case"sent":return e.jsx(E,{className:"h-5 w-5 text-destructive"});case"received":return e.jsx(J,{className:"h-5 w-5 text-primary"});case"pending":return e.jsx(q,{className:"h-5 w-5 text-amber-500"})}},p=()=>{switch(r){case"sent":return"text-destructive";case"received":return"text-primary";case"pending":return"text-amber-500"}};return e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"flex items-center justify-between p-3 rounded-xl hover:bg-secondary/50 transition-colors cursor-pointer",onClick:()=>n(!0),children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"rounded-full bg-secondary p-2 mr-3",children:o()}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium",children:s}),e.jsx("p",{className:"text-xs text-muted-foreground",children:i||t})]})]}),e.jsx("span",{className:L("font-semibold",p()),children:a})]}),e.jsx(Se,{open:x,onClose:()=>n(!1),transaction:{type:r,title:s,amount:a,date:t,recipient:i,hash:m}})]})},Ae=({onComplete:r})=>{var B;const[s,a]=d.useState(""),[t,i]=d.useState(!1),[m,x]=d.useState(null),[n,o]=d.useState(1),[p,I]=d.useState(0),[k,D]=d.useState(!1),j=d.useRef(null),{toast:v}=_();d.useEffect(()=>{n===1&&j.current&&j.current.focus()},[n]);const S=async f=>{i(!0),await new Promise(g=>setTimeout(g,1e3));const h=f.length>=3&&!["admin","system","truststream"].includes(f.toLowerCase());return i(!1),x(h),h},U=f=>{const h=f.target.value.trim().toLowerCase();if(a(h),x(null),h.length>=3){const g=setTimeout(()=>{S(h)},500);return()=>clearTimeout(g)}},Z=async f=>{if(f.preventDefault(),!s||s.length<3){v({title:"Invalid Username",description:"Username must be at least 3 characters",variant:"destructive"});return}i(!0),await S(s)?(i(!1),ee()):(v({title:"Username Unavailable",description:"Please choose another username",variant:"destructive"}),i(!1))},ee=()=>{I(100),o(2),setTimeout(()=>{D(!0),te()},300)},se=()=>{v({title:"Registration Complete",description:`@${s}.strapt is now yours!`}),r()},te=()=>{const h=Date.now()+3e3,g={startVelocity:30,spread:360,ticks:60,zIndex:0};function A(w,T){return Math.random()*(T-w)+w}const ae=setInterval(()=>{const w=h-Date.now();if(w<=0)return clearInterval(ae);const T=50*(w/3e3);M({...g,particleCount:T,origin:{x:A(.1,.3),y:A(0,.2)}}),M({...g,particleCount:T,origin:{x:A(.7,.9),y:A(0,.2)}})},250)};return e.jsxs(y,{className:"border-primary/20 shadow-lg overflow-hidden",children:[e.jsxs(C,{className:"relative pb-2",children:[e.jsxs(R,{className:"text-xl flex items-center gap-2",children:[e.jsx(ne,{className:"h-5 w-5 text-primary"}),n===1?"Choose Your Username":"Registration Complete"]}),e.jsx(le,{children:n===1?"Create your unique identity on STRAPT":"Your on-chain identity is ready"}),e.jsx(ce,{value:p,className:"h-1 mt-4 bg-primary/10"})]}),e.jsx(N,{className:"pt-4",children:e.jsxs(F,{mode:"wait",children:[n===1&&e.jsx(l.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},transition:{duration:.3},children:e.jsxs("form",{onSubmit:Z,className:"space-y-4",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsxs("div",{className:"relative",children:[e.jsx(we,{ref:j,placeholder:"username",value:s,onChange:U,className:"pr-20 h-12 text-base border-primary/20 focus:border-primary"}),e.jsx("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-sm font-medium text-primary",children:".strapt"})]}),e.jsx(F,{children:s&&s.length>=3&&e.jsx(l.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},className:"flex items-center text-sm mt-2",children:t?e.jsxs(e.Fragment,{children:[e.jsx(Y,{className:"h-3 w-3 animate-spin mr-1"}),e.jsx("span",{className:"text-muted-foreground",children:"Checking availability..."})]}):m===!0?e.jsxs(e.Fragment,{children:[e.jsx($,{className:"h-3 w-3 text-green-500 mr-1"}),e.jsx("span",{className:"text-green-500",children:"Username available!"})]}):m===!1?e.jsxs(e.Fragment,{children:[e.jsx(de,{className:"h-3 w-3 text-destructive mr-1"}),e.jsx("span",{className:"text-destructive",children:"Username unavailable"})]}):null})}),e.jsx("p",{className:"text-xs text-muted-foreground mt-1",children:"Choose a username for your wallet. This will be your public identity on STRAPT."})]}),e.jsxs("div",{className:"space-y-1 bg-secondary/30 p-3 rounded-md",children:[e.jsx("p",{className:"text-sm font-medium",children:"Username Requirements:"}),e.jsxs("ul",{className:"text-xs text-muted-foreground list-disc pl-5 space-y-1",children:[e.jsx("li",{className:s.length>=3?"text-green-500":"",children:"At least 3 characters"}),e.jsx("li",{className:/^[a-z0-9_]+$/.test(s)?"text-green-500":"",children:"Only lowercase letters, numbers and underscores"}),e.jsx("li",{children:"Cannot be changed later"})]})]}),e.jsx(u,{type:"submit",className:"w-full h-12 text-base",disabled:t||!s||s.length<3||m===!1,children:t?e.jsxs(e.Fragment,{children:[e.jsx(Y,{className:"mr-2 h-4 w-4 animate-spin"}),"Checking"]}):e.jsxs(e.Fragment,{children:["Continue ",e.jsx(be,{className:"ml-2 h-4 w-4"})]})})]})},"step1"),n===2&&e.jsxs(l.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},transition:{duration:.3},className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(l.div,{initial:{scale:0},animate:{scale:[0,1.2,1]},transition:{duration:.5,times:[0,.7,1]},className:"mx-auto",children:e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-0 bg-gradient-to-r from-primary to-accent rounded-full animate-pulse blur-md opacity-50"}),e.jsx(oe,{className:"h-24 w-24 mx-auto border-4 border-primary relative",children:e.jsx(me,{className:"text-2xl bg-gradient-to-br from-primary/80 to-accent/80 text-white font-bold",children:(B=s[0])==null?void 0:B.toUpperCase()})})]})}),e.jsxs(l.h3,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.3},className:"text-xl font-medium mt-4",children:["@",s,".strapt"]}),e.jsx(l.p,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},className:"text-sm text-muted-foreground mt-2",children:"Your username is now registered and ready to use"})]}),e.jsxs(l.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.7},className:"space-y-1 bg-secondary/30 p-4 rounded-md",children:[e.jsx("p",{className:"text-sm font-medium",children:"What happens next:"}),e.jsxs("ul",{className:"text-xs text-muted-foreground list-disc pl-5 space-y-2",children:[e.jsx("li",{children:"Your username will be linked to your current wallet address"}),e.jsx("li",{children:"You can use this username to send and receive payments"}),e.jsxs("li",{children:["Others can find you using @",s,".strapt"]})]})]})]},"step2")]})}),n===2&&e.jsx(xe,{className:"pt-2 pb-6",children:e.jsx(l.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.9},className:"w-full",whileHover:{scale:1.02},whileTap:{scale:.98},children:e.jsx(u,{className:"w-full h-12 text-base bg-gradient-to-r from-primary to-accent hover:opacity-90 transition-all",onClick:se,children:"Start Using STRAPT"})})}),e.jsx(F,{children:k&&e.jsx(l.div,{className:"fixed inset-0 flex items-center justify-center z-50 bg-black/50 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:e.jsxs(l.div,{className:"bg-card rounded-xl p-8 flex flex-col items-center max-w-md mx-4",initial:{scale:.8,y:20,opacity:0},animate:{scale:1,y:0,opacity:1},exit:{scale:.8,y:20,opacity:0},transition:{type:"spring",damping:15},children:[e.jsx(l.div,{initial:{scale:0},animate:{scale:[0,1.2,1]},transition:{duration:.5,times:[0,.7,1]},children:e.jsx("div",{className:"w-20 h-20 rounded-full bg-gradient-to-br from-primary to-green-500 flex items-center justify-center mb-4 animate-pulse shadow-lg",children:e.jsx($,{className:"h-10 w-10 text-white"})})}),e.jsx(l.h2,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.2},className:"text-xl font-bold mb-2",children:"Username Registered!"}),e.jsxs(l.p,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.4},className:"text-center text-muted-foreground mb-6",children:["@",s,".strapt is now yours"]}),e.jsx(l.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.6},children:e.jsx(u,{onClick:()=>D(!1),className:"px-8 bg-gradient-to-r from-primary to-accent hover:opacity-90 transition-all",children:"Continue"})})]})})})]})},Te=({totalReceived:r,recentActivity:s})=>{const a=s.filter(t=>t.direction==="in"&&t.date>=new Date(Date.now()-6048e5)).reduce((t,i)=>t+i.amount,0);return e.jsxs(y,{children:[e.jsx(C,{className:"pb-2",children:e.jsxs(R,{className:"text-lg flex items-center",children:[e.jsx(K,{className:"h-5 w-5 mr-2 text-primary"}),"Funds Received ",e.jsx("span",{className:"ml-3 text-xs text-amber-500 font-normal",children:"(dummy data)"})]})}),e.jsx(N,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-secondary/20 p-3 rounded-lg",children:[e.jsx("div",{className:"text-sm text-muted-foreground",children:"Total Received"}),e.jsxs("div",{className:"text-2xl font-semibold",children:[r.toFixed(2)," IDRX"]})]}),e.jsxs("div",{className:"bg-secondary/20 p-3 rounded-lg",children:[e.jsx("div",{className:"text-sm text-muted-foreground",children:"Last 7 Days"}),e.jsxs("div",{className:"text-2xl font-semibold",children:[a.toFixed(2)," IDRX"]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium mb-2",children:"Recent Transactions"}),e.jsx("div",{className:"space-y-2",children:s.slice(0,4).map((t,i)=>e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsxs("div",{className:"flex items-center",children:[t.direction==="in"?e.jsx(ke,{className:"h-4 w-4 mr-2 text-green-500"}):e.jsx(E,{className:"h-4 w-4 mr-2 text-amber-500"}),e.jsx("span",{children:t.direction==="in"?"Received":"Sent"})]}),e.jsxs("div",{className:t.direction==="in"?"text-green-500":"text-amber-500",children:[t.direction==="in"?"+":"-",t.amount.toFixed(2)," IDRX"]})]},i))})]})]})})]})},Ie=()=>e.jsxs(y,{className:"overflow-hidden dark:border-primary/20 border-primary/30",children:[e.jsx(C,{className:"bg-gradient-to-r from-primary to-accent text-white",children:e.jsxs(R,{className:"text-xl text-white flex items-center justify-between",children:["Your Balance",e.jsxs(u,{size:"sm",variant:"ghost",className:"text-white h-7 hover:bg-white/20",disabled:!0,children:[e.jsx(G,{className:"h-4 w-4 mr-1"})," Receive"]})]})}),e.jsxs(N,{className:"pt-6",children:[e.jsx("div",{className:"text-center",children:e.jsx("div",{className:"space-y-4",children:e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-center gap-2 mb-2",children:[e.jsx(c,{className:"w-8 h-8 rounded-full"}),e.jsx(c,{className:"h-8 w-32"})]}),e.jsx(c,{className:"h-4 w-24 mx-auto"})]})})}),e.jsx("div",{className:"flex justify-center gap-4 mt-4",children:e.jsx(c,{className:"h-9 w-24"})})]})]}),Ue=()=>e.jsxs(y,{children:[e.jsx(C,{className:"pb-2",children:e.jsxs(R,{className:"text-lg flex items-center",children:[e.jsx(K,{className:"h-5 w-5 mr-2 text-primary"}),"Funds Received"]})}),e.jsx(N,{children:e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsxs("div",{className:"bg-secondary/20 p-3 rounded-lg",children:[e.jsx("div",{className:"text-sm text-muted-foreground",children:"Total Received"}),e.jsx(c,{className:"h-8 w-24 mt-1"})]}),e.jsxs("div",{className:"bg-secondary/20 p-3 rounded-lg",children:[e.jsx("div",{className:"text-sm text-muted-foreground",children:"Last 7 Days"}),e.jsx(c,{className:"h-8 w-24 mt-1"})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium mb-2",children:"Recent Transactions"}),e.jsx("div",{className:"space-y-2",children:Array(4).fill(0).map((r,s)=>e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(c,{className:"h-4 w-4 mr-2 rounded-full"}),e.jsx(c,{className:"h-4 w-16"})]}),e.jsx(c,{className:"h-4 w-20"})]},s))})]})]})})]}),Fe=()=>e.jsxs("div",{className:"flex items-center justify-between p-3 rounded-xl",children:[e.jsxs("div",{className:"flex items-center",children:[e.jsx(c,{className:"rounded-full h-9 w-9 mr-3"}),e.jsxs("div",{children:[e.jsx(c,{className:"h-5 w-24 mb-1"}),e.jsx(c,{className:"h-3 w-16"})]})]}),e.jsx(c,{className:"h-5 w-16"})]}),Pe=()=>e.jsxs("div",{className:"space-y-3",children:[e.jsx("div",{className:"flex justify-between items-center",children:e.jsx(c,{className:"h-6 w-32"})}),e.jsx(y,{className:"dark:border-primary/20 border-primary/30",children:e.jsx(N,{className:"p-0",children:Array(5).fill(0).map((r,s)=>e.jsx(Fe,{},s))})})]}),Ve=()=>{const{isConnected:r,address:s}=he(),[a,t]=d.useState({usdc:1,idrx:65e-6}),i=ue(),m=pe(),{tokens:x,isLoading:n,idrxBalance:o}=je();m.chains.find(U=>U.id===i),d.useEffect(()=>{t({usdc:1,idrx:65e-6})},[]);const p=fe(),[I,k]=d.useState(!1),[D,j]=d.useState(!1),v={totalReceived:385.28,recentActivity:[{amount:10.5,direction:"in",date:new Date(Date.now()-8*60*60*1e3)},{amount:125,direction:"out",date:new Date(Date.now()-2*60*60*1e3)},{amount:200,direction:"in",date:new Date(Date.now()-2*24*60*60*1e3)},{amount:50,direction:"in",date:new Date(Date.now()-5*24*60*60*1e3)},{amount:75,direction:"out",date:new Date(Date.now()-24*60*60*1e3)}]},S=()=>{j(!1)};return e.jsxs("div",{className:"space-y-6",children:[n?e.jsx(Ie,{}):e.jsxs(y,{className:"overflow-hidden dark:border-primary/20 border-primary/30",children:[e.jsx(C,{className:"bg-gradient-to-r from-primary to-accent text-white",children:e.jsxs(R,{className:"text-xl text-white flex items-center justify-between",children:["Your Balance",e.jsxs(u,{size:"sm",variant:"ghost",className:"text-white h-7 hover:bg-white/20",onClick:()=>k(!0),children:[e.jsx(G,{className:"h-4 w-4 mr-1"})," Receive"]})]})}),e.jsxs(N,{className:"pt-6",children:[e.jsx("div",{className:"text-center",children:r?x.length>0?e.jsx("div",{className:"space-y-4",children:o&&e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-center gap-2 mb-2",children:[e.jsx("img",{src:"/IDRX BLUE COIN.svg",alt:"IDRX",className:"w-8 h-8"}),e.jsx("div",{className:"text-2xl font-bold",children:H(o.value,o.symbol)})]}),e.jsxs("div",{className:"text-sm text-muted-foreground",children:["≈ $",(Number(H(o.value))*a.idrx).toFixed(2)," USD"]})]})}):e.jsx("div",{className:"text-sm text-muted-foreground",children:"No tokens found"}):e.jsx("div",{className:"text-sm text-muted-foreground",children:"Connect wallet to view balance"})}),e.jsx("div",{className:"flex justify-center gap-4 mt-4",children:e.jsxs(u,{variant:"secondary",className:"flex items-center gap-2 rounded-xl",onClick:()=>p("/app/claims"),children:[e.jsx(De,{className:"h-4 w-4"})," Claims"]})})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("h2",{className:"font-semibold text-lg",children:"Quick Actions"}),e.jsxs("div",{className:"grid grid-cols-3 gap-3 text-center",children:[e.jsx(P,{icon:ye,label:"Send",to:"/app/transfer",color:"bg-gradient-to-br from-primary to-accent"}),e.jsx(P,{icon:Ne,label:"Stream Payment",to:"/app/streams",color:"bg-gradient-to-br from-blue-500 to-cyan-400"}),e.jsx(P,{icon:Re,label:"Set your STRAPT ID",onClick:()=>j(!0),color:"bg-gradient-to-br from-emerald-500 to-green-400"})]})]}),n?e.jsx(Ue,{}):e.jsx(Te,{totalReceived:v.totalReceived,recentActivity:v.recentActivity}),n?e.jsx(Pe,{}):e.jsxs("div",{className:"space-y-3",children:[e.jsxs("h2",{className:"font-semibold text-lg",children:["Recent Activity ",e.jsx("span",{className:"ml-3 text-xs text-amber-500 font-normal",children:"(dummy data)"})]}),e.jsx(y,{className:"dark:border-primary/20 border-primary/30",children:e.jsxs(N,{className:"p-0",children:[e.jsx(b,{type:"sent",title:"Sent to Mark",amount:"-125 IDRX",date:"2 hrs ago",recipient:"@mark.strapt"}),e.jsx(b,{type:"pending",title:"Protected Transfer",amount:"50 IDRX",date:"5 hrs ago",recipient:"@alice.strapt"}),e.jsx(b,{type:"received",title:"Received from Stream",amount:"+10.5 IDRX",date:"8 hrs ago"}),e.jsx(b,{type:"sent",title:"Pool Contribution",amount:"-75 IDRX",date:"1 day ago",recipient:"Trip Fund"}),e.jsx(b,{type:"received",title:"Received from John",amount:"+200 IDRX",date:"2 days ago",recipient:"@john.strapt"})]})})]}),e.jsx(X,{open:I,onOpenChange:k,children:e.jsxs(z,{children:[e.jsx(O,{children:e.jsx(V,{children:"Your Wallet QR Code"})}),e.jsxs("div",{className:"flex flex-col items-center justify-center space-y-4",children:[e.jsx(Ce,{value:s||"",size:200}),e.jsx("p",{className:"text-sm font-medium",children:"Your Wallet Address"}),e.jsx("p",{className:"text-xs text-muted-foreground",children:s?`${s.slice(0,6)}...${s.slice(-4)}`:"Not connected"}),e.jsxs("div",{className:"flex flex-col w-full gap-2",children:[e.jsxs(u,{variant:"outline",onClick:()=>{s&&(navigator.clipboard.writeText(s),ve.success("Address copied to clipboard"))},disabled:!s,children:[e.jsx(W,{className:"h-4 w-4 mr-1"})," Copy Address"]}),e.jsx(ge,{buttonVariant:"outline",buttonText:"Scan QR Code to Claim"})]})]})]})}),e.jsx(X,{open:D,onOpenChange:j,children:e.jsx(z,{className:"p-0 overflow-hidden max-w-md",children:e.jsx(Ae,{onComplete:S})})})]})};export{Ve as default};
