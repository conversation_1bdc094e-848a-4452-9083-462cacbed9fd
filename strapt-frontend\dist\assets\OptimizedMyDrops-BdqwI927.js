import{k as ce,r as a,u as Z,a as le,J as q,O as de,M as S,N as W,F as H,K as me,P as pe,Q as xe,R as ue,l as ee,j as e,n as O,o as he,p as fe,q as ge,s as _,aq as Y,v as je,w as ye,B as h,x as G,a3 as we,i as se,m as Ne,_ as be,c as De,D as ve,d as Ce,e as ke,f as Re,g as Te}from"./index-G17GlXLb.js";import F from"./StraptDrop-DBtBqCxY.js";import{Q as Ae}from"./QRCode-DVz9xBTO.js";import{m as D}from"./proxy-B8vTGf2f.js";import{G as L}from"./gift-Cgh4t_YK.js";import{S as Se,C as Ee}from"./shuffle-CGODj-xY.js";import{S as te}from"./share-2-DrfYo1nq.js";import{Q as Le}from"./qr-code-BnIF1LDH.js";import{T as re}from"./triangle-alert-C7Etu4yh.js";import{C as Pe}from"./chevron-left-CRB4yH4j.js";import{A as Ie}from"./index-94PVxzUY.js";/**
 * @license lucide-react v0.462.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ae=ce("RefreshCcw",[["path",{d:"M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"14sxne"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16",key:"1hlbsb"}],["path",{d:"M16 16h5v5",key:"ccwih5"}]]),X=q.StraptDrop.address,J=q.StraptDrop.supportedTokens.USDC,K=q.StraptDrop.supportedTokens.IDRX,B=new Map,Ue=6e4;function Fe(){const[t,s]=a.useState({isLoading:!1,isApproving:!1,isCreating:!1,isClaiming:!1,isRefunding:!1,isLoadingUserDrops:!1,currentDropId:null}),[l,p]=a.useState([]),[f,v]=a.useState({symbols:{},decimals:{}}),{address:x,isConnected:g}=Z(),{tokens:C}=le(),d=a.useCallback(n=>{s(m=>({...m,...n}))},[]),k=a.useCallback(n=>{switch(n){case"USDC":return J;case"IDRX":return K;default:throw new Error(`Unsupported token type: ${n}`)}},[]),N=a.useCallback(n=>{switch(n){case"USDC":return 6;case"IDRX":return 2;default:return 18}},[]),b=a.useCallback(async n=>{const m=Date.now(),w=B.get(n);if(w&&m-w.timestamp<Ue)return w.info;try{const r=await de(S,{address:X,abi:F.abi,functionName:"getDropInfo",args:[n]}),T={creator:r[0],tokenAddress:r[1],totalAmount:r[2],remainingAmount:r[3],claimedCount:r[4],totalRecipients:r[5],isRandom:r[6],expiryTime:r[7],message:r[8],isActive:r[9],amountPerRecipient:r[6]?0n:r[2]/r[5]};return B.set(n,{info:T,timestamp:m}),T}catch(r){throw console.error("Error getting drop info:",r),r}},[]),y=a.useCallback(async()=>{try{if(d({isLoadingUserDrops:!0}),!g||!x)throw console.error("No wallet connected"),new Error("No wallet connected");console.log("Fetching drops created by:",x);const w=await fetch(`https://sepolia-blockscout.lisk.com/api/v2/addresses/${X}/logs?topic0=0x7d84a6263ae0d98d3329bd7b46bb4e8d6f98cd35a7adb45c274c8b7fd5ebd5e0`);if(!w.ok)throw new Error("Failed to fetch events from Blockscout API");const T=(await w.json()).items.filter(c=>{try{const j=W({abi:F.abi,data:c.data,topics:c.topics});return j.eventName==="DropCreated"?j.args.creator.toLowerCase()===x.toLowerCase():!1}catch{return!1}}),z=(await Promise.all(T.map(async c=>{try{const U=W({abi:F.abi,data:c.data,topics:c.topics}).args.dropId,oe=await b(U);return{id:U,info:oe}}catch(j){return console.error("Error processing drop event:",j),null}}))).filter(c=>c!==null),P={},I={};for(const c of z){const j=c.info.tokenAddress.toLowerCase(),$=K.toLowerCase(),U=J.toLowerCase();j===$?(P[c.id]="IDRX",I[c.id]=2):j===U?(P[c.id]="USDC",I[c.id]=6):(P[c.id]="Token",I[c.id]=18)}return p(z),v({symbols:P,decimals:I}),z}catch(n){return console.error("Error getting user created drops:",n),[]}finally{d({isLoadingUserDrops:!1})}},[x,g,b,d]),R=a.useCallback(async n=>{try{if(d({isLoading:!0,isRefunding:!0}),!g||!x)throw H.error("Please connect your wallet"),new Error("No wallet connected");H.info("Refunding expired STRAPT Drop...");const m=me(S);if(!m||!m.address)throw new Error("No wallet connected");const{request:w}=await pe(S,{address:X,abi:F.abi,functionName:"refundExpiredDrop",args:[n],account:m.address}),r=await xe(S,w),T=await ue(S,{hash:r});return B.delete(n),H.success("Successfully refunded expired STRAPT Drop"),T}catch(m){throw console.error("Error refunding drop:",m),m}finally{d({isLoading:!1,isRefunding:!1})}},[x,g,d]),{isLoading:A,isApproving:u,isCreating:Q,isClaiming:M,isRefunding:i,isLoadingUserDrops:o,currentDropId:ne}=t;return{isLoading:A,isApproving:u,isCreating:Q,isClaiming:M,isRefunding:i,isLoadingUserDrops:o,currentDropId:ne,userDrops:l,tokenMetadata:f,getUserCreatedDrops:y,refundExpiredDrop:R,getDropInfo:b,getTokenAddress:k,getTokenDecimals:N,tokens:C}}const Qe=t=>{const s=Number(t);if(s<=0)return"No expiration";const l=new Date(s*1e3),p=new Date;if(l<=p)return"Expired";const f=Math.floor((l.getTime()-p.getTime())/(1e3*60*60));return f<1?"Expires in less than an hour":f===1?"Expires in 1 hour":`Expires in ${f} hours`},E=t=>{const s=Number(t);return s>0&&s*1e3<Date.now()},V=(t,s)=>we(t,s),Me=t=>t.isActive&&E(t.expiryTime)&&t.remainingAmount>0n,ie=a.memo(({id:t,info:s,tokenSymbol:l,tokenDecimals:p,onRefund:f,onShowQR:v,isRefunding:x,index:g})=>{const C=ee(),{toast:d}=se(),[k,N]=a.useState(!1),b=async()=>{try{N(!0),await f(t)}finally{N(!1)}},y=()=>Ne(t),R=()=>{const u=y();navigator.clipboard.writeText(u).then(()=>d.success("Link copied to clipboard")).catch(()=>d.error("Failed to copy link"))},A=()=>{const u=y();v(u)};return e.jsx(D.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,scale:.95},transition:{delay:g*.05},whileHover:{y:-5},children:e.jsxs(O,{className:`h-full flex flex-col ${s.isActive?"dark:border-primary/20 dark:bg-black/40 border-primary/30 bg-primary/5 shadow-md":"dark:opacity-80 dark:border-muted/50 dark:bg-black/20 opacity-90 border-muted/30 bg-muted/10"}`,children:[e.jsxs(he,{className:"pb-2 px-4 pt-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(L,{className:"h-5 w-5 text-primary"}),e.jsx(fe,{className:"text-base",children:s.isActive?"Active Drop":"Inactive Drop"})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("span",{className:"text-xs font-medium",children:s.isActive&&!E(s.expiryTime)?"Active":"Inactive"}),e.jsx("div",{className:`w-2 h-2 rounded-full ${s.isActive&&!E(s.expiryTime)?"bg-green-500":"bg-red-500"}`})]})]}),e.jsxs(ge,{className:"text-xs mt-1",children:["Created ",new Date(Number(s.expiryTime)*1e3-24*60*60*1e3).toLocaleDateString()]})]}),e.jsxs(_,{className:"space-y-4 pb-2 px-4 flex-grow",children:[e.jsxs("div",{children:[e.jsxs("p",{className:"font-bold text-xl",children:[V(s.totalAmount,p)," ",l||"tokens"]}),e.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[e.jsxs("div",{className:"flex items-center text-xs text-muted-foreground",children:[e.jsx(Y,{className:"h-3 w-3 mr-1"}),s.totalRecipients.toString()," recipients"]}),e.jsx("div",{className:"flex items-center text-xs",children:s.isRandom?e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"text-amber-500 mr-1",children:"•"}),e.jsx(Se,{className:"h-3 w-3 mr-1 text-amber-500"}),e.jsx("span",{className:"text-amber-500",children:"Random"})]}):e.jsxs(e.Fragment,{children:[e.jsx("span",{className:"text-blue-500 mr-1",children:"•"}),e.jsx(Ee,{className:"h-3 w-3 mr-1 text-blue-500"}),e.jsx("span",{className:"text-blue-500",children:"Fixed"})]})})]})]}),e.jsxs("div",{className:"grid grid-cols-2 gap-3",children:[e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("div",{className:"text-xs text-muted-foreground flex items-center gap-1 mb-1",children:[e.jsx(je,{className:"h-3 w-3"})," Expiry"]}),e.jsx("div",{className:`text-xs font-medium ${E(s.expiryTime)?"text-destructive":""}`,children:Qe(s.expiryTime)})]}),e.jsxs("div",{className:"flex flex-col",children:[e.jsxs("div",{className:"text-xs text-muted-foreground flex items-center gap-1 mb-1",children:[e.jsx(Y,{className:"h-3 w-3"})," Claims"]}),e.jsxs("div",{className:"text-xs font-medium",children:[s.claimedCount.toString()," / ",s.totalRecipients.toString()]})]})]}),e.jsxs("div",{children:[e.jsx("div",{className:"text-xs text-muted-foreground mb-1",children:"Remaining"}),e.jsxs("div",{className:"text-xs font-medium",children:[V(s.remainingAmount,p)," ",l||"tokens"]})]})]}),e.jsx(ye,{className:"px-4 pt-2 pb-4",children:Me(s)?e.jsx(D.div,{className:"w-full",whileHover:{scale:1.02},whileTap:{scale:.98},children:e.jsx(h,{className:"w-full",onClick:b,disabled:k||x,children:k||x?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"h-4 w-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent"}),"Refunding..."]}):e.jsxs(e.Fragment,{children:[e.jsx(ae,{className:"h-4 w-4 mr-2"}),"Refund Expired Drop"]})})}):s.isActive&&!E(s.expiryTime)?e.jsxs("div",{className:"flex gap-2 w-full",children:[e.jsxs(h,{className:"flex-1",variant:"outline",size:"sm",onClick:R,children:[e.jsx(te,{className:"h-4 w-4 mr-2"}),"Copy Link"]}),e.jsx(h,{variant:"outline",size:"sm",onClick:A,children:e.jsx(Le,{className:"h-4 w-4"})}),e.jsx(h,{variant:"outline",size:"sm",onClick:()=>C(`/app/strapt-drop/claim?id=${t}`),children:e.jsx(L,{className:"h-4 w-4"})})]}):!s.isActive&&s.claimedCount>=s.totalRecipients?e.jsxs("div",{className:"w-full flex items-center justify-center text-sm text-muted-foreground py-2 bg-muted/20 rounded-lg",children:[e.jsx(G,{className:"h-4 w-4 mr-2 text-green-500"})," All tokens claimed"]}):!s.isActive&&Number(s.remainingAmount)===0?e.jsxs("div",{className:"w-full flex items-center justify-center text-sm text-muted-foreground py-2 bg-muted/20 rounded-lg",children:[e.jsx(G,{className:"h-4 w-4 mr-2 text-green-500"})," Drop refunded"]}):e.jsxs("div",{className:"w-full flex items-center justify-center text-sm text-muted-foreground py-2 bg-muted/20 rounded-lg",children:[e.jsx(re,{className:"h-4 w-4 mr-2 text-amber-500"})," Drop inactive"]})})]})})});ie.displayName="DropCard";const Ke=()=>{const t=ee(),{toast:s}=se(),{isConnected:l,address:p}=Z(),{getUserCreatedDrops:f,refundExpiredDrop:v,isRefunding:x,isLoadingUserDrops:g,userDrops:C,tokenMetadata:d}=Fe(),[k,N]=a.useState({}),[b,y]=a.useState(!1),[R,A]=a.useState("");a.useEffect(()=>{l&&p&&u()},[l,p]);const u=async()=>{if(!l||!p){s({title:"Wallet Not Connected",description:"Please connect your wallet to view your drops"});return}try{await f()}catch(i){console.error("Error loading user drops:",i),s({title:"Error",description:"Failed to load your drops"})}},Q=a.useCallback(async i=>{if(!l){s({title:"Wallet Not Connected",description:"Please connect your wallet to refund this drop"});return}try{N(o=>({...o,[i]:!0})),await v(i),s({title:"Success",description:"Successfully refunded drop"}),await u()}catch(o){console.error("Error refunding drop:",o),o instanceof Error?o.message.includes("NotExpiredYet")?s({title:"Error",description:"This drop has not expired yet"}):o.message.includes("NotCreator")?s({title:"Error",description:"Only the creator can refund this drop"}):o.message.includes("DropNotActive")?s({title:"Error",description:"This drop is not active"}):o.message.includes("DropNotFound")?(s({title:"Error",description:"This drop does not exist"}),await u()):s({title:"Error",description:`Error refunding drop: ${o.message}`}):s({title:"Error",description:"An unknown error occurred while refunding the drop"})}finally{N(o=>({...o,[i]:!1}))}},[l,v,s,u]),M=a.useCallback(i=>{A(i),y(!0)},[]);return e.jsxs("div",{className:"container max-w-4xl mx-auto py-4 px-4 sm:px-6 sm:py-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 mb-6",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(h,{variant:"ghost",size:"sm",onClick:()=>t("/app/strapt-drop"),className:"mr-1 p-2","aria-label":"Back to STRAPT Drop",children:e.jsx(Pe,{className:"h-5 w-5"})}),e.jsx("h1",{className:"text-xl sm:text-2xl font-bold",children:"My STRAPT Drops"}),e.jsx(be,{content:e.jsxs("div",{children:[e.jsx("p",{className:"font-medium mb-1",children:"About My STRAPT Drops"}),e.jsx("p",{className:"mb-1",children:"View and manage your created STRAPT Drops."}),e.jsxs("ul",{className:"list-disc pl-4 text-xs space-y-1",children:[e.jsx("li",{children:"See all drops you've created"}),e.jsx("li",{children:"Refund expired drops that haven't been fully claimed"}),e.jsx("li",{children:"Track claim status of your drops"})]})]})})]}),e.jsxs("div",{className:"flex gap-2 w-full sm:w-auto",children:[e.jsxs(h,{variant:"outline",size:"sm",onClick:u,disabled:g,className:"flex-1 sm:flex-none",children:[e.jsx(ae,{className:"h-4 w-4 mr-2"}),"Refresh"]}),e.jsxs(h,{size:"sm",onClick:()=>t("/app/strapt-drop"),className:"flex-1 sm:flex-none",children:[e.jsx(L,{className:"h-4 w-4 mr-2"}),"Create New Drop"]})]})]}),l?g?e.jsxs(D.div,{initial:{opacity:0},animate:{opacity:1},className:"flex flex-col items-center justify-center py-16 sm:py-20",children:[e.jsx(De,{size:"lg"}),e.jsx("p",{className:"mt-4 text-muted-foreground animate-pulse",children:"Loading your STRAPT Drops..."})]}):C.length===0?e.jsx(D.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:e.jsx(O,{className:"border-2 border-muted",children:e.jsxs(_,{className:"flex flex-col items-center justify-center py-8 sm:py-10 px-4 sm:px-6 text-center",children:[e.jsx(L,{className:"h-12 w-12 sm:h-16 sm:w-16 text-primary/70 mb-4 sm:mb-6"}),e.jsx("p",{className:"text-lg sm:text-xl font-medium mb-2",children:"No STRAPT Drops Found"}),e.jsx("p",{className:"text-sm sm:text-base text-muted-foreground mb-6",children:"You haven't created any STRAPT Drops yet"}),e.jsx(D.div,{whileHover:{scale:1.05},whileTap:{scale:.95},children:e.jsxs(h,{onClick:()=>t("/app/strapt-drop"),size:"lg",className:"px-8",children:[e.jsx(L,{className:"h-4 w-4 mr-2"}),"Create Your First Drop"]})})]})})}):e.jsx(D.div,{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",initial:{opacity:0},animate:{opacity:1},transition:{staggerChildren:.1},children:e.jsx(Ie,{children:C.map((i,o)=>e.jsx(ie,{id:i.id,info:i.info,tokenSymbol:d.symbols[i.id]||"Token",tokenDecimals:d.decimals[i.id]||18,onRefund:Q,onShowQR:M,isRefunding:x||!!k[i.id],index:o},i.id))})}):e.jsx(D.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:e.jsx(O,{className:"border-2 border-muted",children:e.jsxs(_,{className:"flex flex-col items-center justify-center py-8 sm:py-10 px-4 sm:px-6 text-center",children:[e.jsx(re,{className:"h-12 w-12 sm:h-16 sm:w-16 text-muted-foreground mb-4 sm:mb-6"}),e.jsx("p",{className:"text-lg sm:text-xl font-medium mb-2",children:"Wallet Not Connected"}),e.jsx("p",{className:"text-sm sm:text-base text-muted-foreground mb-6",children:"Please connect your wallet to view your STRAPT Drops"}),e.jsx(h,{size:"lg",className:"px-8",children:"Connect Wallet"})]})})}),e.jsx(ve,{open:b,onOpenChange:y,children:e.jsxs(Ce,{className:"sm:max-w-md",children:[e.jsxs(ke,{children:[e.jsx(Re,{children:"STRAPT Drop QR Code"}),e.jsx(Te,{children:"Share this QR code to let recipients claim tokens from your STRAPT Drop"})]}),e.jsx("div",{className:"flex justify-center py-4",children:e.jsx(Ae,{value:R,size:250,renderAsImage:!0})}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx(h,{variant:"outline",onClick:()=>y(!1),children:"Close"}),e.jsxs(h,{onClick:()=>{navigator.clipboard.writeText(R),s.success("Link copied to clipboard")},children:[e.jsx(te,{className:"h-4 w-4 mr-2"}),"Copy Link"]})]})]})})]})};export{Ke as default};
